# StreamPuller

## 项目介绍

StreamPuller 是一个用于拉流和配置管理的系统，主要特点和框架如下：

### 整体架构

   1. Docker 容器化部署：
      * 基于 Alpine Linux 的轻量级容器
      * 使用 docker-compose 进行多容器编排
      * 支持集群部署和管理
   2. 多模式运行
   3. 配置管理系统：
      * 使用 dufs 作为配置文件共享服务器
      * 支持远程和本地配置同步
      * 任务包生成和部署机制

### 核心组件

   1. AList 网盘集成：
      * 支持中国移动云盘等网盘服务
      * 提取网盘下载直链
   2. Rex 集群管理：
      * 基于 Perl 的远程执行框架
      * 支持批量部署和管理
      * 定时任务调度
   3. CDN 处理系统：
      * CDN 域名和 IP 映射管理
      * 网络探测和测速功能
      * GeoIP 信息收集和管理
   4. 监控系统：
      * 支持 Prometheus 监控
      * 日志收集（Filebeat）

### 技术栈

   1. 编程语言：
      * Bash 脚本（主要运行逻辑）
      * Perl 模块（复杂功能和数据处理）
   2. 数据存储：
      * SQLite 数据库
      * JSON/YAML 配置文件
   3. 网络工具：
      * curl、wget 等网络工具
      * httpx 探测工具

### 特点

   1. 灵活的配置系统：
      * 支持多种配置源和同步方式
      * 任务条件筛选机制
   2. 分布式架构：
      * 支持多节点部署
      * 中心化配置管理
   3. 多功能性：
      * 网盘内容提取
      * 文件下载
      * 网络测速
      * 配置同步
   4. 自动化运维：
      * 支持定时任务
      * 集群批量操作
      * 监控和日志收集

## 注意

如果需要在 macOS 中直接运行，需要安装 `curl-impersonate`，详见 [GitHub Release 页面](https://github.com/lwthiker/curl-impersonate/releases)。建议直接下载预编译版本，然后安装运行库：

```shell
brew install -y nss ca-certificates openssl@3
```

详细安装步骤可以参考 [Notion 笔记](https://www.notion.so/lqbn/macOS-curl-impersonate-21cbdd9693bc8085b236fb149bbbbe18?source=copy_link)。

除此之外，还需要安装以下软件：

```shell
brew install -y bash coreutils jq curl wget
```

Perl 需要安装以下库文件:

⚠️注意⚠️： macOS 需要先执行 `sudo xcodebuild -license` 同意许可协议，否则无法安装。

```shell
cpanm --force \
   DBI \
   DBD::SQLite \
   Hash::Ordered \
   IO::Socket::SSL::Utils \
   JSON \
   JSON::XS \
   LWP::Protocol::https \
   Net::IP \
   Net::SSLeay \
   NetAddr::IP \
   Rex \
   Spreadsheet::ParseXLSX \
   Text::ANSI::WideUtil \
   Text::ANSITable \
   Text::CSV \
   YAML::XS
```

推荐使用最新版本的 Bash 来代替系统自带的 zsh。

## 准备

StreamPuller 运行，需要准备如下组件：

1. **AList**: 详见 [AList 网盘下载平台搭建](./docs/AList/README.md)
2. **dufs**: 详见 [dufs 部署步骤](./docs/dufs/README.md)

## 部署

1. 构建 docker 镜像:

    ```shell
    docker compose build
    ```

2. 提交 docker 镜像:

   ```shell
   docker compose push
   ```

3. 运行:

   ```shell
   docker compose up -d
   ```

## 运行模式

1. **extract 模式**

   在此模式下，仅通过 AList API 提取网盘下载的直链 URL，生成配置文件，并将其保存到 DUFS 共享的远程目录中，并不会进行实际拉流操作。最小配置 docker-compose.yaml 如下：

   ```yaml
   services:
     stream-puller:
       platform: linux/amd64
       image: oa.gforce.cn:5543/library/stream-puller:1.0.6 environment:
         SP_MODE: "extract"
         ALIST_SERVER: "http://127.0.0.1:5166"
         ALIST_USERNAME: "admin"
         ALIST_PASSWORD: "AList@AUK"
         ALIST_PROVIDER: "139YUN"
         REMOTE_CONF_SERVER: "http://127.0.0.1:8500"
       command: /opt/sp_main.sh
       stdin_open: true
       tty: true
       restart: always
       network_mode: "host"
       deploy:
         replicas: 1
       logging:
         driver: "json-file"
         options:
           max-size: "10m"    # 限制每个日志文件为10MB
           max-file: "3"      # 保留最多3个日志文件
   ```

2. **sync 模式**

   此模式仅将远程 DUFS 共享的配置文件下载保存到本地网络 DUFS 共享目录中，以实现缓存效果，也不进行实际拉流操作。最小配置 docker-compose.yaml 如下：

   ```yaml
   services:
     sync:
       image: ak.gforce.cn:5543/library/stream-puller:1.2.6
       environment:
         SP_MODE: "sync"
         SP_DEBUG: true
         REMOTE_CONF_SERVER: "http://**************:8500"
         LOCAL_CONF_SERVER: "http://127.0.0.1:8500"
         ROUND_INTERVAL: "30"
       command: /opt/sp_main.sh
       stdin_open: true
       tty: true
       restart: always
       network_mode: "host"
       logging:
         driver: "json-file"
         options:
           max-size: "10m"    # 限制每个日志文件为10MB
           max-file: "3"      # 保留最多3个日志文件
       deploy:
         replicas: 1
   ```

3. **pan 模式**

   下载并同步远程共享配置中的 `sp_pan.json` 文件，读取其中的下载 URL，随机选取并拉流下载。

4. **file 模式**

   下载并同步远程共享配置中的 `sp_file.json` 文件，读取其中的下载 URL，随机选取并拉流下载。

5. **speed 模式**

   读取配置文件 `sp_speed.json`，读取其中的测速服务器 URL，随机选取并拉流下载。

## 集群管理

1. 查看 `rex` 指令

   ```shell
   cd tools/rex
   rex -T
   ```

2. 编辑配置文件 `servers.ini`
3. 准备远程 `docker-compose.yaml` 配置文件

   ```shell
   rex -g js sp_prepare
   ```

4. 准备远程 docker image

   ```shell
   rex -g js sp_img_pull
   ```

5. 开启拉流

   ```shell
   rex -g js sp_start
   ```

6. 结束拉流

   ```shell
   rex -g js sp_stop
   ```

7. 加入定时计划

   ```shell
   rex -g js sp_cron_add
   ```

## 集群监控

1. prometheus 部署及配置

## 参考

* AList V3 API
  * <https://www.horosama.com/archives/254>
* dufs API
  * <https://hub.docker.com/r/sigoden/dufs>
