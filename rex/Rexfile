use Rex -feature => ['1.4'];
use Rex -feature => ['no_tty'];                           # Disable pty usage for ssh connections
use Rex -feature => ['disable_strict_host_key_checking']; # Disabling strict host key checking for openssh connection mode
use Rex -feature => ['use_server_auth'];                  # Enable the usage of special authentication options for servers

use Rex::Commands;
use Rex::Group::Lookup::INI;
use Rex::Task;
use Rex::TaskList;

use Data::Dumper;
use JSON;
use File::Copy;
use Cwd 'abs_path';
use POSIX qw(strftime);

Rex::Config->set_use_template_ng();
Rex::Config->set_timeout(5);

#Rex::Config->set_openssh_opt( StrictHostKeyChecking => "no" );
#Rex::Config->use_server_auth( )

groups_file "servers.ini";

parallelism 100;

user 'root';
password 'greencache';

sayformat("[%D] %h ==> %s");

BEGIN {
    use Rex::Shared::Var;
    share qw(
        @wan_report 
        %cron_conf 
        @pppoe_avail_accounts 
        @pppoe_accounts_report
    );
};

desc 'Dump system information';
task "dumpinfo", group => "test", sub {
  say Dumper connection;
  say connection->server->option("pppoe.username");
  dump_system_information;
};

desc "Get up status";
task 'status', group => "test", sub {
  say run "uptime";
};

desc 'Set local route';
task 'set_route', group => "test", sub {
  # 删除所有本地静态路由
  say run "while uci -q delete network.\@route[0]; do :; done";

  # 获取 LAN 口 IP 地址
  my $net_info = network_interfaces();
  my $lan_dev = "br-lan";
  my $lan_ip = $net_info->{$lan_dev}->{"ip"};
  my $lan_gw;
  $lan_ip =~ /^(\d+\.\d+\.\d+)\.\d+$/ and $lan_gw = $1 . ".254" or return

  # 逐条添加回指路由
  say run "uci add network route";
  # 注意这里需要添加两次，否则第一条不生效
  say run "uci add network route";
  say run "uci set network.\@route[-1].interface='lan'";
  say run "uci set network.\@route[-1].target='10.0.0.0'";
  say run "uci set network.\@route[-1].netmask='*********'";
  say run "uci set network.\@route[-1].gateway='" . $lan_gw . "'";
  say run "uci commit network";
  say run "uci add network route";
  say run "uci set network.\@route[-1].interface='lan'";
  say run "uci set network.\@route[-1].target='**********'";
  say run "uci set network.\@route[-1].netmask='***********'";
  say run "uci set network.\@route[-1].gateway='" . $lan_gw . "'";
  say run "uci commit network";
  say run "uci add network route";
  say run "uci set network.\@route[-1].interface='lan'";
  say run "uci set network.\@route[-1].target='***********'";
  say run "uci set network.\@route[-1].netmask='***********'";
  say run "uci set network.\@route[-1].gateway='" . $lan_gw . "'";
  say run "uci commit network";
};

desc 'Set docker';
task 'set_docker', group => "test", sub {
  say run "uci set dockerd.globals.data_root='/tmp/docker'";
  say run "uci -q delete dockerd.globals.hosts";
  say run "uci add_list dockerd.globals.hosts='unix:///var/run/docker.sock'";
  say run "uci commit";
  say run "service dockerd restart";
};

sub set_pppoe {
  my ($username, $password) = @_;

  say run "ifdown wan";
  say run "uci -q delete network.wan";
  say run "uci set network.wan=interface";
  say run "uci set network.wan.device='eth1'";
  say run "uci set network.wan.proto=pppoe";
  say run "uci set network.wan.username='" . $username . "'";
  say run "uci set network.wan.password='" . $password . "'";
  say run "uci commit network";
  say run "ifup wan";
  say "PPPoE wan is set up, username is $username, password is $password";
}

desc 'Set pppoe username and password';
task 'set_pppoe', group => "test", sub {
  my $username = connection->server->option("pppoe.username");
  my $password = connection->server->option("pppoe.password");

  set_pppoe($username, $password);
};

desc 'Get pppoe interface status and IP address';
task 'get_pppoe', group => "test", sub {
  my $net_info = network_interfaces();
  my $wan_dev = "pppoe-wan";
  if (exists($net_info->{$wan_dev})) {
    say "PPPoE WAN has the IP: " . $net_info->{$wan_dev}->{"ip"};
  } else {
    say "PPPoE WAN is down";
  }
};

desc 'Set WAN online';
task 'online', group => "test", sub {
  say run "ifup wan && service dnsmasq stop && sleep 2 && service dnsmasq start";
};

desc 'Set WAN offline';
task 'offline', group => "test", sub {
  say run "ifdown wan";
};

desc 'Get WAN IPv4 address';
task 'get_wan', group => "test", sub {
  my $ipv4 = run 'wget -q -T 10 -4 -O - http://ak.gforce.cn:9080/ip';
  if ( $ipv4 =~ /^(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/ ) {
      say "WAN4: $ipv4";
  } else {
    my $html_content = $ipv4;
    my $title;
    my $msg;
    if ($html_content =~ /<title>(.*?)<\/title>/s) {
      $title = $1;
    } 
    if ($html_content =~ /<div class="outlineT">(.*?)<\/div>/s) {
      $msg = $1;
    }
    if ($msg =~ /(.*?)<br/s) {
      $msg = $1;
    }
    say "WAN4: ${title} $msg";
  }
  return $ipv4;
};

desc 'Get WAN IPv6 address';
task 'get_wan6', group => "test", sub {
  my $ipv6 = run 'wget -q -T 10 -6 -O - http://ak.gforce.cn:9080/ip';
  say "WAN6: $ipv6";
  return $ipv6;
};

desc 'WAN Report';
task 'wan_report', group => "test", sub {
  my $report = {};

  # 获取主机名
  $report->{"hostname"} = run 'uname -n';

  # 获取 PPPoE 账号和密码
  $report->{"username"} = run 'uci get network.wan.username';
  $report->{"password"} = run 'uci get network.wan.password';

  # 获取接口信息
  my $net_info = network_interfaces();

  # 获取接口 MAC 地址
  $report->{"mac"} = $net_info->{"eth1"}->{"mac"};

  # 获取接口 IP
  my $wan_dev = "pppoe-wan";
  my $lan_dev = "br-lan";
  if (exists($net_info->{$wan_dev})) {
    $report->{"wan_ip"} = $net_info->{$wan_dev}->{"ip"};
  } else {
    $report->{"wan_ip"} = "PPPoE WAN is down";
  }
  $report->{"lan_ip"} = $net_info->{$lan_dev}->{"ip"};

  # 获取 WAN 口 访问 IPv4 网站的 IP
  my $ipv4 = run 'wget -q -T 10 -4 -O - http://ak.gforce.cn:9080/ip';
  if ( $ipv4 !~ /^(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/ ) {
    my $html_content = $ipv4;
    my $title;
    my $msg;
    if ($html_content =~ /<title>(.*?)<\/title>/s) {
      $title = $1;
    } 
    if ($html_content =~ /<div class="outlineT">(.*?)<\/div>/s) {
      $msg = $1;
    }
    if ($msg =~ /(.*?)<br/s) {
      $msg = $1;
    }
    # 无法正常访问外网，返回错误页面中的提示
    $ipv4 = "${title} $msg";
  }
  $report->{"ipv4"} = $ipv4;

  # 获取 WAN 口 访问 IPv6 网站的 IP
  ; $report->{"ipv6"} = run 'wget -q -T 10 -6 -O - http://ak.gforce.cn:9080/ip';
  # 获取 WAN 口 (pppoe-wan) 访问 IPv6 网站的 IP
  $report->{"ipv6"} = run 'ifconfig pppoe-wan | grep -Eo "(2408:|2409:|240e:)([0-9a-fA-F]{1,4}:){0,7}([0-9a-fA-F]{1,4}|:)"';
  # 获取 IPv6 前缀委派信息
  my $ipv6_pd_info_json = run 'ifstatus lan | jsonfilter -e \'@["ipv6-prefix-assignment"]\'';
  my $ipv6_pd_info = decode_json($ipv6_pd_info_json);
  foreach my $entry (@$ipv6_pd_info) {
    if ($entry->{"address"} =~ /^(2408:|2409:|240e:)/) {
      # 获取 IPv6 前缀委派地址
      $report->{"ipv6_pd_addr"} = $entry->{"address"};
      # 获取 IPv6 前缀委派长度
      $report->{"ipv6_pd_mask"} = $entry->{"mask"};
    }
  }

  defined $report->{"ipv6_pd_addr"} or $report->{"ipv6_pd_addr"} = "";
  defined $report->{"ipv6_pd_mask"} or $report->{"ipv6_pd_mask"} = "";

  # 获取 IPv6 委派 IP 数目
  $report->{"ipv6_pd_count"} = run 'ifconfig | grep -Eo "(2408:|2409:|240e:)([0-9a-fA-F]{1,4}:){0,7}([0-9a-fA-F]{1,4}|:)" | wc -l';

  push(@wan_report, $report);

  # 创建一个 JSON 对象，并禁用美化输出
  my $json = JSON->new->canonical(1);

  # 将数组转换为 JSON 字符串
  my $json_text = $json->encode($report);

  # 打印 JSON 字符串
  print ${json_text}, "\n";
};

after_task_finished 'wan_report', sub {
  # print Dumper(\@wan_report);

  # 打印报表提示
  # 获取当前日期和时间
  my $date_time = strftime("%Y%m%d%H%M%S", localtime);
  print "----------------\n";
  print " WAN Report [$date_time]\n";
  print "----------------\n";

  # 创建一个 JSON 对象，并禁用美化输出
  my $json = JSON->new->canonical(1);

  # 将数组转换为 JSON 字符串
  my $json_text = $json->encode(\@wan_report);

  # 打印 CSV 表头
  print '"主机名","账号","密码","LAN IP","MAC地址","WAN IP","公网IPv4","WAN IPv6","IPv6前缀委派","IPv6地址总数"', "\n";

  # 使用 open 函数创建管道，并调用 jq 和 sort 命令
  open my $jq, '|-', 'jq -r "(.[] | [.hostname,.username, .password, .lan_ip, .mac, .wan_ip, .ipv4, .ipv6, (.ipv6_pd_addr + \"/\" + (.ipv6_pd_mask | tostring)), .ipv6_pd_count]) | @csv" | sort -t, -k1,1 -V' or die "Cannot open jq: $!";

  # 将 JSON 数据传递给 jq
  print $jq $json_text;

  # 关闭管道
  close $jq;
};

desc 'Test PPPoE accounts';
task 'test_pppoe_accounts', group => "test", sub {
  # 获取当前配置的账号密码
  my $pppoe_username = run 'uci get network.wan.username';
  my $pppoe_password = run 'uci get network.wan.password';
  # 尝试获取公网 IPv4 地址, 测试当前是否可以访问公网
  my $public_ipv4 = run_task "get_wan", on => connection->server;
  # 如果可以正常访问公网, 则直接返回
  if ($public_ipv4 =~ /^(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/ ) {
    say "Success! PPPoE username: $pppoe_username, password: $pppoe_password";
    return;
  }

  my $success=0;

  # 不断重试, 直到账号池中没有可用账号为止
  while (not $success) {
    # 默认密码池
    my @pppoe_default_passwords=qw(123321 789789 681858 147258);
    # 定义变量, 存放用于测试的账号和密码
    my ($username, $password);
    # 从账号池获取一个新账号
    if (scalar @pppoe_avail_accounts > 0) {
      $username = shift @pppoe_avail_accounts;
    } else {
      # 账号池中没有可用账号, 退出循环
      say "There are no new accounts available in the account pool.";
      last;
    }
    # 不断重试, 直到密码池中没有可用密码为止
    while (not $success) {
      if (scalar @pppoe_default_passwords > 0) {
        # 更换密码, 重新尝试
        $password = shift @pppoe_default_passwords;
        # 重新拨号
        set_pppoe($username, $password);
        # 等待 10 秒
        sleep 10;
      } else {
        # 如果已经尝试完所有密码仍然不成功, 则结束循环, 准备切换账号
        last
      }

      # 尝试获取公网 IPv4 地址, 测试是否可以访问公网
      $public_ipv4 = run_task "get_wan", on => connection->server;
      # 如果可以正常访问公网, 则退出循环, 结束测试
      if ($public_ipv4 =~ /^(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/ ) {
        say "Success! PPPoE username: $username, password: $password";
        $success = 1;
        last;
      }
    }     

    # 记录测试时间
    my $date_time = strftime("%Y-%m-%d %H:%M:%S", localtime);
    my $report = {};
    # 记录测试结果
    $report->{"username"} = $username;
    $report->{"time"} = $date_time;
    if (not $success) {
      $report->{"status"} = "failed";
    } else {
      $report->{"status"} = "success";
    }
    # 存入共享数组
    push @pppoe_accounts_report, $report;

    my $output = sprintf("%3d accounts tested, %3d accounts available", scalar @pppoe_accounts_report,  scalar @pppoe_avail_accounts);
    say $output;
  }
};

before_task_start 'test_pppoe_accounts', sub {
  # 从配置文件中读取可用账号
  open(my $fh, '<', 'pppoe.txt') or die "Can't open file pppoe.txt: $!";
  # 逐行读取文件内容
  while (my $line = <$fh>) {
    chomp $line;
    push @pppoe_avail_accounts, $line;
  }
  close $fh;

  # 调试信息
  # print Dumper(\@pppoe_avail_accounts), "\n";
};

after_task_finished 'test_pppoe_accounts', sub {
  # print Dumper(\@pppoe_accounts_report);

  # 打印报表提示
  # 获取当前日期和时间
  my $date_time = strftime("%Y%m%d%H%M%S", localtime);
  print "----------------------------------------\n";
  print " PPPoE Accounts Report [$date_time]\n";
  print "----------------------------------------\n";

  # 创建一个 JSON 对象，并禁用美化输出
  my $json = JSON->new->canonical(1);

  # 将数组转换为 JSON 字符串
  my $json_text = $json->encode(\@pppoe_accounts_report);

  # 打印 CSV 表头
  print '"账号","状态","测试时间"', "\n";

  # 使用 open 函数创建管道，并调用 jq 和 sort 命令
  open my $jq, '|-', 'jq -r "(.[] | [.username, .status, .time]) | @csv" | sort -t, -k3,3 -V' or die "Cannot open jq: $!";

  # 将 JSON 数据传递给 jq
  print $jq $json_text;

  # 关闭管道
  close $jq;
};

desc 'Set hostname based on LAN IP';
task 'set_hostname', group => "test", sub {
  my ($lanIP, $suffix, $hostname);
  if (connection->server =~ /\d+\.\d+\.\d+\.(\d+)/) {
    $lanIP = connection->server;
  } else {
    $lanIP = run 'uci get network.lan.ipaddr';
  }
  
  # 主机名前缀为所属 group，后缀为 LAN IPv4 地址的最后一段
  $suffix = $lanIP =~ /(\d+)$/ ?  $1 : 'NA';
  $hostname = connection->server->{__group__}->[-1] . "-worker-" . $suffix;
  
  run "uci set system.\@system[0].hostname='" . $hostname . "'";
  run "uci commit system";
  run "/etc/init.d/system reload";
  
  say "hostname is set to ${hostname}" ;
};

desc "Reboot host";
task 'reboot', group => 'test', sub {
  say run "ifdown wan";
  say run "reboot";
  say "OK";
};

desc "Prepare docker compose config";
task 'sp_prepare', group => 'test', sub {  
  # # 将模板复制到远程主机, 并替换相关变量
  # file '/root/docker/stream-puller/docker-compose.yaml',
  #   ensure => "absent";

  # file '/root/docker/stream-puller/docker-compose.yaml',
  #   owner => "root",
  #   group => "root",
  #   mode  => "644",
  #   content => template(
  #     './docker-compose.tpl',
  #     conf => {
  #       v4_replicas => connection->server->option("v4_replicas"),
  #       v6_replicas => connection->server->option("v6_replicas"),
  #       local_conf_server => connection->server->option("local_conf_server"),
  #     }
  #   ),
  #   on_change => sub { say "docker compose config is updated" },
  #   on_no_change => sub { say "nothing has changed"};

  my $task_dir = "/root/docker/stream-puller";

  my $lanIP;
  if (connection->server =~ /\d+\.\d+\.\d+\.(\d+)/) {
    $lanIP = connection->server;
  } else {
    $lanIP = run 'uci get network.lan.ipaddr';
  }

  my $compose_file = $lanIP . ".yaml";
  run "wget -O /tmp/task-Compose.zip http://**************:8500/tasks/task-Compose.zip";
  run "mkdir -p $task_dir && unzip /tmp/task-Compose.zip $compose_file -d $task_dir && rm -f /tmp/task-Compose.zip";
  run "mv $task_dir/$compose_file $task_dir/docker-compose.yaml";
  
  say "docker compose config is updated";
};

# before_task_start 'sp_prepare', sub {
#   my $conf = {
#     source    => "../docker-compose.yaml",
#     template  => "docker-compose.tpl",
#   };
  
#   $conf->{'source'}   = abs_path($conf->{'source'});
#   $conf->{'template'} = abs_path($conf->{'template'});
  
#   unlink( $conf->{'template'} ) or die "can't delete file $conf->{'template'}";
  
#   ( -e $conf->{'template'} ) and die "file $conf->{'template'} not deleted";
  
#   # 读取原配置文件内容
#   open my $fh, '<', $conf->{'source'} or die "Can't open source config file: " . $conf->{'source'};
#   my $yaml_text = do { local $/; <$fh> };
#   close $fh;

#   use YAML::XS;
  
#   # 读取YAML内容
#   my $yaml = Load($yaml_text);

#   # print "original yaml: $conf->{'source'}\n", $yaml_text;
#   # print Dumper($yaml);
  
#   # ⚠️注意⚠️
#   # 不要直接修改 docker-compose.tpl 文件
#   # 每次都会自动根据 docker-compose.yaml 文件生成  
#   # 修改指定字段
#   $yaml->{services}{x_downloader_template}{environment}{LOCAL_CONF_SERVER} = 
#     '<%= exists $conf->{"local_conf_server"} ? $conf->{"local_conf_server"} : "http://192.168.125.252:8500,http://192.168.126.252:8500,http://192.168.122.252:8500" %>';
  
#   $yaml->{services}{downloader_v4}{deploy}{replicas} = 
#     '<%= exists $conf->{"v4_replicas"} ? $conf->{"v4_replicas"} : "8" %>';
  
#   $yaml->{services}{downloader_v6}{deploy}{replicas} = 
#     '<%= exists $conf->{"v6_replicas"} ? $conf->{"v6_replicas"} : "8" %>';
  
#   # 转换回YAML文本
#   my $yaml_text = Dump($yaml);

#   # 将修改后的内容写回
#   open $fh, '>', $conf->{'template'} or die "Can't write to template: " . $conf->{'template'};
#   print $fh $yaml_text;
#   close $fh;
# };

desc "Delete docker compose config";
task "sp_delete", group => "test", sub {
  say run "rm -rf /root/docker";
  say "OK";
};

desc 'Start stream puller dockers';
task "sp_start", group => "test", sub {
  say run 'service dockerd start && sleep 2 && cd /root/docker/stream-puller && docker compose up -d';
  say "OK";
};

desc 'Stop stream puller dockers';
task "sp_stop", group => "test", sub {
  say run 'cd /root/docker/stream-puller && docker compose down && rm -rf /tmp/sp_state >/dev/null 2>&1';
  say "OK";
};

desc 'Stop and start stream puller dockers';
task "sp_restart", group => "test", sub {
  say run 'cd /root/docker/stream-puller && docker compose down && sleep 2 && docker compose up -d';
  say "OK";
};

desc 'Scale docker replicas count';
task "sp_scale", group => "test", sub {
  say run "cd /root/docker/stream-puller && docker compose up -d";
  say "OK";
};

desc 'Show stream puller dockers status';
task "sp_status", group => "test", sub {
  my $num = run 'cd /root/docker/stream-puller && docker compose ps | awk \'NR>1&&$1~/^stream-puller/ {print $1}\' | wc -l';
  
  $num = $num =~ /\d+/ ? $num : 0;
  
  if ( $num>0 ) {
    say "running with $num workers"
  } else {
    say "not running"
  }
};

desc 'Pull stream puller docker image';
task "sp_img_pull", group => "test", sub {
  say run 'service dockerd start';
  say run 'cd /root/docker/stream-puller && docker compose pull';
  say run 'docker images | grep -q stream-puller && echo "OK" || echo "Failed!!!"';
};

desc 'Prune stream puller docker images and containers';
task "sp_img_prune", group => "test", sub {
  run 'docker system prune -a -f --volumes';
  say "OK";
};

desc 'Show stream puller docker image status';
task 'sp_img_status', group => "test", sub {
  say run 'docker images | grep -q stream-puller && echo "OK" || echo "NOT FOUND!!!"';
};

desc 'Set scheduled tasks according to the predefined histogram';
task 'sp_cron_set', group => "test", sub {
  my @cron_start_array = @{$cron_conf{connection->server}->{start}};
  my @cron_stop_array  = @{$cron_conf{connection->server}->{stop}};

  # clear current cron config
  say run '/usr/bin/crontab -r';

  my ($hour, $minute);
  my $i=1;
  foreach my $timestamp (@cron_start_array) {
    if ($timestamp =~ /^(\d{2}):(\d{2})/) {
      ($hour, $minute) = ($1, $2);

      cron_entry "start-stream-puller-docker-$i",
        ensure  => "present",
        user    => "root",
        command => "cd /root/docker/stream-puller && docker-compose up -d",
        minute  => $minute,
        hour    => $hour,
        on_change => sub {say "cron job start-stream-puller-docker-$i added"; };
        $i++;
    } else {
      say "invalid time: $timestamp";
      continue
    }
  }

  $i=1;
  foreach my $timestamp (@cron_stop_array) {
    if ($timestamp =~ /^(\d{2}):(\d{2})/) {
      ($hour, $minute) = ($1, $2);

      cron_entry "stop-stream-puller-docker-$i",
        ensure  => "present",
        user    => "root",
        command => "cd /root/docker/stream-puller && docker-compose down && /etc/init.d/network restart && rm -rf /tmp/sp_state >/dev/null",
        minute  => $minute,
        hour    => $hour,
        on_change => sub {say "cron job stop-stream-puller-docker-$i added"; };
        $i++;
    } else {
      say "invalid time: $timestamp";
      continue
    }
  }
  
  say run '/etc/init.d/cron enable && /etc/init.d/cron start';
  say 'cron service is enabled and running'
};

before_task_start 'sp_cron_set', sub {
  # 读取当前任务所涉及的所有 server，并将其存入 %cron_conf 关联数组
  my $task = Rex::TaskList->create->get_task('sp_cron_set');
  my @stopped_array = map { $_->{name} } @{$task->server};
  my @started_array = ();

  # 可调用节点总数
  my $total_nodes_num = scalar @stopped_array;

  my %conf = ();

  # 调试信息
  printf "%-6s\t%3s\t%3s\t%3s\t%3s\t%3s\n", "t","v","st","sp","n","d";
  printf "%-6s\t%3s\t%3s\t%3s\t%3s\t%3s\n", "------","---","---","---","---","---";

  # 读取 histogram 配置文件
  open(my $fh, '<', 'histogram.txt') or die "Can't open file histogram.txt: $!";
  # 逐行读取文件内容
  while (my $line = <$fh>) {
    chomp $line;

    # 判断行首是否有时间码 (格式: HH:MM)
    if ($line =~ /^(\d{2}:\d{2})\|(.*)/) {
      my $t = $1;                           # 时间戳
      my $v = length $2;                    # 当前开启节点数量占全部节点数量的百分比
      my $n = int($v*$total_nodes_num/100); # 当前需要开启的节点数量
      my $d = $n - scalar @started_array;   # 当前需要增减的节点数量

      if ($d > 0) {
        for (my $i=0; $i<$d; $i++) {
            my $node_name = shift @stopped_array;
            push @{$conf{$node_name}->{start}}, $t;
            push @started_array, $node_name;
        }
      }

      if ($d < 0) {
        for (my $i=0; $i<$d*-1; $i++) {
            my $node_name = shift @started_array;
            push @{$conf{$node_name}->{stop}}, $t;
            push @stopped_array, $node_name;
        }
      }

      # 调试信息
      printf "%-6s\t%3d\t%3d\t%3d\t%3d\t%3d\n", $t, $v, scalar @started_array, scalar @stopped_array, $n, $d;
    }
  }
  # 关闭文件
  close $fh;

  # 临时配置保存为全局配置
  %cron_conf = %conf;

  # 调试信息
  # print "cron_conf:\n=====\n", Dumper \%cron_conf, "\n=====\n";
  # print "started_array:\n=====\n", Dumper \@started_array, "\n=====\n";
  # print "stopped_array:\n=====\n", Dumper \@stopped_array, "\n=====\n";
};

desc 'Delete scheduled tasks';
task 'sp_cron_del', group => "test", sub {
  # clear current cron config
  say run '/usr/bin/crontab -r';
  say "OK";
};

desc "Prepare and initialize running environment";
batch "deploy", "set_route", "set_docker", "set_hostname", "set_pppoe", "sp_prepare";

desc "Update openwrt systemes";
task 'node_update', group => 'test', sub {
  run 'opkg update && opkg install prometheus-node-exporter-lua-textfile';

  file '/usr/bin/prometheus-textfile-collectors.sh',
    owner => "root",
    group => "root",
    mode  => "755",
    source => "files/prometheus-textfile-collectors.sh";
  
  file '/etc/init.d/prometheus-textfile-collectors',
    owner => "root",
    group => "root",
    mode  => "755",
    source => "files/prometheus-textfile-collectors";
  
  run 'service prometheus-textfile-collectors enable';
  run 'service prometheus-textfile-collectors start';
  run '/etc/init.d/prometheus-node-exporter-lua restart';

  run 'uci set network.lan.ip6ifaceid=\'random\'';
  run 'uci set network.lan.ip6assign=\'60\'';
  run 'uci commit network';
  run 'uci set dhcp.lan.ignore=1';
  run 'uci set dhcp.lan.dhcpv4=disabled';
  run 'uci set dhcp.lan.dhcpv6=disabled';
  run 'uci set dhcp.lan.ra=disabled';
  run 'uci commit dhcp';
  run 'service dnsmasq restart';
  run '/etc/init.d/odhcpd stop';
  run '/etc/init.d/odhcpd disable';

  file '/etc/hotplug.d/iface/60-lan-ipv6addr',
    owner => "root",
    group => "root",
    mode  => "644",
    source => "files/60-lan-ipv6addr-dynamic.sh";

  run '/etc/init.d/network restart';
  say "OK";
};

desc "Update ipv6 scripts for lan interface";
task "update_ipv6_scripts", group => "test", sub {
    run 'uci set network.lan.ip6ifaceid=\'random\'';
    run 'uci set network.lan.ip6assign=\'60\'';
    run 'uci commit network';
    run 'uci set dhcp.lan.ignore=1';
    run 'uci set dhcp.lan.dhcpv4=disabled';
    run 'uci set dhcp.lan.dhcpv6=disabled';
    run 'uci set dhcp.lan.ra=disabled';
    run 'uci commit dhcp';
    run 'service dnsmasq restart';
    run '/etc/init.d/odhcpd stop';
    run '/etc/init.d/odhcpd disable';

    file '/etc/hotplug.d/iface/60-lan-ipv6addr',
      owner => "root",
      group => "root",
      mode  => "644",
      source => "files/60-lan-ipv6addr-dynamic.sh";

    run '/etc/init.d/network restart';
    say "OK";
};
