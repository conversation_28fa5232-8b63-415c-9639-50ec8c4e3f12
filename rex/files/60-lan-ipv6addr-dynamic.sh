#!/usr/bin/env sh

# 创建临时目录用于存放脚本和PID文件
TEMP_DIR="/tmp/lan_ipv6addr"
WORKER_SCRIPT="${TEMP_DIR}/ipv6addr_worker.sh"
WORKER_PID_FILE="${TEMP_DIR}/ipv6addr_worker.pid"

# 确保临时目录存在
mkdir -p "$TEMP_DIR"

# 创建后台工作进程脚本
create_worker_script() {
  cat > "$WORKER_SCRIPT" << 'EOF'
#!/usr/bin/env sh

# 地址有效期 180 秒
ipv6_valid_lft=180
# 运行间隔时间
interval_time=30
# 设定每个前缀对应的后缀组数, 默认为 1, 即 16 个前缀生成 16 个地址
num_suffixes_per_prefix=4

add_ipv6_addresses() {
  json_string=$(/sbin/ifstatus lan)

  # 使用 jsonfilter 命令解析 ipv6-prefix-assignment 中的 GUA 地址以及对应的 mask
  ipv6_data=$(echo "$json_string" | jsonfilter -e '@["ipv6-prefix-assignment"][*]' | while read -r line; do
    address=$(echo "$line" | jsonfilter -e '@.address')
    if echo "$address" | grep -qE "^240e:|^2408:|^2409:"; then
      mask=$(echo "$line" | jsonfilter -e '@.mask')
      echo "{\"address\": \"$address\", \"mask\": \"$mask\"}"
    fi
  done)
  # 提取前缀委派地址
  prefix_delegation=$(echo "$ipv6_data" | jsonfilter -e '@.address')
  # 提取前缀长度
  prefix_length=$(echo "$ipv6_data" | jsonfilter -e '@.mask')

  # 计算分配的前缀 ID 长度
  assigned_len=$((64 - prefix_length))
  # shellcheck disable=SC3019
  total_prefix_ids=$((2**assigned_len))

  # 初始化前缀 ID 数组
  prefix_ids=""
  # 计算十六进制数字的位数长度
  hex_length=$(( (total_prefix_ids + 1) / 16 ))
  i=0
  while [ $i -lt $total_prefix_ids ]; do
    prefix_ids="$prefix_ids $(printf "%0${hex_length}x" "$i")"
    i=$((i + 1))
  done

  # 分离 prefix_delegation 的第 4 段并转换成十六进制数字
  prefix_part1=$(echo "$prefix_delegation" | cut -d':' -f1)
  prefix_part2=$(echo "$prefix_delegation" | cut -d':' -f2)
  prefix_part3=$(echo "$prefix_delegation" | cut -d':' -f3)
  prefix_part4=$(echo "$prefix_delegation" | cut -d':' -f4)

  # 遍历 prefix_ids 并加上 prefix_delegation 的第四段, 得出新地址的前缀
  addr_prefixes=""
  for prefix_id in $prefix_ids; do
    # 前三段保持不变, 第四段和 prefix_id 数值相加
    new_prefix="${prefix_part1}:${prefix_part2}:${prefix_part3}:$(printf "%04x" "$((0x$prefix_part4 + 0x$prefix_id))")"
    addr_prefixes="$addr_prefixes $new_prefix"
  done

  # 计算新地址后缀
  addr_suffixes=""
  
  for addr_prefix in $addr_prefixes; do
    j=0
    while [ $j -lt $num_suffixes_per_prefix ]; do
      suffix_parts=""
      i=0
      while [ "$i" -lt 4 ]; do
        # 生成一个 0 到 65535 之间的随机数
        # shellcheck disable=SC3028
        random_num="$((RANDOM % 65536))"
        # 将随机数转换为 4 位十六进制数
        suffix_parts="$suffix_parts $(printf "%04x" "$random_num")"
        i=$((i + 1))
      done
      suffix=$(echo "$suffix_parts" | tr ' ' ':')
      addr_suffixes="$addr_suffixes $suffix"
      j=$((j + 1))
    done
  done

  echo "addr_prefixes: $addr_prefixes"
  echo "addr_suffixes: $addr_suffixes"

  # 针对每个 addr_prefix，分配 num_suffixes_per_prefix 个 addr_suffix
  ipv6_addresses=""
  prefix_index=0
  suffix_index=1
  for addr_prefix in $addr_prefixes; do
    j=0
    while [ $j -lt $num_suffixes_per_prefix ]; do
      # 取出第 suffix_index 个 addr_suffix
      addr_suffix=$(echo "$addr_suffixes" | cut -d' ' -f $((suffix_index + 1)))
      ipv6_address="${addr_prefix}${addr_suffix}"
      ipv6_addresses="$ipv6_addresses $ipv6_address"
      suffix_index=$((suffix_index + 1))
      j=$((j + 1))
    done
    prefix_index=$((prefix_index + 1))
  done

  # 将生成的 IPv6 地址配置到 br-lan 接口
  for ipv6 in $ipv6_addresses; do
    logger -t lan-ipv6addr "Adding GUA address: $ipv6 to br-lan"
    ip -6 addr add "$ipv6/64" dev br-lan valid_lft $ipv6_valid_lft preferred_lft $ipv6_valid_lft
  done
}

del_ipv6_addresses() {
  # 获取 br-lan 接口上所有的 GUA 地址
  gua_addresses=$(ip -6 addr show dev br-lan | grep "scope global" | awk '{print $2}' | grep -E "^240e:|^2408:|^2409:")

  # 遍历所有 GUA 地址并删除
  for gua in $gua_addresses; do
    logger -t lan-ipv6addr "Deleting GUA address: $gua from br-lan"
    ip -6 addr del "$gua" dev br-lan >/dev/null 2>&1
    sleep 1
  done
}
# 循环执行地址添加操作
while true; do
  add_ipv6_addresses
  sleep $interval_time
done
EOF
  chmod +x "$WORKER_SCRIPT"
}

kill_all_instances() {
  # 清理工作进程
  if [ -f "$WORKER_PID_FILE" ]; then
    worker_pid=$(cat "$WORKER_PID_FILE" 2>/dev/null)
    if [ -n "$worker_pid" ] && echo "$worker_pid" | grep -q '^[0-9]\+$'; then
      if kill -0 "$worker_pid" 2>/dev/null; then
        logger -t lan-ipv6addr "Killing existing worker with PID: $worker_pid"
        kill "$worker_pid" 2>/dev/null
        sleep 2
        if kill -0 "$worker_pid" 2>/dev/null; then
          kill -9 "$worker_pid" 2>/dev/null
        fi
      fi
    fi
  fi

  # 清理所有临时文件
  rm -rf "$TEMP_DIR"
  mkdir -p "$TEMP_DIR"
}

# 仅在 wan 接口上执行
if [ "$ACTION" = "ifup" ] && [ "$INTERFACE" = "wan_6" ]; then
  # 杀死其他实例并清理临时文件
  kill_all_instances
  # 等待 5 秒, 确保 WAN 口 UP
  sleep 5
  logger -t lan-ipv6addr "Interface: ${INTERFACE}(${DEVICE}) / Action: ${ACTION}"
  # 删除现有 IPv6 地址
  del_ipv6_addresses
  # 等待 1 秒, 确保地址删除完成
  sleep 1
  
  # 创建工作进程所需文件
  create_worker_script
  
  # 启动工作进程
  "$WORKER_SCRIPT" >/dev/null 2>&1 &
  worker_pid=$!
  echo "$worker_pid" > "$WORKER_PID_FILE"
  logger -t lan-ipv6addr "Started worker process with PID: $worker_pid"
fi

if [ "$ACTION" = "ifdown" ] && [ "$INTERFACE" = "wan_6" ]; then
  # 杀死所有实例并清理临时文件
  kill_all_instances
  # 等待 1 秒, 确保 WAN 口 DOWN
  sleep 1
  logger -t lan-ipv6addr "Interface: ${INTERFACE}(${DEVICE}) / Action: ${ACTION}"
  # 删除现有 IPv6 地址
  del_ipv6_addresses
fi
