#!/usr/bin/env bash

sp_srvconf_gen() {
  # 提示用户输入
  echo "请输入每行的内容，格式为：ip username password"
  echo "输入完成后按 Ctrl+D 结束输入"

  declare -a input_lines

  # 初始化数组
  input_lines=()

  # 逐行读取用户输入并存入数组
  while read -r line; do
    input_lines+=("$line")
  done

  echo "============"

  # 逐一读取数组并解析每行内容
  for line in "${input_lines[@]}"; do
    # 使用 read 命令解析每行的字段内容
    read -r ip username password <<<"$line"

    # 打印解析后的变量值
    printf "%-15s pppoe.username=%-22s pppoe.password=%-6s\n" "$ip" "$username" "$password"
  done
}

sf_srvconf_fmt() {
  # 提示用户输入
  echo "请输入每行的内容，格式为：*************** pppoe.username=VJTsz51241218509aaat pppoe.password=789789"
  echo "输入完成后按 Ctrl+D 结束输入"

  declare -a input_lines

  # 初始化数组
  input_lines=()

  # 逐行读取用户输入并存入数组
  while read -r line; do
    input_lines+=("$line")
  done

  echo "============"

  # 逐一读取数组并解析每行内容
  for line in "${input_lines[@]}"; do
    # 解析每行的字段内容
    ip=$(echo "$line" | awk '{print $1}')
    username=$(echo "$line" | awk '{print $2}' | cut -d'=' -f2)
    password=$(echo "$line" | awk '{print $3}' | cut -d'=' -f2)

    # 打印解析后的变量值
    printf "%-15s pppoe.username=%-22s pppoe.password=%-6s %-s\n" "$ip" "$username" "$password"
  done
}

# 判断参数个数
if [ $# -eq 0 ]; then
  echo "Usage: $0 (gen|fmt)  ..."
  echo "   gen: generate server.ini configs"
  echo "   fmt: convert provided server.ini configs"
  exit 1
fi

mode=$1

case "${mode}" in
"gen")
  sp_srvconf_gen
  ;;
"fmt")
  sf_srvconf_fmt
  ;;
*)
  echo "Usage: $0 (gen|fmt)"
  echo "   gen: generate server.ini configs"
  echo "   fmt: convert provided server.ini configs"
  exit 1
  ;;
esac
