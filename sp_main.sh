#!/usr/bin/env bash
# shellcheck source=/dev/null
# 公共变量及函数定义
source sp_common.sh

# 将逗号设置为字段分隔符
IFS=','

# 将模式变量字符串分割后复制给模式数组
read -ra modes <<< "$SP_MODE"

# 逐个读取运行模式
for mode in "${modes[@]}"; do
  case "$mode" in
    # 同步配置
    "sync")
      ./sp_sync.sh
    ;;
    # 提取网盘地址
    "extract")
      ./sp_extract.sh
    ;;
    # 网盘下载
    "pan")
      ./sp_file.sh "pan"
    ;;
    # 测速下载
    "speed")
      ./sp_speed.sh
    ;;
    # 文件下载
    "file")
      ./sp_file.sh "file"
    ;;
    *)
      echo "Unsupported mode: $SP_MODE"
      exit 1
    ;;
  esac
done