# 多阶段构建：从 curl-impersonate 镜像获取工具
FROM lwthiker/curl-impersonate:0.6.1-ff-alpine AS curl-impersonate-firefox
FROM lwthiker/curl-impersonate:0.6.1-chrome-alpine AS curl-impersonate-chrome

# 使用 alpine linux 作为基础镜像
FROM alpine:latest

# 设置工作目录
WORKDIR /opt

# 替换apk镜像源为清华大学源，然后安装必要的软件包
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories \
    && apk update \
    && apk --no-cache add bash jq unzip \
    && rm -rf /var/cache/apk/* /tmp/*

# 从 curl-impersonate 镜像拷贝 curl-impersonate-ff
COPY --from=curl-impersonate-firefox /usr/local/bin/curl-impersonate-ff /usr/local/bin/curl-impersonate-ff
COPY --from=curl-impersonate-chrome /usr/local/bin/curl-impersonate-chrome /usr/local/bin/curl-impersonate-chrome

# 创建符号链接，将 curl-impersonate-ff 映射为 curl 命令
RUN ln -s /usr/local/bin/curl-impersonate-chrome /usr/local/bin/curl

# 将 所有脚本 拷贝至 /opt 目录
COPY *.sh /opt/ 
COPY ./utils/*.sh /opt/utils/

# 将 所有必须的配置文件 拷贝至 /opt/configs 目录
COPY ./configs/sp_profiles.json /opt/configs/

# 设置 entrypoint
ENTRYPOINT ["/opt/sp_main.sh"]
