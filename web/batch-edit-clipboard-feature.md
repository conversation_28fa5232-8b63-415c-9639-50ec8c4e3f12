# 批量编辑窗口剪贴板粘贴功能

## 功能概述
为批量编辑窗口添加了与添加记录窗口相同的剪贴板粘贴功能，支持从JSON数据快速填充批量编辑字段。

## ✅ 新增功能特性

### **1. 剪贴板粘贴支持**
- ✅ **快捷键**：`Ctrl+V` / `Cmd+V` 粘贴JSON数据
- ✅ **智能检测**：只在批量编辑窗口打开时响应
- ✅ **焦点保护**：输入框获得焦点时不干扰正常粘贴

### **2. JSON数据处理**
- ✅ **单个对象**：直接使用JSON对象数据
- ✅ **数组数据**：自动使用数组中的第一条记录
- ✅ **格式验证**：自动验证JSON格式有效性

### **3. 字段自动填充**
- ✅ **支持的字段**：
  - `hypervisor` → Hypervisor
  - `v4_task_name` → V4 Task Name
  - `v6_task_name` → V6 Task Name
  - `v4_limit_rate` → V4 Limit Rate
  - `v6_limit_rate` → V6 Limit Rate
  - `v4_replica` → V4 Replica
  - `v6_replica` → V6 Replica
  - V4/V6配置参数（下载次数、最大时间、重试次数、连接超时）
  - `local_conf_server` → Local Conf Server
  - `notes` → Notes

### **4. 智能复选框管理**
- ✅ **自动启用**：填充字段时自动勾选对应的复选框
- ✅ **输入框激活**：自动启用对应的输入框
- ✅ **批量应用**：只有勾选的字段才会应用到选中记录

### **5. 视觉反馈**
- ✅ **粘贴提示**：窗口打开时显示粘贴快捷键提示
- ✅ **填充动画**：成功填充的字段会有高亮动画
- ✅ **状态通知**：显示填充成功的字段数量

## 🔧 使用方法

### **步骤1：选择记录**
1. 在表格中选择一条或多条记录
2. 点击"批量编辑"按钮打开批量编辑窗口

### **步骤2：准备JSON数据**
复制包含记录数据的JSON到剪贴板，例如：
```json
{
  "hypervisor": "beijing-chaoyang-room1-server3",
  "v4_task_name": "test_v4_task",
  "v6_task_name": "test_v6_task",
  "v4_limit_rate": "1000M",
  "v6_limit_rate": "1000M",
  "v4_replica": 4,
  "v6_replica": 4,
  "local_conf_server": "config.example.com",
  "notes": "批量编辑测试"
}
```

### **步骤3：粘贴数据**
1. 在批量编辑窗口中按 `Ctrl+V` (Windows/Linux) 或 `Cmd+V` (Mac)
2. 系统会自动：
   - 解析JSON数据
   - 勾选对应字段的复选框
   - 填充字段值
   - 显示成功填充的字段数量

### **步骤4：确认应用**
1. 检查自动填充的字段值
2. 根据需要调整或取消勾选某些字段
3. 点击"应用更改"按钮批量更新选中记录

## 🎯 技术实现

### **事件监听管理**
- **独立监听器**：批量编辑窗口有独立的剪贴板监听器
- **生命周期管理**：窗口打开时添加，关闭时移除
- **冲突避免**：与添加记录窗口的监听器互不干扰

### **字段映射系统**
- **完整映射**：支持所有批量编辑字段
- **ID匹配**：JSON字段名自动映射到批量编辑输入框ID
- **复选框联动**：自动管理复选框和输入框的启用状态

### **错误处理**
- **格式验证**：JSON格式错误时显示友好提示
- **字段检查**：只填充存在的有效字段
- **异常捕获**：完整的错误处理和用户反馈

## 📋 测试场景

### **基础功能测试**
1. **单条记录编辑**：选择1条记录，粘贴JSON数据
2. **批量记录编辑**：选择多条记录，粘贴JSON数据
3. **部分字段填充**：JSON只包含部分字段
4. **无效JSON处理**：粘贴无效JSON格式数据

### **交互测试**
1. **快捷键响应**：测试Ctrl+V和Cmd+V
2. **焦点保护**：在输入框中粘贴普通文本
3. **窗口切换**：在不同模态框间切换
4. **复选框联动**：验证复选框自动勾选

### **兼容性测试**
1. **浏览器兼容**：Chrome、Firefox、Safari、Edge
2. **操作系统**：Windows、macOS、Linux
3. **安全上下文**：HTTPS环境下的剪贴板API

## 🔄 与添加记录窗口的一致性

### **相同特性**
- ✅ 相同的快捷键操作
- ✅ 相同的JSON解析逻辑
- ✅ 相同的视觉反馈效果
- ✅ 相同的错误处理机制

### **差异化处理**
- ✅ **字段映射**：批量编辑使用 `batch_` 前缀的字段ID
- ✅ **复选框管理**：自动勾选和启用对应字段
- ✅ **应用逻辑**：只更新勾选的字段到选中记录

批量编辑窗口现在具备了完整的剪贴板粘贴功能，与添加记录窗口保持一致的用户体验！
