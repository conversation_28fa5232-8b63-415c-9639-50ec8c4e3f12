// 配置文件加载和保存功能

// 处理文件选择
function handleFileSelect(event) {
    console.log('handleFileSelect 被调用');
    const file = event.target.files[0];
    if (!file) {
        console.log('没有选择文件');
        return;
    }

    console.log('选择的文件:', file.name, '大小:', file.size, '类型:', file.type);

    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.csv')) {
        alert('请选择CSV文件！');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        console.log('文件读取完成，内容长度:', e.target.result.length);
        const content = e.target.result;
        try {
            const parsedData = parseCSV(content);
            console.log('CSV解析完成，数据条数:', parsedData.length);

            data = parsedData;
            buildHierarchySelectors();
            filterData();

            console.log('数据加载成功，共', data.length, '条记录');
            alert(`文件"${file.name}"加载成功！共加载 ${data.length} 条记录。`);

            // 调整侧边栏高度
            setTimeout(() => {
                if (typeof adjustSidebarHeight === 'function') {
                    adjustSidebarHeight();
                }
            }, 100);

        } catch (error) {
            console.error('文件解析失败:', error);
            alert('文件解析失败：' + error.message);
        }
    };

    reader.onerror = function() {
        console.error('文件读取失败');
        alert('文件读取失败');
    };

    reader.readAsText(file, 'utf-8');
}

// 解析CSV内容
function parseCSV(content) {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length === 0) return [];
    
    const headers = parseCSVLine(lines[0]);
    const result = [];
    
    for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i]);
        if (values.length === 0) continue;
        
        const row = {};
        headers.forEach((header, index) => {
            row[header] = values[index] || '';
        });
        result.push(row);
    }
    
    return result;
}

// 解析CSV行（处理引号和逗号）
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    let i = 0;
    
    while (i < line.length) {
        const char = line[i];
        
        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // 双引号转义
                current += '"';
                i += 2;
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
                i++;
            }
        } else if (char === ',' && !inQuotes) {
            // 字段分隔符
            result.push(current.trim());
            current = '';
            i++;
        } else {
            current += char;
            i++;
        }
    }
    
    result.push(current.trim());
    return result;
}

// 生成CSV内容
function generateCSV(dataArray) {
    const headers = ['vm_ip_address', 'hypervisor', 'v4_task_name', 'v6_task_name',
                   'v4_limit_rate', 'v6_limit_rate', 'v4_replica', 'v6_replica',
                   'v4_download_times', 'v4_max_time', 'v4_max_retries', 'v4_connect_timeout',
                   'v6_download_times', 'v6_max_time', 'v6_max_retries', 'v6_connect_timeout',
                   'local_conf_server', 'notes'];

    let csv = headers.join(',') + '\n';

    dataArray.forEach(row => {
        const values = headers.map(header => {
            let value = row[header] || '';
            // 如果值包含逗号或引号，需要用引号包围
            if (value.includes(',') || value.includes('"')) {
                value = '"' + value.replace(/"/g, '""') + '"';
            }
            return value;
        });
        csv += values.join(',') + '\n';
    });

    return csv;
}

// 导出CSV
function exportCSV() {
    if (!data || data.length === 0) {
        alert('没有数据可导出！');
        return;
    }

    try {
        const csv = generateCSV(data);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'task_configs.csv';
        link.click();

        console.log('CSV导出成功，共', data.length, '条记录');
    } catch (error) {
        console.error('导出CSV失败:', error);
        alert(`导出CSV失败: ${error.message}`);
    }
}



// 处理拖拽上传
function setupDragAndDrop() {
    const dropZone = document.body;
    
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.add('drag-over');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.remove('drag-over');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dropZone.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                
                if (fileExtension === 'csv') {
                    try {
                        data = parseCSV(content);
                        buildHierarchySelectors();
                        filterData();
                        console.log('CSV数据加载成功，共', data.length, '条记录');
                    } catch (error) {
                        alert('CSV文件解析失败：' + error.message);
                    }
                } else {
                    alert('不支持的文件格式。请上传CSV文件。');
                }
            };
            reader.readAsText(file);
        }
    });
}

// 保存配置到本地存储
function saveToLocalStorage() {
    try {
        const configData = {
            data: data,
            filters: {
                provinces: Array.from(selectedFilters.provinces),
                cities: Array.from(selectedFilters.cities),
                rooms: Array.from(selectedFilters.rooms),
                servers: Array.from(selectedFilters.servers)
            },
            pagination: {
                currentPage: currentPage,
                pageSize: pageSize
            },
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('taskConfigEditor', JSON.stringify(configData));
        console.log('配置已保存到本地存储');
        return true;
    } catch (error) {
        console.error('保存到本地存储失败:', error);
        return false;
    }
}

// 从本地存储加载配置
function loadFromLocalStorage() {
    try {
        const savedData = localStorage.getItem('taskConfigEditor');
        if (!savedData) return false;
        
        const configData = JSON.parse(savedData);
        
        // 恢复数据
        if (configData.data && Array.isArray(configData.data)) {
            data = configData.data;
        }
        
        // 恢复过滤器状态
        if (configData.filters) {
            selectedFilters.provinces = new Set(configData.filters.provinces || []);
            selectedFilters.cities = new Set(configData.filters.cities || []);
            selectedFilters.rooms = new Set(configData.filters.rooms || []);
            selectedFilters.servers = new Set(configData.filters.servers || []);
        }
        
        // 恢复分页状态
        if (configData.pagination) {
            currentPage = configData.pagination.currentPage || 1;
            pageSize = configData.pagination.pageSize || 50;
            
            // 更新页面大小选择器
            const pageSizeSelect = document.getElementById('pageSize');
            if (pageSizeSelect) {
                pageSizeSelect.value = pageSize;
            }
        }
        
        buildHierarchySelectors();
        filterData();
        console.log('从本地存储加载配置成功');
        return true;
    } catch (error) {
        console.error('从本地存储加载失败:', error);
        return false;
    }
}

// 清除本地存储
function clearLocalStorage() {
    try {
        localStorage.removeItem('taskConfigEditor');
        console.log('本地存储已清除');
        return true;
    } catch (error) {
        console.error('清除本地存储失败:', error);
        return false;
    }
}






