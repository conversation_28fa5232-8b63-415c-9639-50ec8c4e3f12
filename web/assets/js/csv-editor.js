// CSV格式配置的编辑功能

// 表格展开状态
let isTableFullExpanded = false;

// IP地址排序辅助函数
function ipToNumber(ip) {
    if (!ip || typeof ip !== 'string') return 0;

    const parts = ip.split('.');
    if (parts.length !== 4) return 0;

    let result = 0;
    for (let i = 0; i < 4; i++) {
        const part = parseInt(parts[i], 10);
        if (isNaN(part) || part < 0 || part > 255) return 0;
        result = result * 256 + part;
    }
    return result;
}

// 对已过滤的数据应用排序（不重新渲染表格）
function applySortToFilteredData(column, direction) {
    if (!column || !direction) return;

    filteredData.sort((a, b) => {
        let valueA = a[column] || '';
        let valueB = b[column] || '';

        // IP地址特殊处理
        if (column === 'vm_ip_address') {
            const numA = ipToNumber(valueA);
            const numB = ipToNumber(valueB);
            return direction === 'asc' ? numA - numB : numB - numA;
        }

        // 字符串排序
        valueA = valueA.toString().toLowerCase();
        valueB = valueB.toString().toLowerCase();

        if (direction === 'asc') {
            return valueA.localeCompare(valueB);
        } else {
            return valueB.localeCompare(valueA);
        }
    });
}

// 排序函数（包含表格重新渲染）
function sortData(column, direction) {
    if (!column || !direction) return;

    applySortToFilteredData(column, direction);

    // 重新渲染表格
    currentPage = 1; // 排序后回到第一页
    renderTable();
}

// 处理表头点击排序
function handleHeaderClick(column) {

    // 确定新的排序方向
    let newDirection;
    if (sortState.column === column) {
        // 同一列：升序 -> 降序 -> 无排序 -> 升序
        if (sortState.direction === 'asc') {
            newDirection = 'desc';
        } else if (sortState.direction === 'desc') {
            newDirection = null;
        } else {
            newDirection = 'asc';
        }
    } else {
        // 不同列：直接设为升序
        newDirection = 'asc';
    }

    // 更新排序状态
    sortState.column = newDirection ? column : null;
    sortState.direction = newDirection;

    // 更新表头视觉状态
    updateHeaderSortIndicators();

    if (newDirection) {
        // 执行排序
        sortData(column, newDirection);
    } else {
        // 恢复原始顺序：重新过滤数据
        filterData();
    }
}

// 更新表头排序指示器
function updateHeaderSortIndicators() {
    const headers = document.querySelectorAll('.sortable-header');
    headers.forEach(header => {
        const column = header.dataset.column;
        header.classList.remove('sort-asc', 'sort-desc');

        if (sortState.column === column) {
            if (sortState.direction === 'asc') {
                header.classList.add('sort-asc');
            } else if (sortState.direction === 'desc') {
                header.classList.add('sort-desc');
            }
        }
    });
}

// 数据过滤
function filterData() {
    const searchIP = document.getElementById('searchIP').value.toLowerCase();
    const searchTask = document.getElementById('searchTask').value.toLowerCase();
    
    filteredData = data.filter(row => {
        // IP 搜索
        if (searchIP && !row.vm_ip_address.toLowerCase().includes(searchIP)) {
            return false;
        }
        
        // 任务名搜索
        if (searchTask && 
            !row.v4_task_name.toLowerCase().includes(searchTask) && 
            !row.v6_task_name.toLowerCase().includes(searchTask)) {
            return false;
        }
        
        // Hypervisor 层级过滤
        const parsed = parseHypervisor(row.hypervisor);
        
        if (selectedFilters.provinces.size > 0 && !selectedFilters.provinces.has(parsed.province)) {
            return false;
        }
        if (selectedFilters.cities.size > 0 && !selectedFilters.cities.has(parsed.city)) {
            return false;
        }
        if (selectedFilters.rooms.size > 0 && !selectedFilters.rooms.has(parsed.room)) {
            return false;
        }
        if (selectedFilters.servers.size > 0 && !selectedFilters.servers.has(parsed.server)) {
            return false;
        }
        
        return true;
    });

    // 如果有排序状态，应用排序
    if (sortState.column && sortState.direction) {
        applySortToFilteredData(sortState.column, sortState.direction);
    }

    currentPage = 1;
    renderTable();
    updateSelectedCount();
}

// 表格渲染
function renderTable() {
    const tbody = document.getElementById('tableBody');
    tbody.innerHTML = '';
    
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    const pageData = filteredData.slice(start, end);
    
    pageData.forEach((row, index) => {
        const globalIndex = start + index;
        const isSelected = selectedRows.has(globalIndex);
        const tr = document.createElement('tr');
        if (isSelected) {
            tr.classList.add('selected');
        }
        
        // 移除整行的点击事件，改为单独处理每个单元格
        
        // 定义可编辑的字段
        const editableFields = [
            'vm_ip_address', 'hypervisor', 'v4_task_name', 'v6_task_name',
            'v4_limit_rate', 'v6_limit_rate', 'v4_replica', 'v6_replica',
            'v4_download_times', 'v4_max_time', 'v4_max_retries', 'v4_connect_timeout',
            'v6_download_times', 'v6_max_time', 'v6_max_retries', 'v6_connect_timeout',
            'local_conf_server', 'notes'
        ];

        tr.innerHTML = `
            <td class="row-number">${globalIndex + 1}</td>
            <td class="editable-cell" data-field="vm_ip_address" data-index="${globalIndex}">${row.vm_ip_address || ''}</td>
            <td class="editable-cell" data-field="hypervisor" data-index="${globalIndex}">${row.hypervisor || ''}</td>
            <td class="editable-cell" data-field="v4_task_name" data-index="${globalIndex}">${row.v4_task_name || ''}</td>
            <td class="editable-cell" data-field="v6_task_name" data-index="${globalIndex}">${row.v6_task_name || ''}</td>
            <td class="editable-cell" data-field="v4_limit_rate" data-index="${globalIndex}">${row.v4_limit_rate || ''}</td>
            <td class="editable-cell" data-field="v6_limit_rate" data-index="${globalIndex}">${row.v6_limit_rate || ''}</td>
            <td class="editable-cell" data-field="v4_replica" data-index="${globalIndex}">${row.v4_replica || ''}</td>
            <td class="editable-cell" data-field="v6_replica" data-index="${globalIndex}">${row.v6_replica || ''}</td>
            <td class="editable-cell v4-config-data" data-field="v4_download_times" data-index="${globalIndex}">${row.v4_download_times || ''}</td>
            <td class="editable-cell v4-config-data" data-field="v4_max_time" data-index="${globalIndex}">${row.v4_max_time || ''}</td>
            <td class="editable-cell v4-config-data" data-field="v4_max_retries" data-index="${globalIndex}">${row.v4_max_retries || ''}</td>
            <td class="editable-cell v4-config-data" data-field="v4_connect_timeout" data-index="${globalIndex}">${row.v4_connect_timeout || ''}</td>
            <td class="editable-cell v6-config-data" data-field="v6_download_times" data-index="${globalIndex}">${row.v6_download_times || ''}</td>
            <td class="editable-cell v6-config-data" data-field="v6_max_time" data-index="${globalIndex}">${row.v6_max_time || ''}</td>
            <td class="editable-cell v6-config-data" data-field="v6_max_retries" data-index="${globalIndex}">${row.v6_max_retries || ''}</td>
            <td class="editable-cell v6-config-data" data-field="v6_connect_timeout" data-index="${globalIndex}">${row.v6_connect_timeout || ''}</td>
            <td class="editable-cell basic-data" data-field="local_conf_server" data-index="${globalIndex}">${row.local_conf_server || ''}</td>
            <td class="editable-cell basic-data" data-field="notes" data-index="${globalIndex}">${row.notes || ''}</td>
        `;

        // 为行号添加专门的事件处理
        const rowNumberCell = tr.querySelector('.row-number');
        rowNumberCell.addEventListener('click', function(event) {
            handleRowNumberClick(globalIndex, event);
        });
        rowNumberCell.addEventListener('mousedown', function(event) {
            handleRowNumberMouseDown(globalIndex, event);
        });
        rowNumberCell.addEventListener('mouseenter', function(event) {
            handleRowNumberMouseEnter(globalIndex, event);
        });

        // 为可编辑单元格添加事件处理
        const editableCells = tr.querySelectorAll('.editable-cell');
        editableCells.forEach((cell, cellIndex) => {
            // 鼠标按下事件：开始选择
            cell.addEventListener('mousedown', function(event) {
                if (event.button === 0) { // 左键
                    handleCellMouseDown(globalIndex, cellIndex, cell, event);
                }
            });

            // 双击事件：编辑单元格
            cell.addEventListener('dblclick', function(event) {
                event.stopPropagation();
                event.preventDefault();
                startCellEdit(cell);
            });
        });
        tbody.appendChild(tr);
    });
    
    updatePagination();
    updateBatchControls();

    // 调整侧边栏高度以匹配表格内容
    setTimeout(() => {
        adjustSidebarHeight();
        // 修复表头和内容的滚动条对齐问题
        fixScrollbarAlignment();
    }, 0);
}

// 修复表头和内容的滚动条对齐问题
function fixScrollbarAlignment() {
    const headerContainer = document.querySelector('.table-header-container');
    const contentContainer = document.querySelector('.table-content-container');

    if (!headerContainer || !contentContainer) return;

    // 使用requestAnimationFrame确保DOM已经完全渲染
    requestAnimationFrame(() => {
        // 检查内容容器是否有滚动条
        const hasScrollbar = contentContainer.scrollHeight > contentContainer.clientHeight;

        if (hasScrollbar) {
            // 计算滚动条宽度 - 使用更精确的方法
            const scrollbarWidth = contentContainer.offsetWidth - contentContainer.clientWidth;

            // 为表头容器添加右边距以匹配滚动条宽度
            headerContainer.style.paddingRight = scrollbarWidth + 'px';
            headerContainer.style.boxSizing = 'border-box';

            console.log('Scrollbar detected, width:', scrollbarWidth + 'px');
        } else {
            // 如果没有滚动条，移除右边距
            headerContainer.style.paddingRight = '0px';
            console.log('No scrollbar detected, padding removed');
        }
    });
}

// 分页功能
function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        renderTable();
        // 确保侧边栏高度调整
        setTimeout(() => {
            if (typeof adjustSidebarHeight === 'function') {
                adjustSidebarHeight();
            }
        }, 100);
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (currentPage < totalPages) {
        currentPage++;
        renderTable();
        // 确保侧边栏高度调整
        setTimeout(() => {
            if (typeof adjustSidebarHeight === 'function') {
                adjustSidebarHeight();
            }
        }, 100);
    }
}

function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    currentPage = 1;
    renderTable();
    // 确保侧边栏高度调整
    setTimeout(() => {
        if (typeof adjustSidebarHeight === 'function') {
            adjustSidebarHeight();
        }
    }, 100);
}

// CRUD 操作
function showAddModal() {
    editingIndex = -1;
    document.getElementById('modalTitle').textContent = '添加记录';
    document.getElementById('editForm').reset();

    // 重置批量添加模式
    const batchAddMode = document.getElementById('batch_add_mode');
    const ipRangeHelp = document.getElementById('ipRangeHelp');
    const previewSection = document.getElementById('batchPreviewSection');
    const submitBtn = document.getElementById('submitBtn');

    if (batchAddMode) batchAddMode.checked = false;
    if (ipRangeHelp) ipRangeHelp.style.display = 'none';
    if (previewSection) previewSection.style.display = 'none';
    if (submitBtn) submitBtn.textContent = '保存记录';

    // 移除批量添加模式的CSS类
    document.body.classList.remove('batch-add-mode');

    // 设置剪贴板粘贴监听器
    setupAddModalClipboardListener();

    document.getElementById('editModal').style.display = 'block';
}

// 编辑和删除功能已移除，改用Excel风格的行选择

function handleFormSubmit(event) {
    event.preventDefault();

    const batchAddMode = document.getElementById('batch_add_mode');
    const isBatchMode = batchAddMode && batchAddMode.checked;

    if (isBatchMode) {
        // 批量添加模式
        handleBatchAdd();
    } else {
        // 单条记录添加/编辑模式
        handleSingleAdd();
    }
}

// 处理单条记录添加/编辑
function handleSingleAdd() {
    const row = {};

    // 获取所有字段
    const fields = ['vm_ip_address', 'hypervisor', 'v4_task_name', 'v6_task_name',
                   'v4_limit_rate', 'v6_limit_rate', 'v4_replica', 'v6_replica',
                   'v4_download_times', 'v4_max_time', 'v4_max_retries', 'v4_connect_timeout',
                   'v6_download_times', 'v6_max_time', 'v6_max_retries', 'v6_connect_timeout',
                   'local_conf_server', 'notes'];

    fields.forEach(field => {
        const input = document.getElementById(`edit_${field}`);
        row[field] = input ? input.value : '';
    });

    if (editingIndex === -1) {
        // 添加新记录
        data.push(row);
        showNotification('记录添加成功', 'success');
    } else {
        // 更新现有记录
        const originalIndex = data.indexOf(filteredData[editingIndex]);
        data[originalIndex] = row;
        showNotification('记录更新成功', 'success');
    }

    closeModal();
    buildHierarchySelectors();
    filterData();
    saveToLocalStorage();
}

// 行选择功能
function toggleRowSelection(index) {
    if (selectedRows.has(index)) {
        selectedRows.delete(index);
    } else {
        selectedRows.add(index);
    }
    renderTable();
}

// 全选/取消全选功能（适应新的行号列）
function toggleSelectAll() {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;

    if (selectedRows.size > 0) {
        // 如果有选中的行，则清除所有选择
        selectedRows.clear();
    } else {
        // 如果没有选中的行，则全选当前页
        for (let i = start; i < end && i < filteredData.length; i++) {
            selectedRows.add(i);
        }
    }
    renderTable();
}

function clearSelection() {
    selectedRows.clear();
    renderTable();
}

// Cmd+A 全选所有数据（不仅仅是当前页）
function selectAllRows() {
    selectedRows.clear();
    // 选择所有过滤后的数据
    for (let i = 0; i < filteredData.length; i++) {
        selectedRows.add(i);
    }
    renderTable();
    updateSelectedCount();

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = true;
    }
}

// 批量操作
function showBatchEditModal() {
    // 检查是否有选中的记录
    if (selectedRows.size > 0) {
        // 获取第一条选中的记录作为模板
        const selectedIndices = Array.from(selectedRows).sort((a, b) => a - b);
        const firstSelectedIndex = selectedIndices[0];
        const firstSelectedRecord = filteredData[firstSelectedIndex];

        if (firstSelectedRecord) {
            const recordCount = selectedRows.size;
            console.log(`选中了${recordCount}条记录，使用第一条记录作为模板填充字段:`, firstSelectedRecord);

            // 定义所有字段的映射关系
            const fieldMappings = [
                'hypervisor',
                'v4_task_name',
                'v6_task_name',
                'v4_limit_rate',
                'v6_limit_rate',
                'v4_replica',
                'v6_replica',
                'v4_download_times',
                'v6_download_times',
                'v4_max_time',
                'v6_max_time',
                'v4_max_retries',
                'v6_max_retries',
                'v4_connect_timeout',
                'v6_connect_timeout',
                'local_conf_server',
                'notes'
            ];

            // 自动填充所有字段
            fieldMappings.forEach(fieldName => {
                const checkboxId = `batch_${fieldName}_check`;
                const inputId = `batch_${fieldName}`;

                const checkbox = document.getElementById(checkboxId);
                const input = document.getElementById(inputId);

                if (checkbox && input && firstSelectedRecord[fieldName] !== undefined) {
                    // 勾选复选框
                    checkbox.checked = true;
                    // 启用输入框
                    input.disabled = false;
                    // 填充值
                    input.value = firstSelectedRecord[fieldName] || '';

                    console.log(`填充字段 ${fieldName}: ${firstSelectedRecord[fieldName]}`);
                }
            });

            // 更新模态框标题
            const modalTitle = document.querySelector('#batchEditModal h2');
            if (modalTitle) {
                if (recordCount === 1) {
                    modalTitle.textContent = `编辑记录 (${firstSelectedRecord.vm_ip_address || '未知IP'})`;
                } else {
                    modalTitle.textContent = `批量编辑 (${recordCount} 条记录) - 基于第一条记录 (${firstSelectedRecord.vm_ip_address || '未知IP'})`;
                }
            }
        }
    } else {
        // 无选择时，清空所有字段并重置标题
        resetBatchEditForm();
        const modalTitle = document.querySelector('#batchEditModal h2');
        if (modalTitle) {
            modalTitle.textContent = `批量编辑 (0 条记录)`;
        }
    }

    // 设置批量编辑模态框的剪贴板监听器
    setupBatchEditModalClipboardListener();

    document.getElementById('batchEditModal').style.display = 'block';
}

function closeBatchEditModal() {
    document.getElementById('batchEditModal').style.display = 'none';
    // 移除批量编辑模态框的剪贴板监听器
    removeBatchEditModalClipboardListener();
    // 关闭时重置表单
    resetBatchEditForm();
}

// 重置批量编辑表单
function resetBatchEditForm() {
    const fieldMappings = [
        'hypervisor',
        'v4_task_name',
        'v6_task_name',
        'v4_limit_rate',
        'v6_limit_rate',
        'v4_replica',
        'v6_replica',
        'v4_download_times',
        'v6_download_times',
        'v4_max_time',
        'v6_max_time',
        'v4_max_retries',
        'v6_max_retries',
        'v4_connect_timeout',
        'v6_connect_timeout',
        'local_conf_server',
        'notes'
    ];

    // 重置所有字段
    fieldMappings.forEach(fieldName => {
        const checkboxId = `batch_${fieldName}_check`;
        const inputId = `batch_${fieldName}`;

        const checkbox = document.getElementById(checkboxId);
        const input = document.getElementById(inputId);

        if (checkbox && input) {
            // 取消勾选复选框
            checkbox.checked = false;
            // 禁用输入框
            input.disabled = true;
            // 清空值
            input.value = '';
        }
    });
}

function batchDelete() {
    if (selectedRows.size === 0) {
        alert('请先选择要删除的记录');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedRows.size} 条记录吗？`)) {
        // 将选中的索引转换为实际的数据行
        const rowsToDelete = [];
        selectedRows.forEach(index => {
            if (index < filteredData.length) {
                rowsToDelete.push(filteredData[index]);
            }
        });
        
        // 从原始数据中删除这些行
        rowsToDelete.forEach(row => {
            const originalIndex = data.indexOf(row);
            if (originalIndex !== -1) {
                data.splice(originalIndex, 1);
            }
        });
        
        // 清除选择状态
        selectedRows.clear();
        
        // 重新构建层级选择器和过滤数据
        buildHierarchySelectors();
        filterData();
    }
}

function handleBatchEdit(event) {
    event.preventDefault();
    
    if (selectedRows.size === 0) {
        alert('请先选择要编辑的记录');
        return;
    }
    
    const formData = new FormData(event.target);
    const batchFields = ['hypervisor', 'v4_task_name', 'v6_task_name', 'v4_limit_rate', 'v6_limit_rate',
                        'v4_replica', 'v6_replica', 'v4_download_times', 'v4_max_time', 'v4_max_retries',
                        'v4_connect_timeout', 'v6_download_times', 'v6_max_time', 'v6_max_retries',
                        'v6_connect_timeout', 'local_conf_server', 'notes'];
    
    // 获取要更新的字段
    const updates = {};
    batchFields.forEach(field => {
        const checkbox = document.getElementById(`batch_${field}_check`);
        const input = document.getElementById(`batch_${field}`);
        
        if (checkbox && checkbox.checked && input) {
            updates[field] = input.value;
        }
    });
    
    if (Object.keys(updates).length === 0) {
        alert('请至少选择一个要修改的字段');
        return;
    }
    
    // 应用批量更新
    selectedRows.forEach(index => {
        if (index < filteredData.length) {
            const row = filteredData[index];
            const originalIndex = data.indexOf(row);
            if (originalIndex !== -1) {
                Object.keys(updates).forEach(field => {
                    data[originalIndex][field] = updates[field];
                });
            }
        }
    });
    
    closeBatchEditModal();
    buildHierarchySelectors();
    filterData();

    alert(`已成功更新 ${selectedRows.size} 条记录`);
}

// Excel风格行选择功能
let isDragging = false;
let isRowNumberDragging = false;
let dragStartIndex = -1;
let dragEndIndex = -1;
let wasRowDragged = false; // 标记是否发生了拖拽

// 处理行号点击和拖拽开始
function handleRowNumberClick(index, event) {
    event.stopPropagation();
    event.preventDefault();

    if (event.shiftKey && lastSelectedIndex !== -1) {
        // Shift+点击：选择范围（Excel风格）
        console.log('Shift+点击行号:', index, '从', lastSelectedIndex, '到', index);
        selectRange(lastSelectedIndex, index);
        lastSelectedIndex = index;
        renderTable();
        updateSelectedCount();
    } else if (event.ctrlKey || event.metaKey) {
        // Ctrl/Cmd+点击：切换选择状态（Excel风格）
        console.log('Ctrl/Cmd+点击行号:', index);
        toggleRowSelection(index);
        lastSelectedIndex = index;
        renderTable();
        updateSelectedCount();
    } else {
        // 普通点击：清除其他选择，只选择当前行
        console.log('普通点击行号:', index);
        selectedRows.clear();
        selectedRows.add(index);
        lastSelectedIndex = index;
        renderTable();
        updateSelectedCount();
    }
}

// 处理行号鼠标按下事件（开始拖拽）
function handleRowNumberMouseDown(index, event) {
    if (event.button !== 0) return; // 只处理左键

    // 如果有修饰键，不启动拖拽，让click事件处理
    if (event.shiftKey || event.ctrlKey || event.metaKey) {
        return;
    }

    console.log('mousedown - 行号:', index);
    event.stopPropagation();

    isRowNumberDragging = true;
    dragStartIndex = index;
    dragEndIndex = index;
    wasRowDragged = false; // 重置拖拽标记
    console.log('mousedown - 重置拖拽标记为false');

    // 添加表格容器的拖拽样式
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
        tableContainer.classList.add('dragging');
    }

    // 添加全局事件监听
    document.addEventListener('mouseup', handleRowNumberMouseUp);
    document.addEventListener('mouseleave', handleRowNumberMouseUp);
}

// 处理行号鼠标进入事件（拖拽过程中）
function handleRowNumberMouseEnter(index, event) {
    if (!isRowNumberDragging) return;

    dragEndIndex = index;
    wasRowDragged = true; // 标记发生了拖拽

    // 清除之前的选择（如果不是Ctrl/Cmd选择）
    if (!event.ctrlKey && !event.metaKey) {
        selectedRows.clear();
    }

    // 选择拖拽范围内的所有行
    const startIdx = Math.min(dragStartIndex, dragEndIndex);
    const endIdx = Math.max(dragStartIndex, dragEndIndex);

    for (let i = startIdx; i <= endIdx; i++) {
        selectedRows.add(i);
    }

    // 更新最后选择的索引
    lastSelectedIndex = index;

    renderTable();
}

// 处理行号鼠标释放事件（结束拖拽）
function handleRowNumberMouseUp(event) {
    if (!isRowNumberDragging) return;

    console.log('mouseup - 拖拽标记:', wasRowDragged, '开始索引:', dragStartIndex);

    isRowNumberDragging = false;
    dragStartIndex = -1;
    dragEndIndex = -1;

    // 移除表格容器的拖拽样式
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
        tableContainer.classList.remove('dragging');
    }

    // 移除全局事件监听
    document.removeEventListener('mouseup', handleRowNumberMouseUp);
    document.removeEventListener('mouseleave', handleRowNumberMouseUp);

    // 如果发生了拖拽，更新选择计数
    if (wasRowDragged) {
        updateSelectedCount();
    }
}

// 处理鼠标按下事件
function handleMouseDown(index, event) {
    if (event.button !== 0) return; // 只处理左键

    isDragging = true;
    dragStartIndex = index;
    dragEndIndex = index;

    if (!event.ctrlKey && !event.metaKey && !event.shiftKey) {
        selectedRows.clear();
    }

    selectedRows.add(index);
    lastSelectedIndex = index;

    // 添加全局鼠标事件监听
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleMouseUp);

    event.preventDefault();
    renderTable();
}

// 处理鼠标进入事件
function handleMouseEnter(index, event) {
    if (!isDragging) return;

    dragEndIndex = index;

    // 清除之前的选择（如果不是Ctrl选择）
    if (!event.ctrlKey && !event.metaKey) {
        selectedRows.clear();
    }

    // 选择拖拽范围内的所有行
    const startIdx = Math.min(dragStartIndex, dragEndIndex);
    const endIdx = Math.max(dragStartIndex, dragEndIndex);

    for (let i = startIdx; i <= endIdx; i++) {
        selectedRows.add(i);
    }

    renderTable();
}

// 处理鼠标释放事件
function handleMouseUp(event) {
    isDragging = false;
    dragStartIndex = -1;
    dragEndIndex = -1;

    // 移除全局事件监听
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('mouseleave', handleMouseUp);

    renderTable();
}

// 选择范围
function selectRange(startIndex, endIndex) {
    const start = Math.min(startIndex, endIndex);
    const end = Math.max(startIndex, endIndex);

    for (let i = start; i <= end; i++) {
        selectedRows.add(i);
    }

    renderTable();
}

// Excel风格内联编辑功能
let currentEditingCell = null;

// 开始编辑单元格
function startCellEdit(cell) {
    // 如果已经有单元格在编辑，先完成编辑
    if (currentEditingCell) {
        finishCellEdit();
    }

    currentEditingCell = cell;
    const field = cell.dataset.field;
    const index = parseInt(cell.dataset.index);
    const currentValue = cell.textContent.trim();

    // 创建输入框
    const input = document.createElement('input');
    input.type = getInputType(field);
    input.className = 'cell-editor';
    input.value = currentValue;

    // 设置单元格为编辑状态
    cell.classList.add('cell-editing');
    cell.innerHTML = '';
    cell.appendChild(input);

    // 聚焦并选中文本
    input.focus();
    input.select();

    // 添加事件监听
    input.onblur = function() {
        finishCellEdit();
    };

    input.onkeydown = function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            finishCellEdit();
        } else if (event.key === 'Escape') {
            event.preventDefault();
            cancelCellEdit();
        }
    };

    // 阻止事件冒泡
    input.onclick = function(event) {
        event.stopPropagation();
    };
}

// 完成编辑
function finishCellEdit() {
    if (!currentEditingCell) return;

    const input = currentEditingCell.querySelector('.cell-editor');
    if (!input) return;

    const field = currentEditingCell.dataset.field;
    const index = parseInt(currentEditingCell.dataset.index);
    const newValue = input.value.trim();

    // 更新数据
    if (index < filteredData.length) {
        const row = filteredData[index];
        const originalIndex = data.indexOf(row);
        if (originalIndex !== -1) {
            data[originalIndex][field] = newValue;
            filteredData[index][field] = newValue;
        }
    }

    // 恢复单元格显示
    currentEditingCell.classList.remove('cell-editing');
    currentEditingCell.textContent = newValue;
    currentEditingCell = null;

    // 重新构建层级选择器和过滤数据（如果编辑的是关键字段）
    if (['vm_ip_address', 'hypervisor'].includes(field)) {
        buildHierarchySelectors();
        filterData();
    }
}

// 取消编辑
function cancelCellEdit() {
    if (!currentEditingCell) return;

    const originalValue = currentEditingCell.dataset.originalValue || '';
    currentEditingCell.classList.remove('cell-editing');
    currentEditingCell.textContent = originalValue;
    currentEditingCell = null;
}

// 根据字段类型返回合适的输入类型
function getInputType(field) {
    const numberFields = [
        'v4_replica', 'v6_replica', 'v4_download_times', 'v4_max_time',
        'v4_max_retries', 'v4_connect_timeout', 'v6_download_times',
        'v6_max_time', 'v6_max_retries', 'v6_connect_timeout'
    ];

    return numberFields.includes(field) ? 'number' : 'text';
}

// 处理单元格鼠标按下
function handleCellMouseDown(rowIndex, cellIndex, cellElement, event) {
    // 如果点击的是拖动手柄，不处理
    if (event.target.classList.contains('cell-drag-handle')) {
        return;
    }



    // 清除之前的选择
    clearCellSelection();

    // 开始选择
    isSelectingCells = true;
    selectionStartCell = { row: rowIndex, cell: cellIndex, element: cellElement };

    // 选中当前单元格
    selectSingleCell(rowIndex, cellIndex, cellElement);

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleCellSelectionMove);
    document.addEventListener('mouseup', handleCellSelectionEnd);
}

// 处理鼠标移动（拖动选择）
function handleCellSelectionMove(event) {
    if (!isSelectingCells) return;

    // 找到鼠标下的单元格
    const elementUnderMouse = document.elementFromPoint(event.clientX, event.clientY);
    const cellUnderMouse = elementUnderMouse?.closest('.editable-cell');

    if (cellUnderMouse) {
        const rowIndex = parseInt(cellUnderMouse.dataset.index);
        const cellIndex = Array.from(cellUnderMouse.parentNode.children).indexOf(cellUnderMouse) - 1; // -1 因为第一列是行号

        if (rowIndex >= 0 && cellIndex >= 0) {
            updateCellSelection(rowIndex, cellIndex);
        }
    }
}

// 处理单元格选择结束
function handleCellSelectionEnd(event) {
    isSelectingCells = false;
    document.removeEventListener('mousemove', handleCellSelectionMove);
    document.removeEventListener('mouseup', handleCellSelectionEnd);

    // 为选择范围添加拖动手柄
    if (selectedCellRange) {
        createRangeDragHandle();
    }
}

// 选择单个单元格
function selectSingleCell(rowIndex, cellIndex, cellElement) {
    currentSelectedCell = {
        row: rowIndex,
        cell: cellIndex,
        element: cellElement
    };

    const cellKey = `${rowIndex}-${cellIndex}`;
    selectedCells.add(cellKey);

    // 设置选择范围
    selectedCellRange = {
        startRow: rowIndex,
        endRow: rowIndex,
        startCol: cellIndex,
        endCol: cellIndex
    };

    // 添加选中样式
    cellElement.classList.add('cell-selected');


}

// 更新单元格选择范围
function updateCellSelection(endRowIndex, endCellIndex) {
    if (!selectionStartCell) return;

    // 清除之前的选择样式
    clearCellSelectionStyles();

    // 计算选择范围
    const startRow = selectionStartCell.row;
    const startCol = selectionStartCell.cell;
    const endRow = endRowIndex;
    const endCol = endCellIndex;

    selectedCellRange = {
        startRow: Math.min(startRow, endRow),
        endRow: Math.max(startRow, endRow),
        startCol: Math.min(startCol, endCol),
        endCol: Math.max(startCol, endCol)
    };

    // 应用选择范围样式
    applyCellRangeSelection();
}

// 应用单元格范围选择样式
function applyCellRangeSelection() {
    if (!selectedCellRange) return;

    const { startRow, endRow, startCol, endCol } = selectedCellRange;

    // 清除所有选择相关的样式
    selectedCells.clear();

    // 为范围内的所有单元格添加样式
    for (let row = startRow; row <= endRow; row++) {
        const tableRows = document.querySelectorAll('tbody tr');
        const tableRow = tableRows[row - (currentPage - 1) * pageSize];
        if (tableRow) {
            const cells = tableRow.querySelectorAll('.editable-cell');
            for (let col = startCol; col <= endCol; col++) {
                if (cells[col]) {
                    const cellKey = `${row}-${col}`;
                    selectedCells.add(cellKey);

                    // 添加基础选择样式
                    cells[col].classList.add('cell-in-range');

                    // 添加边框样式
                    if (row === startRow) cells[col].classList.add('cell-range-top');
                    if (row === endRow) cells[col].classList.add('cell-range-bottom');
                    if (col === startCol) cells[col].classList.add('cell-range-left');
                    if (col === endCol) cells[col].classList.add('cell-range-right');

                    // 如果是单个单元格，使用特殊样式
                    if (startRow === endRow && startCol === endCol) {
                        cells[col].classList.remove('cell-in-range');
                        cells[col].classList.add('cell-selected');
                    }
                }
            }
        }
    }
}

// 清除单元格选择样式
function clearCellSelectionStyles() {
    document.querySelectorAll('.cell-selected, .cell-in-range, .cell-range-top, .cell-range-bottom, .cell-range-left, .cell-range-right').forEach(cell => {
        cell.classList.remove('cell-selected', 'cell-in-range', 'cell-range-top', 'cell-range-bottom', 'cell-range-left', 'cell-range-right');
    });
}

// 清除单元格选择
function clearCellSelection() {
    // 移除所有选择样式和拖动手柄
    clearCellSelectionStyles();

    // 移除拖动手柄
    document.querySelectorAll('.cell-drag-handle').forEach(handle => {
        handle.remove();
    });

    selectedCells.clear();
    currentSelectedCell = null;
    selectedCellRange = null;
    selectionStartCell = null;

    // 清理拖动状态
    if (isDraggingHandle) {
        cleanupDrag();
    }
}

// 清除所有过滤条件
function clearAllFilters() {
    // 清除搜索框
    const searchIP = document.getElementById('searchIP');
    const searchTask = document.getElementById('searchTask');
    if (searchIP) searchIP.value = '';
    if (searchTask) searchTask.value = '';

    // 清除所有过滤器选择
    selectedFilters.provinces.clear();
    selectedFilters.cities.clear();
    selectedFilters.rooms.clear();
    selectedFilters.servers.clear();

    // 重置上次选择的索引
    lastSelectedFilters.provinces = -1;
    lastSelectedFilters.cities = -1;
    lastSelectedFilters.rooms = -1;
    lastSelectedFilters.servers = -1;

    // 重新构建选择器（这会清除所有选中状态）
    buildHierarchySelectors();

    // 重新过滤数据
    filterData();

    // 保存到本地存储
    saveToLocalStorage();

    console.log('所有过滤条件已清除');
}

// 创建范围拖动手柄
function createRangeDragHandle() {
    if (!selectedCellRange) return;

    // 移除已存在的拖动手柄
    document.querySelectorAll('.cell-drag-handle').forEach(handle => {
        handle.remove();
    });

    // 找到选择范围的右下角单元格
    const { endRow, endCol } = selectedCellRange;
    const tableRows = document.querySelectorAll('tbody tr');
    const tableRow = tableRows[endRow - (currentPage - 1) * pageSize];

    if (tableRow) {
        const cells = tableRow.querySelectorAll('.editable-cell');
        const targetCell = cells[endCol];

        if (targetCell) {
            // 创建拖动手柄元素
            const dragHandle = document.createElement('div');
            dragHandle.className = 'cell-drag-handle';

            // 添加拖动事件监听器
            dragHandle.addEventListener('mousedown', function(event) {
                handleRangeDragStart(event);
            });

            // 将手柄添加到右下角单元格
            targetCell.appendChild(dragHandle);
        }
    }
}

// 处理范围拖动开始
function handleRangeDragStart(event) {

    event.preventDefault();
    event.stopPropagation();

    if (!selectedCellRange) return;

    isDraggingHandle = true;
    dragStartCell = selectedCellRange; // 存储整个选择范围

    // 创建跟随鼠标的拖动手柄
    const followingHandle = document.createElement('div');
    followingHandle.className = 'drag-handle-following';
    followingHandle.style.left = event.clientX + 'px';
    followingHandle.style.top = event.clientY + 'px';
    document.body.appendChild(followingHandle);



    // 存储跟随手柄的引用
    window.followingDragHandle = followingHandle;

    // 添加拖动源样式到所有选中的单元格
    selectedCells.forEach(cellKey => {
        const [rowIndex, cellIndex] = cellKey.split('-').map(Number);
        const tableRows = document.querySelectorAll('tbody tr');
        const tableRow = tableRows[rowIndex - (currentPage - 1) * pageSize];
        if (tableRow) {
            const cells = tableRow.querySelectorAll('.editable-cell');
            if (cells[cellIndex]) {
                cells[cellIndex].classList.add('cell-drag-source');
            }
        }
    });

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleRangeDragMove);
    document.addEventListener('mouseup', handleRangeDragEnd);

    // 改变鼠标样式
    document.body.style.cursor = 'crosshair';


}

// 处理范围拖动移动
function handleRangeDragMove(event) {
    if (!isDraggingHandle || !dragStartCell) return;

    // 更新跟随鼠标的拖动手柄位置
    if (window.followingDragHandle) {
        window.followingDragHandle.style.left = event.clientX + 'px';
        window.followingDragHandle.style.top = event.clientY + 'px';

    }

    // 找到鼠标下的单元格
    const elementUnderMouse = document.elementFromPoint(event.clientX, event.clientY);
    const cellUnderMouse = elementUnderMouse?.closest('.editable-cell');

    if (cellUnderMouse) {
        const rowIndex = parseInt(cellUnderMouse.dataset.index);
        const cellIndex = Array.from(cellUnderMouse.parentNode.children).indexOf(cellUnderMouse) - 1; // -1 因为第一列是行号

        // 清除之前的预览
        clearDragPreview();

        // 更新当前拖动到的位置
        dragCurrentCell = { row: rowIndex, cell: cellIndex };

        // 显示拖动预览
        showRangeDragPreview();
    }
}

// 处理范围拖动结束
function handleRangeDragEnd(event) {
    if (!isDraggingHandle) return;

    // 执行范围复制操作
    if (dragCurrentCell) {
        performRangeCopy();

        // 更新选择范围到新位置
        updateSelectionToNewRange();
    }

    // 清理状态
    cleanupRangeDrag();
}

// 显示范围拖动预览
function showRangeDragPreview() {
    if (!dragStartCell || !dragCurrentCell) return;

    const sourceRange = dragStartCell;
    const sourceStartRow = sourceRange.startRow;
    const sourceEndRow = sourceRange.endRow;
    const sourceStartCol = sourceRange.startCol;
    const sourceEndCol = sourceRange.endCol;

    const dragRow = dragCurrentCell.row;
    const dragCol = dragCurrentCell.cell;

    // 计算填充范围
    let fillStartRow, fillEndRow, fillStartCol, fillEndCol;

    if (dragRow >= sourceEndRow) {
        // 向下填充：显示从源范围到拖动位置的整个范围
        fillStartRow = sourceStartRow;
        fillEndRow = dragRow;
        fillStartCol = sourceStartCol;
        fillEndCol = sourceEndCol;
    } else if (dragRow <= sourceStartRow) {
        // 向上填充：显示从拖动位置到源范围的整个范围
        fillStartRow = dragRow;
        fillEndRow = sourceEndRow;
        fillStartCol = sourceStartCol;
        fillEndCol = sourceEndCol;
    } else {
        // 在源范围内，不显示预览
        return;
    }

    // 为填充范围内的所有单元格添加预览样式
    const tableRows = document.querySelectorAll('tbody tr');
    for (let row = fillStartRow; row <= fillEndRow; row++) {
        const tableRow = tableRows[row - (currentPage - 1) * pageSize];
        if (tableRow) {
            const cells = tableRow.querySelectorAll('.editable-cell');
            for (let col = fillStartCol; col <= fillEndCol; col++) {
                if (cells[col]) {
                    // 源范围使用原有样式，新填充区域使用醒目样式
                    if (row >= sourceStartRow && row <= sourceEndRow) {
                        cells[col].classList.add('cell-drag-source');
                    } else {
                        cells[col].classList.add('cell-fill-preview');

                        // 只给外围单元格添加边框
                        if (row === fillStartRow) {
                            cells[col].classList.add('fill-top');
                        }
                        if (row === fillEndRow) {
                            cells[col].classList.add('fill-bottom');
                        }
                        if (col === fillStartCol) {
                            cells[col].classList.add('fill-left');
                        }
                        if (col === fillEndCol) {
                            cells[col].classList.add('fill-right');
                        }
                    }
                    dragPreviewCells.add(cells[col]);
                }
            }
        }
    }
}

// 清除拖动预览
function clearDragPreview() {
    dragPreviewCells.forEach(cell => {
        cell.classList.remove('cell-drag-target', 'cell-drag-preview', 'cell-fill-preview', 'cell-drag-source',
                           'fill-top', 'fill-bottom', 'fill-left', 'fill-right');
    });
    dragPreviewCells.clear();
}

// 执行范围复制
function performRangeCopy() {
    if (!dragStartCell || !dragCurrentCell) return;

    const sourceRange = dragStartCell;
    const sourceWidth = sourceRange.endCol - sourceRange.startCol + 1;
    const sourceHeight = sourceRange.endRow - sourceRange.startRow + 1;



    // 获取源数据 - 直接从数据数组获取，确保数据一致性
    const sourceData = [];
    for (let row = sourceRange.startRow; row <= sourceRange.endRow; row++) {
        const rowData = [];
        if (row < filteredData.length) {
            for (let col = sourceRange.startCol; col <= sourceRange.endCol; col++) {
                // 从DOM获取字段名和值
                const tableRows = document.querySelectorAll('tbody tr');
                const tableRow = tableRows[row - (currentPage - 1) * pageSize];
                if (tableRow) {
                    const cells = tableRow.querySelectorAll('.editable-cell');
                    if (cells[col]) {
                        const field = cells[col].dataset.field;
                        const value = cells[col].textContent.trim();
                        rowData.push({ field, value });
                    }
                }
            }
        }
        sourceData.push(rowData);
    }

    // Excel风格的填充逻辑：从源范围填充到拖动位置
    const sourceStartRow = sourceRange.startRow;
    const sourceEndRow = sourceRange.endRow;
    const sourceStartCol = sourceRange.startCol;
    const sourceEndCol = sourceRange.endCol;

    const dragRow = dragCurrentCell.row;
    const dragCol = dragCurrentCell.cell;

    // 计算填充范围
    let fillStartRow, fillEndRow, fillStartCol, fillEndCol;

    if (dragRow >= sourceEndRow) {
        // 向下填充：从源范围的下一行开始填充到拖动位置
        fillStartRow = sourceEndRow + 1;
        fillEndRow = dragRow;
        fillStartCol = sourceStartCol;
        fillEndCol = sourceEndCol;
    } else if (dragRow <= sourceStartRow) {
        // 向上填充：从拖动位置填充到源范围的上一行
        fillStartRow = dragRow;
        fillEndRow = sourceStartRow - 1;
        fillStartCol = sourceStartCol;
        fillEndCol = sourceEndCol;
    } else {
        // 在源范围内，不需要填充
        return;
    }





    // Excel风格填充：将源数据重复填充到目标范围
    const sourceRowCount = sourceEndRow - sourceStartRow + 1;
    const sourceColCount = sourceEndCol - sourceStartCol + 1;

    for (let fillRow = fillStartRow; fillRow <= fillEndRow; fillRow++) {
        // 确保填充行在有效范围内
        if (fillRow >= 0 && fillRow < filteredData.length) {

            for (let fillCol = fillStartCol; fillCol <= fillEndCol; fillCol++) {
                // 计算对应的源位置（循环使用源数据）
                const sourceRowIndex = sourceStartRow + ((fillRow - fillStartRow) % sourceRowCount);
                const sourceColIndex = sourceStartCol + ((fillCol - fillStartCol) % sourceColCount);

                // 获取源单元格的值
                const tableRows = document.querySelectorAll('tbody tr');
                const sourceTableRow = tableRows[sourceRowIndex - (currentPage - 1) * pageSize];

                if (sourceTableRow) {
                    const sourceCells = sourceTableRow.querySelectorAll('.editable-cell');
                    if (sourceCells[sourceColIndex]) {
                        const sourceValue = sourceCells[sourceColIndex].textContent.trim();

                        // 获取目标单元格的字段名
                        const targetTableRow = tableRows[fillRow - (currentPage - 1) * pageSize];
                        if (targetTableRow) {
                            const targetCells = targetTableRow.querySelectorAll('.editable-cell');
                            if (targetCells[fillCol]) {
                                const targetField = targetCells[fillCol].dataset.field;

                                // 更新数据
                                const targetDataRow = filteredData[fillRow];
                                const originalIndex = data.indexOf(targetDataRow);
                                if (originalIndex !== -1) {
                                    data[originalIndex][targetField] = sourceValue;
                                    filteredData[fillRow][targetField] = sourceValue;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 重新渲染表格
    renderTable();
}

// 更新选择范围到新位置
function updateSelectionToNewRange() {
    if (!dragStartCell || !dragCurrentCell) return;

    const sourceRange = dragStartCell;
    const sourceStartRow = sourceRange.startRow;
    const sourceEndRow = sourceRange.endRow;
    const sourceStartCol = sourceRange.startCol;
    const sourceEndCol = sourceRange.endCol;

    const dragRow = dragCurrentCell.row;
    const dragCol = dragCurrentCell.cell;

    // 计算新的选择范围
    let newStartRow, newEndRow, newStartCol, newEndCol;

    if (dragRow >= sourceEndRow) {
        // 向下填充 - 选择整个填充范围
        newStartRow = sourceStartRow;
        newEndRow = dragRow;
        newStartCol = sourceStartCol;
        newEndCol = sourceEndCol;
    } else if (dragRow <= sourceStartRow) {
        // 向上填充 - 选择整个填充范围
        newStartRow = dragRow;
        newEndRow = sourceEndRow;
        newStartCol = sourceStartCol;
        newEndCol = sourceEndCol;
    } else {
        // 在源范围内，保持原选择
        return;
    }

    // 清除当前选择
    clearCellSelection();

    // 设置新的选择范围
    selectedCellRange = {
        startRow: newStartRow,
        endRow: newEndRow,
        startCol: newStartCol,
        endCol: newEndCol
    };

    // 更新选中单元格集合
    selectedCells.clear();
    for (let row = newStartRow; row <= newEndRow; row++) {
        for (let col = newStartCol; col <= newEndCol; col++) {
            selectedCells.add(`${row}-${col}`);
        }
    }

    // 应用选择样式
    applyCellRangeSelection();
}

// 清理范围拖动状态
function cleanupRangeDrag() {
    isDraggingHandle = false;

    // 移除跟随鼠标的拖动手柄
    if (window.followingDragHandle) {
        document.body.removeChild(window.followingDragHandle);
        window.followingDragHandle = null;
    }

    // 移除拖动源样式
    document.querySelectorAll('.cell-drag-source').forEach(cell => {
        cell.classList.remove('cell-drag-source');
    });

    clearDragPreview();

    // 重置变量
    dragStartCell = null;
    dragCurrentCell = null;

    // 移除事件监听器
    document.removeEventListener('mousemove', handleRangeDragMove);
    document.removeEventListener('mouseup', handleRangeDragEnd);

    // 恢复鼠标样式
    document.body.style.cursor = '';

    // 重新创建拖动手柄
    createRangeDragHandle();
}

// 清理拖动状态（保持兼容性）
function cleanupDrag() {
    cleanupRangeDrag();
}

// 点击表格外部时完成编辑和清除选择
document.addEventListener('click', function(event) {
    if (currentEditingCell && !event.target.closest('.cell-editing')) {
        finishCellEdit();
    }

    // 如果点击的不是表格内容，清除单元格选择
    if (!event.target.closest('.table-container')) {
        clearCellSelection();
    }
});

// 表格完整展开/收起功能
function toggleFullTable() {
    const tableWrapper = document.querySelector('.table-wrapper');
    const icon = document.getElementById('table-expand-icon');
    const button = icon.parentElement;
    const expandIcon = icon.querySelector('.expand-icon');
    const collapseIcon = icon.querySelector('.collapse-icon');

    // 切换状态
    isTableFullExpanded = !isTableFullExpanded;

    // 更新表格容器的CSS类和图标显示
    if (isTableFullExpanded) {
        tableWrapper.classList.add('table-full-expanded');
        expandIcon.style.display = 'none';
        collapseIcon.style.display = 'block';
        button.classList.add('expanded');
        button.title = '收起表格';
    } else {
        tableWrapper.classList.remove('table-full-expanded');
        expandIcon.style.display = 'block';
        collapseIcon.style.display = 'none';
        button.classList.remove('expanded');
        button.title = '展开完整表格';
    }

    // 重新渲染表格以确保单元格选择状态正确
    renderTable();

    // 调整侧边栏高度
    adjustSidebarHeight();
}

// 调整侧边栏高度以匹配表格容器高度
function adjustSidebarHeight() {
    const tableWrapper = document.querySelector('.table-wrapper');
    const sidebar = document.querySelector('.table-expand-sidebar');

    if (tableWrapper && sidebar) {
        // 侧边栏高度只匹配 table-wrapper 的高度
        const tableWrapperHeight = tableWrapper.offsetHeight;

        // 设置侧边栏高度
        sidebar.style.height = tableWrapperHeight + 'px';

        console.log('Sidebar height adjusted to:', {
            tableWrapperHeight: tableWrapperHeight
        });
    }
}

// 监听窗口大小变化，重新调整滚动条对齐
window.addEventListener('resize', function() {
    setTimeout(() => {
        fixScrollbarAlignment();
        adjustSidebarHeight();
    }, 100);
});

// 批量添加相关函数

// 处理批量添加
function handleBatchAdd() {
    const ipAddressInput = document.getElementById('edit_vm_ip_address');
    const ipAddressValue = ipAddressInput ? ipAddressInput.value.trim() : '';

    if (!ipAddressValue) {
        alert('请输入IP地址或IP地址范围');
        return;
    }

    // 解析IP地址范围
    const ipAddresses = parseIPRange(ipAddressValue);

    if (ipAddresses.length === 0) {
        alert('IP地址格式不正确');
        return;
    }

    // 获取模板数据（除了IP地址外的其他字段）
    const templateRow = {};
    const fields = ['hypervisor', 'v4_task_name', 'v6_task_name',
                   'v4_limit_rate', 'v6_limit_rate', 'v4_replica', 'v6_replica',
                   'v4_download_times', 'v4_max_time', 'v4_max_retries', 'v4_connect_timeout',
                   'v6_download_times', 'v6_max_time', 'v6_max_retries', 'v6_connect_timeout',
                   'local_conf_server', 'notes'];

    fields.forEach(field => {
        const input = document.getElementById(`edit_${field}`);
        templateRow[field] = input ? input.value : '';
    });

    // 批量创建记录
    const newRecords = [];
    ipAddresses.forEach(ip => {
        const newRecord = {
            vm_ip_address: ip,
            ...templateRow
        };
        newRecords.push(newRecord);
        data.push(newRecord);
    });

    console.log(`批量添加了 ${newRecords.length} 条记录:`, newRecords);

    closeModal();
    buildHierarchySelectors();
    filterData();

    // 显示成功消息
    showNotification(`批量添加成功！共添加 ${newRecords.length} 条记录`, 'success');

    saveToLocalStorage();
}

// 解析IP地址范围
function parseIPRange(ipString) {
    const ipAddresses = [];

    // 检查是否是范围格式 (例如: *************-110)
    if (ipString.includes('-')) {
        const parts = ipString.split('-');
        if (parts.length === 2) {
            const startIP = parts[0].trim();
            const endPart = parts[1].trim();

            // 验证起始IP格式
            const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
            const startMatch = startIP.match(ipRegex);

            if (startMatch) {
                const [, a, b, c, startD] = startMatch;
                const startNum = parseInt(startD);

                // 检查结束部分是否是完整IP还是只是最后一段
                let endNum;
                if (endPart.includes('.')) {
                    // 完整IP格式
                    const endMatch = endPart.match(ipRegex);
                    if (endMatch) {
                        endNum = parseInt(endMatch[4]);
                    } else {
                        return [];
                    }
                } else {
                    // 只是最后一段数字
                    endNum = parseInt(endPart);
                }

                // 验证数字范围
                if (isNaN(startNum) || isNaN(endNum) || startNum < 0 || startNum > 255 || endNum < 0 || endNum > 255 || startNum > endNum) {
                    return [];
                }

                // 生成IP地址范围
                for (let i = startNum; i <= endNum; i++) {
                    ipAddresses.push(`${a}.${b}.${c}.${i}`);
                }
            }
        }
    } else {
        // 单个IP地址
        const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
        if (ipRegex.test(ipString)) {
            const parts = ipString.split('.');
            const valid = parts.every(part => {
                const num = parseInt(part);
                return num >= 0 && num <= 255;
            });

            if (valid) {
                ipAddresses.push(ipString);
            }
        }
    }

    return ipAddresses;
}

// 切换批量添加模式
function toggleBatchAddMode() {
    const batchAddMode = document.getElementById('batch_add_mode');
    const ipRangeHelp = document.getElementById('ipRangeHelp');
    const previewSection = document.getElementById('batchPreviewSection');
    const submitBtn = document.getElementById('submitBtn');
    const ipAddressInput = document.getElementById('edit_vm_ip_address');

    if (batchAddMode && batchAddMode.checked) {
        // 启用批量添加模式
        document.body.classList.add('batch-add-mode');
        if (ipRangeHelp) ipRangeHelp.style.display = 'block';
        if (submitBtn) submitBtn.textContent = '批量添加记录';

        // 监听IP地址输入变化以更新预览
        if (ipAddressInput) {
            ipAddressInput.addEventListener('input', updateBatchPreview);
            updateBatchPreview(); // 立即更新一次
        }
    } else {
        // 禁用批量添加模式
        document.body.classList.remove('batch-add-mode');
        if (ipRangeHelp) ipRangeHelp.style.display = 'none';
        if (previewSection) previewSection.style.display = 'none';
        if (submitBtn) submitBtn.textContent = '保存记录';

        // 移除事件监听器
        if (ipAddressInput) {
            ipAddressInput.removeEventListener('input', updateBatchPreview);
        }
    }
}

// 更新批量添加预览
function updateBatchPreview() {
    const ipAddressInput = document.getElementById('edit_vm_ip_address');
    const previewSection = document.getElementById('batchPreviewSection');
    const previewContent = document.getElementById('batchPreviewContent');
    const hypervisorInput = document.getElementById('edit_hypervisor');

    if (!ipAddressInput || !previewSection || !previewContent) return;

    const ipAddressValue = ipAddressInput.value.trim();
    const hypervisorValue = hypervisorInput ? hypervisorInput.value.trim() : '';

    if (!ipAddressValue) {
        previewSection.style.display = 'none';
        return;
    }

    // 解析IP地址范围
    const ipAddresses = parseIPRange(ipAddressValue);

    if (ipAddresses.length === 0) {
        previewSection.style.display = 'none';
        return;
    }

    // 显示预览
    previewSection.style.display = 'block';

    // 生成预览内容
    let previewHTML = '';
    const maxPreviewItems = 10; // 最多显示10个预览项

    ipAddresses.slice(0, maxPreviewItems).forEach(ip => {
        previewHTML += `
            <div class="preview-item">
                <span class="ip-address">${ip}</span>
                <span class="hypervisor">${hypervisorValue || '(未设置)'}</span>
            </div>
        `;
    });

    if (ipAddresses.length > maxPreviewItems) {
        previewHTML += `
            <div class="preview-item">
                <span class="ip-address">...</span>
                <span class="hypervisor">还有 ${ipAddresses.length - maxPreviewItems} 条记录</span>
            </div>
        `;
    }

    previewHTML += `<div class="preview-count">总计将添加 ${ipAddresses.length} 条记录</div>`;

    previewContent.innerHTML = previewHTML;
}

// 剪贴板粘贴功能相关变量和函数
let addModalClipboardListener = null;

// 设置添加记录模态框的剪贴板监听器
function setupAddModalClipboardListener() {
    // 移除之前的监听器（如果存在）
    removeAddModalClipboardListener();

    // 添加粘贴提示
    addPasteHint();

    // 创建新的监听器函数
    addModalClipboardListener = function(event) {
        // 只在添加记录模态框打开时响应
        const editModal = document.getElementById('editModal');
        if (!editModal || editModal.style.display !== 'block') {
            return;
        }

        // 检查是否是粘贴快捷键 (Ctrl+V 或 Cmd+V)
        if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
            console.log('检测到粘贴快捷键，当前焦点元素:', document.activeElement);

            const activeElement = document.activeElement;

            // 如果焦点在输入框或文本区域中，让默认粘贴行为继续
            if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
                console.log('焦点在输入框中，跳过JSON粘贴');
                return;
            }

            // 阻止默认行为并处理剪贴板粘贴
            console.log('执行JSON粘贴功能');
            event.preventDefault();
            handleClipboardPaste();
        }
    };

    // 添加事件监听器
    document.addEventListener('keydown', addModalClipboardListener);
}

// 移除添加记录模态框的剪贴板监听器
function removeAddModalClipboardListener() {
    if (addModalClipboardListener) {
        document.removeEventListener('keydown', addModalClipboardListener);
        addModalClipboardListener = null;
    }
    // 移除粘贴提示
    removePasteHint();
}

// 添加粘贴提示
function addPasteHint() {
    const editModal = document.getElementById('editModal');
    if (!editModal) return;

    const modalContent = editModal.querySelector('.modal-content');
    if (!modalContent) return;

    // 检查是否已存在提示
    if (modalContent.querySelector('.paste-hint')) return;

    const hint = document.createElement('div');
    hint.className = 'paste-hint';
    hint.textContent = 'Ctrl+V / Cmd+V 粘贴JSON数据';

    modalContent.appendChild(hint);

    // 显示提示
    setTimeout(() => {
        hint.classList.add('show');
    }, 100);

    // 3秒后自动隐藏
    setTimeout(() => {
        hint.classList.remove('show');
    }, 3000);
}

// 移除粘贴提示
function removePasteHint() {
    const editModal = document.getElementById('editModal');
    if (!editModal) return;

    const hint = editModal.querySelector('.paste-hint');
    if (hint) {
        hint.remove();
    }
}

// 处理剪贴板粘贴
async function handleClipboardPaste() {
    console.log('开始处理剪贴板粘贴');
    try {
        let clipboardText = '';

        console.log('检查剪贴板API支持:', {
            hasClipboard: !!navigator.clipboard,
            isSecureContext: window.isSecureContext
        });

        // 尝试使用现代Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            try {
                console.log('尝试读取剪贴板内容...');
                clipboardText = await navigator.clipboard.readText();
                console.log('剪贴板读取成功，内容长度:', clipboardText.length);
            } catch (err) {
                console.warn('Clipboard API读取失败:', err);
                // 如果现代API失败，显示提示信息
                showNotification('无法访问剪贴板，请确保页面在安全上下文中运行', 'warning');
                return;
            }
        } else {
            console.warn('剪贴板API不可用:', {
                hasClipboard: !!navigator.clipboard,
                isSecureContext: window.isSecureContext
            });
            // 对于不支持现代API的浏览器，显示提示
            showNotification('当前浏览器不支持剪贴板API，请手动输入数据', 'warning');
            return;
        }

        if (!clipboardText.trim()) {
            showNotification('剪贴板为空', 'warning');
            return;
        }

        console.log('剪贴板内容:', clipboardText);

        // 尝试解析JSON
        let jsonData;
        try {
            jsonData = JSON.parse(clipboardText);
        } catch (parseError) {
            console.warn('剪贴板内容不是有效的JSON格式:', parseError);
            showNotification('剪贴板内容不是有效的JSON格式', 'error');
            return;
        }

        // 处理JSON数据
        let recordData;
        if (Array.isArray(jsonData)) {
            if (jsonData.length === 0) {
                showNotification('剪贴板中的JSON数组为空', 'warning');
                return;
            }
            // 如果是数组，使用第一条记录
            recordData = jsonData[0];
            console.log('使用JSON数组中的第一条记录:', recordData);
        } else if (typeof jsonData === 'object' && jsonData !== null) {
            // 如果是单个对象，直接使用
            recordData = jsonData;
            console.log('使用JSON对象:', recordData);
        } else {
            showNotification('剪贴板中的JSON数据格式不正确', 'error');
            return;
        }

        // 填充表单字段
        const filledFields = fillFormFromJSON(recordData);

        if (filledFields > 0) {
            showNotification(`成功从剪贴板填充了 ${filledFields} 个字段`, 'success');
        } else {
            showNotification('未找到可填充的字段', 'warning');
        }

    } catch (error) {
        console.error('处理剪贴板粘贴时发生错误:', error);
        showNotification('粘贴失败：' + error.message, 'error');
    }
}

// 从JSON数据填充表单字段
function fillFormFromJSON(jsonData) {
    const fieldMapping = {
        'vm_ip_address': 'edit_vm_ip_address',
        'hypervisor': 'edit_hypervisor',
        'v4_task_name': 'edit_v4_task_name',
        'v6_task_name': 'edit_v6_task_name',
        'v4_limit_rate': 'edit_v4_limit_rate',
        'v6_limit_rate': 'edit_v6_limit_rate',
        'v4_replica': 'edit_v4_replica',
        'v6_replica': 'edit_v6_replica',
        'v4_download_times': 'edit_v4_download_times',
        'v4_max_time': 'edit_v4_max_time',
        'v4_max_retries': 'edit_v4_max_retries',
        'v4_connect_timeout': 'edit_v4_connect_timeout',
        'v6_download_times': 'edit_v6_download_times',
        'v6_max_time': 'edit_v6_max_time',
        'v6_max_retries': 'edit_v6_max_retries',
        'v6_connect_timeout': 'edit_v6_connect_timeout',
        'local_conf_server': 'edit_local_conf_server',
        'notes': 'edit_notes'
    };

    let filledCount = 0;
    const filledElements = [];

    // 添加模态框粘贴活动状态
    const editModal = document.getElementById('editModal');
    const modalContent = editModal ? editModal.querySelector('.modal-content') : null;
    if (modalContent) {
        modalContent.classList.add('paste-active');
    }

    // 遍历字段映射，填充存在的字段
    for (const [jsonField, inputId] of Object.entries(fieldMapping)) {
        if (jsonData.hasOwnProperty(jsonField)) {
            const inputElement = document.getElementById(inputId);
            if (inputElement) {
                const value = jsonData[jsonField];
                // 确保值不是null或undefined
                if (value !== null && value !== undefined) {
                    inputElement.value = String(value);
                    filledCount++;
                    filledElements.push(inputElement);
                    console.log(`填充字段 ${jsonField}: ${value}`);

                    // 添加视觉反馈
                    inputElement.classList.add('field-filled');
                }
            }
        }
    }

    // 如果填充了IP地址且启用了批量模式，更新预览
    if (jsonData.hasOwnProperty('vm_ip_address')) {
        const batchAddMode = document.getElementById('batch_add_mode');
        if (batchAddMode && batchAddMode.checked) {
            updateBatchPreview();
        }
    }

    // 2秒后移除视觉反馈
    setTimeout(() => {
        filledElements.forEach(element => {
            element.classList.remove('field-filled');
        });
        if (modalContent) {
            modalContent.classList.remove('paste-active');
        }
    }, 2000);

    return filledCount;
}

// 批量编辑模态框剪贴板监听器变量
let batchEditModalClipboardListener = null;

// 设置批量编辑模态框的剪贴板监听器
function setupBatchEditModalClipboardListener() {
    // 移除之前的监听器（如果存在）
    removeBatchEditModalClipboardListener();

    // 添加粘贴提示
    addBatchEditPasteHint();

    // 创建新的监听器函数
    batchEditModalClipboardListener = function(event) {
        // 只在批量编辑模态框打开时响应
        const batchEditModal = document.getElementById('batchEditModal');
        if (!batchEditModal || batchEditModal.style.display !== 'block') {
            return;
        }

        // 检查是否是粘贴快捷键 (Ctrl+V 或 Cmd+V)
        if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
            console.log('批量编辑模态框检测到粘贴快捷键，当前焦点元素:', document.activeElement);

            const activeElement = document.activeElement;

            // 如果焦点在输入框或文本区域中，让默认粘贴行为继续
            if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
                console.log('焦点在输入框中，跳过JSON粘贴');
                return;
            }

            // 阻止默认行为并处理剪贴板粘贴
            console.log('执行批量编辑JSON粘贴功能');
            event.preventDefault();
            handleBatchEditClipboardPaste();
        }
    };

    // 添加事件监听器
    document.addEventListener('keydown', batchEditModalClipboardListener);
}

// 移除批量编辑模态框的剪贴板监听器
function removeBatchEditModalClipboardListener() {
    if (batchEditModalClipboardListener) {
        document.removeEventListener('keydown', batchEditModalClipboardListener);
        batchEditModalClipboardListener = null;
    }

    // 移除粘贴提示
    const batchEditModal = document.getElementById('batchEditModal');
    if (batchEditModal) {
        const hint = batchEditModal.querySelector('.paste-hint');
        if (hint) {
            hint.remove();
        }
    }
}

// 添加批量编辑粘贴提示
function addBatchEditPasteHint() {
    const batchEditModal = document.getElementById('batchEditModal');
    if (!batchEditModal) return;

    const modalContent = batchEditModal.querySelector('.modal-content');
    if (!modalContent) return;

    // 检查是否已存在提示
    if (modalContent.querySelector('.paste-hint')) return;

    const hint = document.createElement('div');
    hint.className = 'paste-hint';
    hint.textContent = 'Ctrl+V / Cmd+V 粘贴JSON数据';

    modalContent.appendChild(hint);

    // 显示提示
    setTimeout(() => {
        hint.classList.add('show');
    }, 100);

    // 3秒后自动隐藏
    setTimeout(() => {
        hint.classList.remove('show');
    }, 3000);
}

// 处理批量编辑模态框的剪贴板粘贴
async function handleBatchEditClipboardPaste() {
    try {
        let clipboardText = '';

        // 尝试使用现代Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            try {
                console.log('尝试读取剪贴板内容...');
                clipboardText = await navigator.clipboard.readText();
                console.log('剪贴板读取成功，内容长度:', clipboardText.length);
            } catch (err) {
                console.warn('Clipboard API读取失败:', err);
                showNotification('无法访问剪贴板，请确保页面在安全上下文中运行', 'warning');
                return;
            }
        } else {
            console.warn('剪贴板API不可用');
            showNotification('当前浏览器不支持剪贴板API，请手动输入数据', 'warning');
            return;
        }

        if (!clipboardText.trim()) {
            showNotification('剪贴板为空', 'warning');
            return;
        }

        console.log('剪贴板内容:', clipboardText);

        // 尝试解析JSON
        let jsonData;
        try {
            jsonData = JSON.parse(clipboardText);
        } catch (parseError) {
            console.warn('剪贴板内容不是有效的JSON格式:', parseError);
            showNotification('剪贴板内容不是有效的JSON格式', 'error');
            return;
        }

        // 处理JSON数据
        let recordData;
        if (Array.isArray(jsonData)) {
            if (jsonData.length === 0) {
                showNotification('剪贴板中的JSON数组为空', 'warning');
                return;
            }
            // 如果是数组，使用第一条记录
            recordData = jsonData[0];
            console.log('使用JSON数组中的第一条记录:', recordData);
        } else if (typeof jsonData === 'object' && jsonData !== null) {
            // 如果是单个对象，直接使用
            recordData = jsonData;
            console.log('使用JSON对象:', recordData);
        } else {
            showNotification('剪贴板中的JSON数据格式不正确', 'error');
            return;
        }

        // 填充批量编辑表单字段
        const filledFields = fillBatchEditFormFromJSON(recordData);

        if (filledFields > 0) {
            showNotification(`成功从剪贴板填充了 ${filledFields} 个字段`, 'success');
        } else {
            showNotification('未找到可填充的字段', 'warning');
        }

    } catch (error) {
        console.error('处理批量编辑剪贴板粘贴时发生错误:', error);
        showNotification('粘贴失败：' + error.message, 'error');
    }
}

// 从JSON数据填充批量编辑表单
function fillBatchEditFormFromJSON(jsonData) {
    console.log('开始填充批量编辑表单，JSON数据:', jsonData);

    // 批量编辑字段映射（JSON字段名 -> 批量编辑输入框ID）
    const batchFieldMapping = {
        'hypervisor': 'batch_hypervisor',
        'v4_task_name': 'batch_v4_task_name',
        'v6_task_name': 'batch_v6_task_name',
        'v4_limit_rate': 'batch_v4_limit_rate',
        'v6_limit_rate': 'batch_v6_limit_rate',
        'v4_replica': 'batch_v4_replica',
        'v6_replica': 'batch_v6_replica',
        'v4_download_times': 'batch_v4_download_times',
        'v4_max_time': 'batch_v4_max_time',
        'v4_max_retries': 'batch_v4_max_retries',
        'v4_connect_timeout': 'batch_v4_connect_timeout',
        'v6_download_times': 'batch_v6_download_times',
        'v6_max_time': 'batch_v6_max_time',
        'v6_max_retries': 'batch_v6_max_retries',
        'v6_connect_timeout': 'batch_v6_connect_timeout',
        'local_conf_server': 'batch_local_conf_server',
        'notes': 'batch_notes'
    };

    let filledCount = 0;
    const filledElements = [];

    // 添加视觉反馈
    const modalContent = document.querySelector('#batchEditModal .modal-content');
    if (modalContent) {
        modalContent.classList.add('paste-active');
    }

    // 遍历字段映射，填充存在的字段
    for (const [jsonField, inputId] of Object.entries(batchFieldMapping)) {
        if (jsonData.hasOwnProperty(jsonField)) {
            const inputElement = document.getElementById(inputId);
            const checkboxElement = document.getElementById(inputId + '_check');

            if (inputElement && checkboxElement) {
                const value = jsonData[jsonField];
                // 确保值不是null或undefined
                if (value !== null && value !== undefined) {
                    // 启用复选框
                    checkboxElement.checked = true;
                    // 启用输入框
                    inputElement.disabled = false;
                    // 填充值
                    inputElement.value = String(value);

                    filledCount++;
                    filledElements.push(inputElement);
                    console.log(`填充批量编辑字段 ${jsonField}: ${value}`);

                    // 添加视觉反馈
                    inputElement.classList.add('field-filled');
                    checkboxElement.parentElement.classList.add('field-filled');
                }
            }
        }
    }

    // 2秒后移除视觉反馈
    setTimeout(() => {
        filledElements.forEach(element => {
            element.classList.remove('field-filled');
            const checkbox = document.getElementById(element.id + '_check');
            if (checkbox && checkbox.parentElement) {
                checkbox.parentElement.classList.remove('field-filled');
            }
        });
        if (modalContent) {
            modalContent.classList.remove('paste-active');
        }
    }, 2000);

    return filledCount;
}
