// 通用工具函数

// 解析 hypervisor 字段
function parseHypervisor(hypervisor) {
    if (hypervisor === '_DEFAULT_') return { province: '_DEFAULT_', city: '_DEFAULT_', room: '_DEFAULT_', server: '_DEFAULT_' };
    const parts = hypervisor.split('-');
    return {
        province: parts[0] || '',
        city: parts[1] || '',
        room: parts[2] || '',
        server: parts[3] || ''
    };
}

// 构建层级数据结构
function buildHierarchyData() {
    const hierarchy = {};
    
    data.forEach(row => {
        const parsed = parseHypervisor(row.hypervisor);
        
        if (!hierarchy[parsed.province]) {
            hierarchy[parsed.province] = {};
        }
        if (!hierarchy[parsed.province][parsed.city]) {
            hierarchy[parsed.province][parsed.city] = {};
        }
        if (!hierarchy[parsed.province][parsed.city][parsed.room]) {
            hierarchy[parsed.province][parsed.city][parsed.room] = new Set();
        }
        hierarchy[parsed.province][parsed.city][parsed.room].add(parsed.server);
    });
    
    return hierarchy;
}

// 更新选中数量显示
function updateSelectedCount() {
    const selectedCount = document.getElementById('selectedCount');
    if (selectedCount) {
        const totalFiltered = filteredData.length;
        const totalSelected = Array.from(selectedFilters.provinces).length + 
                             Array.from(selectedFilters.cities).length + 
                             Array.from(selectedFilters.rooms).length + 
                             Array.from(selectedFilters.servers).length;
        
        if (totalSelected > 0) {
            selectedCount.textContent = `已选择 ${totalSelected} 个过滤条件，显示 ${totalFiltered} 条记录`;
        } else {
            selectedCount.textContent = `显示 ${totalFiltered} 条记录`;
        }
    }
}

// 获取过滤器选择器ID
function getFilterSelectorId(filterType) {
    const selectorMap = {
        'provinces': 'provinceSelector',
        'cities': 'citySelector',
        'rooms': 'roomSelector',
        'servers': 'serverSelector'
    };
    return selectorMap[filterType];
}

// 切换批量编辑字段的启用状态
function toggleBatchField(fieldName) {
    const checkbox = document.getElementById(`batch_${fieldName}_check`);
    const input = document.getElementById(`batch_${fieldName}`);
    
    if (checkbox && input) {
        input.disabled = !checkbox.checked;
        if (!checkbox.checked) {
            input.value = '';
        }
    }
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    
    let allSelected = true;
    let anySelected = false;
    
    for (let i = start; i < end && i < filteredData.length; i++) {
        if (selectedRows.has(i)) {
            anySelected = true;
        } else {
            allSelected = false;
        }
    }
    
    selectAllCheckbox.checked = allSelected && anySelected;
    selectAllCheckbox.indeterminate = anySelected && !allSelected;
}

// 更新批量操作控件显示状态 - 优化的平滑过渡
function updateBatchControls() {
    const batchControls = document.getElementById('batchControls');
    const selectedCount = selectedRows.size;

    // 检查元素是否存在
    if (!batchControls) {
        console.warn('batchControls element not found');
        return;
    }

    if (selectedCount > 0) {
        // 显示批量控制栏
        if (!batchControls.classList.contains('show')) {
            // 确保元素可见但透明，然后添加show类触发动画
            batchControls.style.display = 'flex';
            // 使用requestAnimationFrame确保display设置生效后再添加show类
            requestAnimationFrame(() => {
                batchControls.classList.add('show');
            });
        }

        const selectedCountText = document.getElementById('selectedCountText');
        if (selectedCountText) {
            selectedCountText.textContent = `已选择 ${selectedCount} 条记录`;
        }
    } else {
        // 隐藏批量控制栏
        if (batchControls.classList.contains('show')) {
            batchControls.classList.remove('show');
            // 等待动画完成后隐藏元素
            setTimeout(() => {
                if (!batchControls.classList.contains('show')) {
                    batchControls.style.display = 'none';
                }
            }, 400); // 与CSS transition时间匹配
        }
    }
}

// 处理行点击事件，支持Shift多选
function handleRowClick(globalIndex, event) {
    console.log('handleRowClick called:', globalIndex, 'shiftKey:', event.shiftKey, 'lastSelectedIndex:', lastSelectedIndex);
    
    if (event.shiftKey && lastSelectedIndex !== -1) {
        // Shift多选：选择从lastSelectedIndex到当前索引之间的所有行
        const startIdx = Math.min(lastSelectedIndex, globalIndex);
        const endIdx = Math.max(lastSelectedIndex, globalIndex);
        
        // 选择范围内的所有行
        for (let i = startIdx; i <= endIdx; i++) {
            selectedRows.add(i);
        }
        renderTable();
    } else {
        // 普通单选
        toggleRowSelection(globalIndex);
        lastSelectedIndex = globalIndex;
    }
}

// 处理层级选择器的点击事件，支持Shift多选
function handleFilterClick(filterType, item, itemIndex, event) {
    if (event.shiftKey && lastSelectedFilters[filterType] !== -1) {
        // Shift多选：选择从上次选择到当前索引之间的所有项
        const selector = document.getElementById(getFilterSelectorId(filterType));
        const items = Array.from(selector.querySelectorAll('.level-item'));
        
        const startIdx = Math.min(lastSelectedFilters[filterType], itemIndex);
        const endIdx = Math.max(lastSelectedFilters[filterType], itemIndex);
        
        // 选择范围内的所有项
        for (let i = startIdx; i <= endIdx; i++) {
            if (items[i]) {
                const span = items[i].querySelector('span');
                if (span) {
                    const value = span.textContent;
                    selectedFilters[filterType].add(value);
                }
            }
        }
        
        // 重新构建层级选择器和过滤数据
        const hierarchy = buildHierarchyData();
        updateFilterSelectors(hierarchy, filterType);
        updateSelectorVisualState();
        filterData();
        updateSelectedCount();
    } else {
        // 普通单选
        toggleFilter(filterType, item);
        lastSelectedFilters[filterType] = itemIndex;
    }
}

// 全选/取消全选层级选择器中的所有项
function toggleSelectAllFilters(filterType) {
    const selector = document.getElementById(getFilterSelectorId(filterType));
    const items = Array.from(selector.querySelectorAll('.level-item span'));
    
    // 检查是否已全选
    const allItems = items.map(span => span.textContent);
    const selectedItems = Array.from(selectedFilters[filterType]);
    const isAllSelected = allItems.length > 0 && allItems.every(item => selectedItems.includes(item));
    
    if (isAllSelected) {
        // 取消全选
        selectedFilters[filterType].clear();
    } else {
        // 全选
        allItems.forEach(item => {
            selectedFilters[filterType].add(item);
        });
    }
    
    // 重新构建层级选择器和过滤数据
    const hierarchy = buildHierarchyData();
    updateFilterSelectors(hierarchy, filterType);
    updateSelectorVisualState();
    filterData();
    updateSelectedCount();
}

// 更新层级选择器（在层级变化时调用）
function updateFilterSelectors(hierarchy, changedFilterType) {
    if (changedFilterType === 'provinces') {
        updateCitySelector(hierarchy);
        updateRoomSelector(hierarchy);
        updateServerSelector(hierarchy);
    } else if (changedFilterType === 'cities') {
        updateRoomSelector(hierarchy);
        updateServerSelector(hierarchy);
    } else if (changedFilterType === 'rooms') {
        updateServerSelector(hierarchy);
    }
}
