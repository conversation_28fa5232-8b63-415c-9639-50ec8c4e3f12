// 主应用逻辑，页面初始化，事件绑定

// 全局变量
let data = [];
let filteredData = [];
let currentPage = 1;
let pageSize = 50;
let editingIndex = -1;
let selectedRows = new Set();
let selectedCells = new Set(); // 存储选中的单元格 "rowIndex-cellIndex"
let currentSelectedCell = null; // 当前选中的单元格 {row: index, cell: index, element: dom}
let selectedFilters = {
    provinces: new Set(),
    cities: new Set(),
    rooms: new Set(),
    servers: new Set()
};

// 排序状态管理
let sortState = {
    column: null,    // 当前排序的列名
    direction: null  // 排序方向: 'asc', 'desc', null
};
let lastSelectedIndex = -1; // 记录上次选择的索引，用于Shift多选

// 拖动手柄相关变量
let isDraggingHandle = false;
let dragStartCell = null;
let dragCurrentCell = null;
let dragPreviewCells = new Set();

// 单元格选择相关变量
let isSelectingCells = false;
let selectionStartCell = null;
let selectedCellRange = null; // {startRow, endRow, startCol, endCol}
let lastSelectedFilters = {
    provinces: -1,
    cities: -1,
    rooms: -1,
    servers: -1
}; // 记录各层级上次选择的索引，用于Shift多选

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 应用初始化
function initializeApp() {
    console.log('初始化Task Config Editor...');
    
    // 初始化事件监听器
    setupEventListeners();
    
    // 初始化拖拽上传
    setupDragAndDrop();

    // 尝试从本地存储加载数据
    if (!loadFromLocalStorage()) {
        console.log('未找到本地存储数据，等待用户上传文件');
    }
    
    console.log('Task Config Editor初始化完成');
}

// 设置事件监听器
function setupEventListeners() {
    // 文件上传
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }
    
    // 表单提交
    const editForm = document.getElementById('editForm');
    if (editForm) {
        editForm.addEventListener('submit', handleFormSubmit);
    }
    
    // 批量编辑表单
    const batchEditForm = document.getElementById('batchEditForm');
    if (batchEditForm) {
        batchEditForm.addEventListener('submit', handleBatchEdit);
    }
    
    // 搜索框
    const searchIP = document.getElementById('searchIP');
    const searchTask = document.getElementById('searchTask');
    if (searchIP) searchIP.addEventListener('input', filterData);
    if (searchTask) searchTask.addEventListener('input', filterData);
    
    // 分页控件
    const pageSize = document.getElementById('pageSize');
    if (pageSize) pageSize.addEventListener('change', changePageSize);
    
    // 全选复选框
    const selectAll = document.getElementById('selectAll');
    if (selectAll) selectAll.addEventListener('change', toggleSelectAll);
    
    // 批量编辑字段控制
    setupBatchFieldControls();
    
    // 模态框外部点击关闭
    setupModalCloseEvents();
    
    // 键盘快捷键
    setupKeyboardShortcuts();

    // 设置表头排序事件
    setupHeaderSortEvents();

    // 定期保存到本地存储
    setupAutoSave();
}

// 设置批量编辑字段控制
function setupBatchFieldControls() {
    const batchFields = ['hypervisor', 'v4_task_name', 'v6_task_name', 'v4_limit_rate', 'v6_limit_rate',
                       'v4_replica', 'v6_replica', 'v4_download_times', 'v4_max_time', 'v4_max_retries',
                       'v4_connect_timeout', 'v6_download_times', 'v6_max_time', 'v6_max_retries',
                       'v6_connect_timeout', 'local_conf_server', 'notes'];
    
    batchFields.forEach(field => {
        const checkbox = document.getElementById(`batch_${field}_check`);
        const input = document.getElementById(`batch_${field}`);
        
        if (checkbox && input) {
            checkbox.addEventListener('change', function() {
                input.disabled = !this.checked;
                if (!this.checked) {
                    input.value = '';
                }
            });
        }
    });
}

// 设置模态框关闭事件
function setupModalCloseEvents() {
    window.addEventListener('click', function(event) {
        const editModal = document.getElementById('editModal');
        const batchEditModal = document.getElementById('batchEditModal');
        
        if (event.target === editModal) {
            closeModal();
        }
        if (event.target === batchEditModal) {
            closeBatchEditModal();
        }
    });
}

// 设置键盘快捷键
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Ctrl+S 保存到本地存储
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            saveToLocalStorage();
            showNotification('配置已保存到本地存储');
        }
        
        // Ctrl+O 打开文件选择
        if (event.ctrlKey && event.key === 'o') {
            event.preventDefault();
            document.getElementById('fileInput').click();
        }
        
        // Ctrl+E 导出CSV
        if (event.ctrlKey && event.key === 'e') {
            event.preventDefault();
            exportCSV();
        }
        
        // Ctrl+A 或 Cmd+A 全选当前页
        if ((event.ctrlKey || event.metaKey) && event.key === 'a' && event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {
            event.preventDefault();
            // 直接调用全选所有数据的函数
            selectAllRows();
        }
        
        // ESC 关闭模态框
        if (event.key === 'Escape') {
            closeModal();
            closeBatchEditModal();
        }
        
        // Delete 删除选中行
        if (event.key === 'Delete' && selectedRows.size > 0 && event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {
            event.preventDefault();
            batchDelete();
        }

        // Ctrl+C 或 Cmd+C 复制选中记录为JSON
        if ((event.ctrlKey || event.metaKey) && event.key === 'c' && event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {
            event.preventDefault();
            copySelectedRecordsAsJSON();
        }
    });
}

// 设置表头排序事件
function setupHeaderSortEvents() {
    const sortableHeaders = document.querySelectorAll('.sortable-header');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            const column = this.dataset.column;
            if (column) {
                handleHeaderClick(column);
            }
        });
    });
}

// 设置自动保存
function setupAutoSave() {
    // 每5分钟自动保存一次
    setInterval(function() {
        if (data.length > 0) {
            saveToLocalStorage();
            console.log('自动保存完成');
        }
    }, 5 * 60 * 1000);
}

// 模态框控制
function closeModal() {
    const editModal = document.getElementById('editModal');
    if (editModal) {
        editModal.style.display = 'none';
        // 移除剪贴板监听器
        removeAddModalClipboardListener();
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 构建层级选择器
function buildHierarchySelectors() {
    const hierarchy = buildHierarchyData();
    
    // 构建省份选择器
    buildProvinceSelector(hierarchy);
    
    // 构建其他层级选择器
    updateCitySelector(hierarchy);
    updateRoomSelector(hierarchy);
    updateServerSelector(hierarchy);
    
    // 更新视觉状态
    updateSelectorVisualState();
}

// 构建省份选择器
function buildProvinceSelector(hierarchy) {
    const selector = document.getElementById('provinceSelector');
    if (!selector) return;
    
    selector.innerHTML = '<h4>省份</h4>';
    
    const provinces = Object.keys(hierarchy).sort();
    provinces.forEach((province, index) => {
        const item = document.createElement('div');
        item.className = 'level-item';
        item.innerHTML = `
            <input type="checkbox" id="province_${index}">
            <span>${province}</span>
        `;
        
        item.addEventListener('click', function(event) {
            if (event.target.type !== 'checkbox') {
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
            }
            handleFilterClick('provinces', province, index, event);
        });
        
        selector.appendChild(item);
    });
}

// 更新城市选择器
function updateCitySelector(hierarchy) {
    const selector = document.getElementById('citySelector');
    if (!selector) return;
    
    selector.innerHTML = '<h4>城市</h4>';
    
    const cities = new Set();
    
    // 如果选择了省份，只显示选中省份的城市
    if (selectedFilters.provinces.size > 0) {
        selectedFilters.provinces.forEach(province => {
            if (hierarchy[province]) {
                Object.keys(hierarchy[province]).forEach(city => cities.add(city));
            }
        });
    } else {
        // 显示所有城市
        Object.values(hierarchy).forEach(provinceData => {
            Object.keys(provinceData).forEach(city => cities.add(city));
        });
    }
    
    const sortedCities = Array.from(cities).sort();
    sortedCities.forEach((city, index) => {
        const item = document.createElement('div');
        item.className = 'level-item';
        item.innerHTML = `
            <input type="checkbox" id="city_${index}">
            <span>${city}</span>
        `;
        
        item.addEventListener('click', function(event) {
            if (event.target.type !== 'checkbox') {
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
            }
            handleFilterClick('cities', city, index, event);
        });
        
        selector.appendChild(item);
    });
}

// 更新机房选择器
function updateRoomSelector(hierarchy) {
    const selector = document.getElementById('roomSelector');
    if (!selector) return;
    
    selector.innerHTML = '<h4>机房</h4>';
    
    const rooms = new Set();
    
    // 根据选中的省份和城市过滤机房
    if (selectedFilters.provinces.size > 0 || selectedFilters.cities.size > 0) {
        Object.keys(hierarchy).forEach(province => {
            if (selectedFilters.provinces.size === 0 || selectedFilters.provinces.has(province)) {
                Object.keys(hierarchy[province]).forEach(city => {
                    if (selectedFilters.cities.size === 0 || selectedFilters.cities.has(city)) {
                        Object.keys(hierarchy[province][city]).forEach(room => rooms.add(room));
                    }
                });
            }
        });
    } else {
        // 显示所有机房
        Object.values(hierarchy).forEach(provinceData => {
            Object.values(provinceData).forEach(cityData => {
                Object.keys(cityData).forEach(room => rooms.add(room));
            });
        });
    }
    
    const sortedRooms = Array.from(rooms).sort();
    sortedRooms.forEach((room, index) => {
        const item = document.createElement('div');
        item.className = 'level-item';
        item.innerHTML = `
            <input type="checkbox" id="room_${index}">
            <span>${room}</span>
        `;
        
        item.addEventListener('click', function(event) {
            if (event.target.type !== 'checkbox') {
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
            }
            handleFilterClick('rooms', room, index, event);
        });
        
        selector.appendChild(item);
    });
}

// 更新服务器选择器
function updateServerSelector(hierarchy) {
    const selector = document.getElementById('serverSelector');
    if (!selector) return;
    
    selector.innerHTML = '<h4>服务器</h4>';
    
    const servers = new Set();
    
    // 根据选中的省份、城市和机房过滤服务器
    Object.keys(hierarchy).forEach(province => {
        if (selectedFilters.provinces.size === 0 || selectedFilters.provinces.has(province)) {
            Object.keys(hierarchy[province]).forEach(city => {
                if (selectedFilters.cities.size === 0 || selectedFilters.cities.has(city)) {
                    Object.keys(hierarchy[province][city]).forEach(room => {
                        if (selectedFilters.rooms.size === 0 || selectedFilters.rooms.has(room)) {
                            hierarchy[province][city][room].forEach(server => servers.add(server));
                        }
                    });
                }
            });
        }
    });
    
    const sortedServers = Array.from(servers).sort();
    sortedServers.forEach((server, index) => {
        const item = document.createElement('div');
        item.className = 'level-item';
        item.innerHTML = `
            <input type="checkbox" id="server_${index}">
            <span>${server}</span>
        `;
        
        item.addEventListener('click', function(event) {
            if (event.target.type !== 'checkbox') {
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
            }
            handleFilterClick('servers', server, index, event);
        });
        
        selector.appendChild(item);
    });
}

// 切换过滤器
function toggleFilter(filterType, item) {
    if (selectedFilters[filterType].has(item)) {
        selectedFilters[filterType].delete(item);
    } else {
        selectedFilters[filterType].add(item);
    }

    // 重新构建层级选择器和过滤数据
    const hierarchy = buildHierarchyData();
    updateFilterSelectors(hierarchy, filterType);
    updateSelectorVisualState();
    filterData();
    updateSelectedCount();
}

// 更新选择器视觉状态
function updateSelectorVisualState() {
    // 更新省份选择器的视觉状态
    const provinceItems = document.querySelectorAll('#provinceSelector .level-item');
    provinceItems.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        const span = item.querySelector('span');
        if (checkbox && span) {
            const value = span.textContent;
            const isSelected = selectedFilters.provinces.has(value);
            checkbox.checked = isSelected;
            if (isSelected) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }
    });

    // 更新城市选择器的视觉状态
    const cityItems = document.querySelectorAll('#citySelector .level-item');
    cityItems.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        const span = item.querySelector('span');
        if (checkbox && span) {
            const value = span.textContent;
            const isSelected = selectedFilters.cities.has(value);
            checkbox.checked = isSelected;
            if (isSelected) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }
    });

    // 更新机房选择器的视觉状态
    const roomItems = document.querySelectorAll('#roomSelector .level-item');
    roomItems.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        const span = item.querySelector('span');
        if (checkbox && span) {
            const value = span.textContent;
            const isSelected = selectedFilters.rooms.has(value);
            checkbox.checked = isSelected;
            if (isSelected) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }
    });

    // 更新服务器选择器的视觉状态
    const serverItems = document.querySelectorAll('#serverSelector .level-item');
    serverItems.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        const span = item.querySelector('span');
        if (checkbox && span) {
            const value = span.textContent;
            const isSelected = selectedFilters.servers.has(value);
            checkbox.checked = isSelected;
            if (isSelected) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }
    });
}

// 应用启动时的初始化
window.addEventListener('load', function() {
    console.log('页面加载完成，开始最终初始化...');

    // 初始化全局拖放功能
    initGlobalDragDrop();

    // 如果有数据，渲染表格
    if (data.length > 0) {
        buildHierarchySelectors();
        filterData();
    }

    console.log('应用初始化完成');
});

// 全局拖放功能初始化
function initGlobalDragDrop() {
    const overlay = document.getElementById('globalDropOverlay');
    const dropBox = document.querySelector('.drop-zone-box');
    let dragCounter = 0; // 用于跟踪拖拽事件的计数器
    let autoCloseTimer = null; // 自动关闭定时器
    let dragStateMonitor = null; // 拖拽状态监控定时器
    let lastDragActivity = 0; // 最后一次拖拽活动时间

    // 监听整个文档的拖拽事件
    document.addEventListener('dragenter', function(e) {
        e.preventDefault();
        dragCounter++;
        lastDragActivity = Date.now(); // 更新最后活动时间

        // 只在第一次进入时显示浮层
        if (dragCounter === 1) {
            overlay.classList.add('show');

            // 启动自动关闭定时器（10秒后自动关闭）
            if (autoCloseTimer) {
                clearTimeout(autoCloseTimer);
            }
            autoCloseTimer = setTimeout(() => {
                console.log('Auto-closing drop overlay after timeout');
                forceCloseDropOverlay();
            }, 10000);

            // 启动定期检查，每500ms检查一次拖拽状态
            startDragStateMonitor();
        }
    });

    document.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
        lastDragActivity = Date.now(); // 更新最后活动时间
    });

    document.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dragCounter--;
        lastDragActivity = Date.now(); // 更新最后活动时间

        // 当计数器为0时隐藏浮层
        if (dragCounter === 0) {
            overlay.classList.remove('show');
            dropBox.classList.remove('drag-over');
            stopDragStateMonitor();
        }
    });

    // 监听全局的dragend事件，确保拖拽结束时重置状态
    document.addEventListener('dragend', function(e) {
        console.log('Drag ended, force closing overlay');
        forceCloseDropOverlay();
    });

    // 监听鼠标释放事件，作为额外的安全措施
    let isDraggingFile = false;
    let dragLeaveTimer = null;

    document.addEventListener('dragstart', function(e) {
        // 检查是否是文件拖拽
        if (e.dataTransfer && e.dataTransfer.types.includes('Files')) {
            isDraggingFile = true;
        }
    });

    document.addEventListener('mouseup', function(e) {
        // 如果正在拖拽文件且鼠标释放，重置状态
        if (isDraggingFile) {
            console.log('Mouse up during file drag, force closing overlay');
            setTimeout(() => {
                forceCloseDropOverlay();
                isDraggingFile = false;
            }, 100);
        }
    });

    // 监听页面失去焦点，可能表示拖拽到了外部
    window.addEventListener('blur', function(e) {
        if (isDraggingFile || overlay.classList.contains('show')) {
            console.log('Window blur during drag, scheduling close');
            setTimeout(() => {
                if (overlay.classList.contains('show')) {
                    forceCloseDropOverlay();
                }
            }, 500);
        }
    });

    // 监听页面重新获得焦点
    window.addEventListener('focus', function(e) {
        // 如果页面重新获得焦点但没有拖拽活动，关闭遮罩
        setTimeout(() => {
            if (overlay.classList.contains('show') && !isDraggingFile) {
                console.log('Window focus without active drag, closing overlay');
                forceCloseDropOverlay();
            }
        }, 200);
    });

    // 拖拽框的特殊处理
    dropBox.addEventListener('dragenter', function(e) {
        e.preventDefault();
        dropBox.classList.add('drag-over');
    });

    dropBox.addEventListener('dragleave', function(e) {
        e.preventDefault();
        // 只有当离开的是dropBox本身时才移除样式
        if (!dropBox.contains(e.relatedTarget)) {
            dropBox.classList.remove('drag-over');
        }
    });

    // 处理文件放置
    document.addEventListener('drop', function(e) {
        console.log('Drop event triggered');
        e.preventDefault();
        e.stopPropagation();

        // 强制关闭遮罩
        forceCloseDropOverlay();

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            const fileExtension = file.name.split('.').pop().toLowerCase();

            console.log('File dropped:', file.name, 'Type:', fileExtension);

            if (fileExtension === 'csv') {
                handleCSVFile(file);
            } else {
                alert('请拖放CSV文件！当前文件类型：' + fileExtension);
            }
        }
    });

    // 在拖放框上也添加drop事件处理
    dropBox.addEventListener('drop', function(e) {
        console.log('Drop event on dropBox triggered');
        e.preventDefault();
        e.stopPropagation();

        // 强制关闭遮罩
        forceCloseDropOverlay();

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            const fileExtension = file.name.split('.').pop().toLowerCase();

            console.log('File dropped on dropBox:', file.name, 'Type:', fileExtension);

            if (fileExtension === 'csv') {
                handleCSVFile(file);
            } else {
                alert('请拖放CSV文件！当前文件类型：' + fileExtension);
            }
        }
    });

    // 强制关闭遮罩的函数
    function forceCloseDropOverlay() {
        console.log('Force closing drop overlay');

        // 清除自动关闭定时器
        if (autoCloseTimer) {
            clearTimeout(autoCloseTimer);
            autoCloseTimer = null;
        }

        // 重置拖拽计数器
        dragCounter = 0;

        // 移除遮罩显示
        overlay.classList.remove('show');
        dropBox.classList.remove('drag-over');

        // 移除页面上所有可能的拖拽样式
        document.body.classList.remove('drag-over');
        document.documentElement.classList.remove('drag-over');

        // 查找并移除所有带有drag-over类的元素
        const dragOverElements = document.querySelectorAll('.drag-over');
        dragOverElements.forEach(element => {
            element.classList.remove('drag-over');
        });

        // 强制隐藏遮罩，使用多种方法确保可靠性
        overlay.style.display = 'none';
        overlay.style.visibility = 'hidden';
        overlay.style.opacity = '0';

        // 延迟重置样式，确保遮罩完全隐藏
        setTimeout(() => {
            overlay.style.display = '';
            overlay.style.visibility = '';
            overlay.style.opacity = '';
        }, 200);

        // 重置文件拖拽状态
        isDraggingFile = false;

        // 停止拖拽状态监控
        stopDragStateMonitor();
    }

    // 启动拖拽状态监控
    function startDragStateMonitor() {
        if (dragStateMonitor) {
            clearInterval(dragStateMonitor);
        }

        dragStateMonitor = setInterval(() => {
            const now = Date.now();
            const timeSinceLastActivity = now - lastDragActivity;

            // 如果超过2秒没有拖拽活动，且遮罩仍然显示，则关闭遮罩
            if (timeSinceLastActivity > 2000 && overlay.classList.contains('show')) {
                console.log('No drag activity for 2 seconds, closing overlay');
                forceCloseDropOverlay();
            }
        }, 500);
    }

    // 停止拖拽状态监控
    function stopDragStateMonitor() {
        if (dragStateMonitor) {
            clearInterval(dragStateMonitor);
            dragStateMonitor = null;
        }
    }

    // 点击浮层背景关闭
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            dragCounter = 0;
            overlay.classList.remove('show');
            dropBox.classList.remove('drag-over');
        }
    });
}

// 处理CSV文件
function handleCSVFile(file) {
    console.log('Handling CSV file:', file.name);

    const reader = new FileReader();
    reader.onload = function(e) {
        const content = e.target.result;
        try {
            data = parseCSV(content);
            buildHierarchySelectors();
            filterData();
            console.log('CSV文件加载成功，共', data.length, '条记录');

            // 显示成功消息
            alert(`CSV文件"${file.name}"加载成功！共加载 ${data.length} 条记录。`);

            // 多次调整侧边栏高度，确保在不同时机都能正确调整
            setTimeout(() => {
                if (typeof adjustSidebarHeight === 'function') {
                    adjustSidebarHeight();
                }
            }, 100);

            setTimeout(() => {
                if (typeof adjustSidebarHeight === 'function') {
                    adjustSidebarHeight();
                }
            }, 500);

            setTimeout(() => {
                if (typeof adjustSidebarHeight === 'function') {
                    adjustSidebarHeight();
                }
            }, 1000);

        } catch (error) {
            console.error('CSV文件解析失败:', error);
            alert('CSV文件解析失败：' + error.message);
        }
    };
    reader.readAsText(file);
}

// 复制选中记录为JSON格式
function copySelectedRecordsAsJSON() {
    // 检查是否有选中的记录
    if (selectedRows.size === 0) {
        showNotification('请先选择要复制的记录', 'warning');
        return;
    }

    try {
        // 获取选中的记录数据
        const selectedRecords = [];
        const selectedIndices = Array.from(selectedRows).sort((a, b) => a - b);

        selectedIndices.forEach(index => {
            if (index < filteredData.length) {
                const record = filteredData[index];
                // 创建记录的完整副本
                const recordCopy = { ...record };
                selectedRecords.push(recordCopy);
            }
        });

        // 转换为格式化的JSON字符串
        const jsonString = JSON.stringify(selectedRecords, null, 2);

        console.log(`准备复制 ${selectedRecords.length} 条记录到剪贴板:`, selectedRecords);

        // 尝试使用现代Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(jsonString).then(() => {
                showNotification(`已复制 ${selectedRecords.length} 条记录到剪贴板 (JSON格式)`, 'success');
                console.log('使用Clipboard API复制成功');
            }).catch(err => {
                console.error('Clipboard API复制失败:', err);
                // 回退到传统方法
                fallbackCopyToClipboard(jsonString, selectedRecords.length);
            });
        } else {
            // 回退到传统方法
            fallbackCopyToClipboard(jsonString, selectedRecords.length);
        }

    } catch (error) {
        console.error('复制记录时发生错误:', error);
        showNotification('复制失败：' + error.message, 'error');
    }
}

// 回退的剪贴板复制方法
function fallbackCopyToClipboard(text, recordCount) {
    try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // 选择并复制文本
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showNotification(`已复制 ${recordCount} 条记录到剪贴板 (JSON格式)`, 'success');
            console.log('使用fallback方法复制成功');
        } else {
            throw new Error('execCommand复制失败');
        }

    } catch (error) {
        console.error('Fallback复制方法也失败:', error);
        showNotification('复制失败，请手动复制数据', 'error');

        // 最后的备选方案：在控制台显示数据
        console.log('复制失败，以下是要复制的JSON数据:');
        console.log(text);
    }
}
