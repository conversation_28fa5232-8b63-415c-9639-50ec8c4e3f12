/* 科技商务风格配色方案 */

:root {
    /* 主色调 - 深蓝科技色系 */
    --primary-dark: #1a2332;
    --primary-main: #2d3748;
    --primary-light: #4a5568;
    --primary-lighter: #718096;

    /* 辅助色 - 银灰商务色系 */
    --secondary-dark: #2d3748;
    --secondary-main: #4a5568;
    --secondary-light: #a0aec0;
    --secondary-lighter: #e2e8f0;

    /* 背景色系 */
    --bg-primary: #f7fafc;
    --bg-secondary: #edf2f7;
    --bg-tertiary: #ffffff;
    --bg-overlay: rgba(26, 35, 50, 0.95);

    /* 文字色系 */
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-tertiary: #718096;
    --text-inverse: #ffffff;

    /* 功能色系 - 柔和自然 */
    --accent-blue: #3182ce;
    --accent-blue-light: #63b3ed;
    --accent-success: #38a169;
    --accent-success-light: #68d391;
    --accent-warning: #d69e2e;
    --accent-warning-light: #f6e05e;
    --accent-danger: #e53e3e;
    --accent-danger-light: #fc8181;

    /* V4/V6 专用配色 - 和谐搭配 */
    --v4-primary: #dc783c;    /* 深橙色 */
    --v4-light: #fed7aa;      /* 浅橙色背景 */
    --v4-border: #ea580c;     /* 橙色边框 */
    --v6-primary: #9b59b6;    /* 深紫色 */
    --v6-light: #f4f0ff;      /* 浅紫色背景 */
    --v6-border: #8e44ad;     /* 紫色边框 */

    /* 边框和分割线 */
    --border-light: #e2e8f0;
    --border-main: #cbd5e0;
    --border-dark: #a0aec0;

    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    padding: 20px;
    line-height: 1.6;
    color: var(--text-primary);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-tertiary);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    border: 1px solid var(--border-light);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-main) 100%);
    color: var(--text-inverse);
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
    pointer-events: none;
}

.header h1 {
    font-size: 28px;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 按钮样式 - 科技商务风格 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-light) 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2c5aa0 0%, var(--accent-blue) 100%);
    box-shadow: 0 8px 25px rgba(49, 130, 206, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-success) 0%, var(--accent-success-light) 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-success:hover {
    background: linear-gradient(135deg, #2f855a 0%, var(--accent-success) 100%);
    box-shadow: 0 8px 25px rgba(56, 161, 105, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--accent-danger) 0%, var(--accent-danger-light) 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c53030 0%, var(--accent-danger) 100%);
    box-shadow: 0 8px 25px rgba(229, 62, 62, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, var(--accent-warning) 0%, var(--accent-warning-light) 100%);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #b7791f 0%, var(--accent-warning) 100%);
    box-shadow: 0 8px 25px rgba(214, 158, 46, 0.3);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 过滤区域 - 科技商务风格 */
.filter-section {
    padding: 24px;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    border-bottom: 1px solid var(--border-light);
    position: relative;
}

.filter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-blue) 0%, var(--accent-blue-light) 100%);
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    letter-spacing: 0.3px;
}

.filter-group input[type="text"] {
    padding: 12px 16px;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 14px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-group input[type="text"]:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
    transform: translateY(-1px);
}

/* 层级选择器 - 现代化设计 */
.hypervisor-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 16px;
}

.level-selector {
    border: 2px solid var(--border-light);
    border-radius: 12px;
    background: var(--bg-tertiary);
    max-height: 220px;
    overflow-y: auto;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.level-selector:hover {
    border-color: var(--border-main);
    box-shadow: var(--shadow-md);
}

.level-selector h4 {
    background: rgba(45, 55, 72, 0.85);
    backdrop-filter: blur(8px);
    color: var(--text-inverse);
    padding: 8px 12px;
    margin: 0;
    font-size: 11px;
    font-weight: 700;
    position: sticky;
    top: 0;
    z-index: 1;
    letter-spacing: 0.6px;
    text-transform: uppercase;
    border-radius: 12px 12px 0 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.22);
    height: 28px;
    display: flex;
    align-items: center;
}

.level-item {
    padding: 4px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    height: 28px;
    font-size: 11px;
    line-height: 1.2;
}

.level-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-light) 100%);
    transition: width 0.3s ease;
}

.level-item:hover {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    transform: translateX(4px);
}

.level-item:hover::before {
    width: 3px;
}

.level-item.selected {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.1) 0%, rgba(99, 179, 237, 0.1) 100%);
    color: var(--accent-blue);
    font-weight: 600;
    border-left: 3px solid var(--accent-blue);
}

.level-item input[type="checkbox"] {
    margin: 0;
    width: 14px;
    height: 14px;
    accent-color: var(--accent-blue);
}

.level-item span {
    flex: 1;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
}

.filter-actions {
    margin-top: 16px;
    display: flex;
    align-items: stretch;
    gap: 12px;
}

.selected-count {
    flex: 1;
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--secondary-lighter) 0%, var(--bg-tertiary) 100%);
    border-radius: 8px;
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 600;
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
}

/* 清除过滤按钮 - 与页面整体风格统一 */
.filter-actions .btn-clear-filters {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    font-size: 13px;
    font-weight: 600;
    border-radius: 8px;
    min-width: 120px;
    position: relative;
    overflow: hidden;

    /* 使用与其他按钮一致的样式 */
    background: linear-gradient(135deg, var(--accent-danger) 0%, #c53030 100%);
    color: var(--text-inverse);
    border: 1px solid #c53030;
    box-shadow: var(--shadow-sm);

    transition: all 0.3s ease;
    letter-spacing: 0.5px;
}

/* 添加光泽效果 */
.filter-actions .btn-clear-filters::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.filter-actions .btn-clear-filters:hover::before {
    left: 100%;
}

.filter-actions .btn-clear-filters:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #c53030 0%, var(--accent-danger) 100%);
}

.filter-actions .btn-clear-filters:active {
    transform: translateY(0);
}

.filter-actions .btn-clear-filters svg {
    opacity: 0.9;
    transition: transform 0.2s ease;
}

.filter-actions .btn-clear-filters:hover svg {
    transform: rotate(90deg);
}

/* 批量操作控制栏 - 与表头一致的样式和高度 */
.batch-controls {
    /* 使用与表头一致的背景透明度和模糊效果 */
    background: rgba(45, 55, 72, 0.85) !important;
    backdrop-filter: blur(8px);
    color: var(--text-inverse);
    padding: 0 24px;
    align-items: center;
    gap: 20px;
    margin-top: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.22);
    position: relative;
    overflow: hidden;

    /* 平滑的高度和透明度过渡 - 压缩高度与表头一致 */
    max-height: 0;
    opacity: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;

    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-8px);
}

.batch-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-blue-light) 0%, var(--accent-success-light) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.batch-controls.show {
    display: flex;
    /* 压缩高度，与表头行高度保持一致 */
    max-height: 48px;
    opacity: 1;
    padding-top: 12px;
    padding-bottom: 12px;
    margin-top: 12px;
    margin-bottom: 0;
    transform: translateY(0);
}

.batch-controls.show::before {
    opacity: 1;
}

.batch-info {
    font-weight: 700;
    flex: 1;
    /* 压缩字体大小，与表头保持一致 */
    font-size: 12px;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

/* 批量控制栏中的按钮样式 - 紧凑设计 */
.batch-controls .btn {
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 6px;
    min-height: 24px;
}

.batch-controls .btn:hover {
    transform: translateY(-1px);
}

/* 表格容器 - 现代化滚动 */
.table-container {
    overflow-x: auto;
    height: 650px;
    overflow-y: auto;
    border-radius: 8px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    position: relative;
}

.table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--secondary-light) 0%, var(--secondary-main) 100%);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary-main) 0%, var(--primary-light) 100%);
}

/* 分页控件 - 浅色协调主题 */
.pagination {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 12px 12px;
    flex-wrap: wrap;
    position: relative;
}

/* 分页区域的微光效果 */
.pagination::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
    pointer-events: none;
}

/* 分页按钮容器 */
.pagination-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 分页按钮样式 - 浅色协调主题 */
.pagination .btn {
    padding: 8px 16px;
    font-size: 13px;
    min-width: 80px;
    border: 1px solid #c8d3e0;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination .btn:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    border-color: #adb5bd;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    transform: translateY(-1px);
}

.pagination .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.pagination .btn:disabled {
    background: linear-gradient(135deg, #dee2e6 0%, #ced4da 100%);
    color: #6c757d;
    border-color: #dee2e6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    text-shadow: none;
}

.pagination .btn:disabled:hover {
    background: linear-gradient(135deg, #dee2e6 0%, #ced4da 100%);
    transform: none;
    box-shadow: none;
}

/* 分页信息文本 - 浅色主题 */
.pagination #pageInfo {
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
    min-width: 120px;
    text-align: center;
    font-size: 13px;
}

/* 分页选择器 - 浅色主题 */
.pagination select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 13px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 100px;
    cursor: pointer;
}

.pagination select:hover {
    border-color: #adb5bd;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.pagination select:focus {
    outline: none;
    border-color: #6c757d;
    box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.2);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* 选择器选项样式 */
.pagination select option {
    background: #ffffff;
    color: #495057;
    padding: 8px;
}

/* 分页按钮的特殊状态 - 当前页面指示 */
.pagination .btn.current-page {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #0056b3;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.3);
}

/* 分页区域的内边框效果 */
.pagination::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
    pointer-events: none;
}

/* 分页控件的整体阴影效果 */
.pagination {
    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 -2px 8px rgba(0, 0, 0, 0.05);
}

/* 模态框 - 现代化设计 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    backdrop-filter: blur(8px);
    animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

.modal-content {
    background: var(--bg-tertiary);
    margin: 3% auto;
    padding: 32px;
    border-radius: 16px;
    width: 92%;
    max-width: 900px;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    border: 1px solid var(--border-light);
}

@keyframes slideIn {
    from {
        transform: translateY(-30px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-content h2 {
    margin-bottom: 24px;
    color: var(--text-primary);
    border-bottom: 3px solid var(--accent-blue);
    padding-bottom: 12px;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.3px;
    position: relative;
}

.modal-content h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue-light) 0%, var(--accent-blue) 100%);
    border-radius: 2px;
}

/* 表单样式 - 现代化设计 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    letter-spacing: 0.3px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 14px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
    transform: translateY(-1px);
}

.form-section {
    margin-bottom: 28px;
    padding: 20px;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: 12px;
    border-left: 4px solid var(--accent-blue);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-blue) 0%, transparent 100%);
}

.form-section h3 {
    margin-bottom: 18px;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 700;
    letter-spacing: -0.2px;
}

/* V4/V6 分组样式 - 和谐配色 */
.v4-group {
    border-left: 4px solid var(--v4-border);
    padding-left: 16px;
    background: linear-gradient(135deg, rgba(220, 120, 60, 0.06) 0%, transparent 100%);
    border-radius: 0 8px 8px 0;
    margin: 8px 0;
    padding: 12px 16px;
}

.v6-group {
    border-left: 4px solid var(--v6-border);
    padding-left: 16px;
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.06) 0%, transparent 100%);
    border-radius: 0 8px 8px 0;
    margin: 8px 0;
    padding: 12px 16px;
}

.v4-group label {
    color: var(--v4-primary);
    font-weight: 600;
}

.v6-group label {
    color: var(--v6-primary);
    font-weight: 600;
}

/* 通知样式 - 现代化设计 */
.notification {
    position: fixed;
    top: 24px;
    right: 24px;
    padding: 16px 24px;
    border-radius: 12px;
    color: var(--text-inverse);
    font-weight: 600;
    z-index: 2000;
    transform: translateX(400px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 300px;
}

.notification.show {
    transform: translateX(0);
    animation: notificationSlide 0.4s ease-out;
}

@keyframes notificationSlide {
    0% {
        transform: translateX(400px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-info {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-light) 100%);
}

.notification-success {
    background: linear-gradient(135deg, var(--accent-success) 0%, var(--accent-success-light) 100%);
}

.notification-warning {
    background: linear-gradient(135deg, var(--accent-warning) 0%, var(--accent-warning-light) 100%);
    color: var(--text-primary);
}

.notification-error {
    background: linear-gradient(135deg, var(--accent-danger) 0%, var(--accent-danger-light) 100%);
}

/* 拖拽上传样式 - 科技风格 */
.drag-over {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.1) 0%, rgba(99, 179, 237, 0.1) 100%);
    border: 3px dashed var(--accent-blue);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.drag-over::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(49, 130, 206, 0.1), transparent);
    animation: dragShimmer 2s infinite;
}

@keyframes dragShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 响应式设计 - 移动优先 */
@media (max-width: 768px) {
    body {
        padding: 12px;
    }

    .container {
        border-radius: 8px;
    }

    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 20px;
    }

    .header h1 {
        font-size: 24px;
    }

    .controls {
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    .btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .filter-section {
        padding: 16px;
    }

    .filter-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .filter-actions {
        flex-direction: column;
        gap: 8px;
    }

    .filter-actions .btn-clear-filters {
        min-width: auto;
        padding: 10px 14px;
        font-size: 12px;
    }

    .hypervisor-selector {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 2% auto;
        padding: 20px;
        width: 95%;
        border-radius: 12px;
    }

    .batch-controls {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .pagination {
        padding: 12px 16px;
        gap: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .pagination-controls {
        gap: 8px;
    }

    .pagination .btn {
        padding: 6px 12px;
        font-size: 12px;
        min-width: 60px;
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        border: 1px solid #c8d3e0;
    }

    .pagination #pageInfo {
        font-size: 12px;
        min-width: 100px;
        color: #495057;
    }

    .pagination select {
        font-size: 12px;
        padding: 6px 8px;
        min-width: 80px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        color: #495057;
        border: 1px solid #ced4da;
    }
}

/* 全局拖放浮层样式 */
.global-drop-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(2px);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.global-drop-overlay.show {
    display: flex;
}

.drop-zone-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 40px;
}

.drop-zone-box {
    border: 3px dashed rgba(147, 51, 234, 0.8);
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    padding: 60px 40px;
    text-align: center;
    min-width: 400px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.drop-zone-box.drag-over {
    border-color: rgba(147, 51, 234, 1);
    background: rgba(147, 51, 234, 0.2);
    transform: scale(1.05);
}

.drop-zone-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.8;
}

.drop-zone-text {
    font-size: 24px;
    font-weight: bold;
    color: white;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.drop-zone-subtext {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
