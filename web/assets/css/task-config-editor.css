* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: #2c3e50;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.controls {
    display: flex;
    gap: 10px;
}

.batch-controls {
    display: none;
    background: #34495e;
    color: white;
    padding: 15px 20px;
    align-items: center;
    gap: 15px;
}

.batch-controls.show {
    display: flex;
}

.batch-info {
    font-weight: bold;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

th input[type="checkbox"] {
    transform: scale(1.2);
}

td input[type="checkbox"] {
    transform: scale(1.1);
}

tr.selected {
    background-color: #e3f2fd !important;
}

.batch-edit-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.batch-edit-section h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.batch-field-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.batch-field-group input[type="checkbox"] {
    transform: scale(1.1);
}

.batch-field-group label {
    min-width: 150px;
    font-weight: normal;
}

.batch-field-group input[type="text"],
.batch-field-group input[type="number"] {
    flex: 1;
}

.filter-section {
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #2c3e50;
}

.hypervisor-selector {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.level-selector {
    border: 1px solid #ddd;
    border-radius: 4px;
    max-height: 150px;
    overflow-y: auto;
    background: white;
}

.level-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 8px;
}

.level-item:hover {
    background: #f0f0f0;
}

.level-item.selected {
    background: #e3f2fd;
    color: #1976d2;
}

input, select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.table-container {
    overflow-x: auto;
    max-height: 600px;
    overflow-y: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

th, td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    white-space: nowrap;
}

th {
    background: #34495e;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

/* V4字段背景色 */
td:nth-child(5), td:nth-child(7), td:nth-child(9), 
td:nth-child(11), td:nth-child(12), td:nth-child(13), td:nth-child(14) {
    background-color: #ebf3fd;
}

/* V6字段背景色 */
td:nth-child(6), td:nth-child(8), td:nth-child(10), 
td:nth-child(15), td:nth-child(16), td:nth-child(17), td:nth-child(18) {
    background-color: #f4f0ff;
}

/* 鼠标悬停时保持分组颜色 */
tr:hover td:nth-child(5), tr:hover td:nth-child(7), tr:hover td:nth-child(9), 
tr:hover td:nth-child(11), tr:hover td:nth-child(12), tr:hover td:nth-child(13), tr:hover td:nth-child(14) {
    background-color: #d6e9ff;
}

tr:hover td:nth-child(6), tr:hover td:nth-child(8), tr:hover td:nth-child(10), 
tr:hover td:nth-child(15), tr:hover td:nth-child(16), tr:hover td:nth-child(17), tr:hover td:nth-child(18) {
    background-color: #e8dcff;
}

/* 选中行时的颜色 */
tr.selected td:nth-child(5), tr.selected td:nth-child(7), tr.selected td:nth-child(9), 
tr.selected td:nth-child(11), tr.selected td:nth-child(12), tr.selected td:nth-child(13), tr.selected td:nth-child(14) {
    background-color: #b3d9ff !important;
}

tr.selected td:nth-child(6), tr.selected td:nth-child(8), tr.selected td:nth-child(10), 
tr.selected td:nth-child(15), tr.selected td:nth-child(16), tr.selected td:nth-child(17), tr.selected td:nth-child(18) {
    background-color: #d1c4ff !important;
}

tr:hover {
    background: #f5f5f5;
}

.actions {
    display: flex;
    gap: 5px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: bold;
    margin-bottom: 5px;
}

.pagination {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.selected-count {
    background: #e8f5e8;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    font-weight: bold;
}

.form-section {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: #fafafa;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.v4-group {
    background: #ebf3fd;
    border-left: 4px solid #3498db;
    padding-left: 10px;
}

.v6-group {
    background: #f4f0ff;
    border-left: 4px solid #9b59b6;
    padding-left: 10px;
}

.v4-group label {
    color: #2980b9;
    font-weight: bold;
}

.v6-group label {
    color: #8e44ad;
    font-weight: bold;
}
