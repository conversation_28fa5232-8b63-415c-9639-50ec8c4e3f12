/* 编辑器专用样式 - 科技商务风格 */

/* 表格样式 - 现代化设计 */
table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    background: var(--bg-tertiary);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    position: relative;
    /* 默认禁止文本选择，避免拖动时选中文字 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

th, td {
    padding: 4px 6px;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
    white-space: nowrap;
    vertical-align: middle;
    transition: all 0.2s ease;
    font-size: 11px;
    line-height: 1.2;
    height: 28px;
}

/* 添加thead样式确保固定效果 */
thead {
    position: sticky;
    top: 0;
    z-index: 15;
}

th {
    background: rgba(55, 65, 81, 0.95) !important;
    backdrop-filter: blur(12px);
    color: var(--text-inverse);
    position: sticky;
    top: 0;
    z-index: 10;
    font-weight: 700;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.6px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    border-right: 1px solid rgba(255, 255, 255, 0.15);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 第一行主标题保持深灰色背景 */
tr:first-child th {
    background: rgba(55, 65, 81, 0.95) !important;
    top: 0;
    z-index: 12;
}

/* 第二行表头固定位置 */
tr:nth-child(2) th {
    top: 28px; /* 第一行表头的高度 */
    z-index: 11;
}

/* 第二行V4子标题 - 现代蓝色系 */
tr:nth-child(2) th:nth-child(1), tr:nth-child(2) th:nth-child(3), tr:nth-child(2) th:nth-child(5),
tr:nth-child(2) th:nth-child(7), tr:nth-child(2) th:nth-child(8), tr:nth-child(2) th:nth-child(9),
tr:nth-child(2) th:nth-child(10) {
    background: rgba(49, 130, 206, 0.95) !important;
    backdrop-filter: blur(12px);
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* 第二行V6子标题 - 现代紫色系 */
tr:nth-child(2) th:nth-child(2), tr:nth-child(2) th:nth-child(4), tr:nth-child(2) th:nth-child(6),
tr:nth-child(2) th:nth-child(11), tr:nth-child(2) th:nth-child(12), tr:nth-child(2) th:nth-child(13),
tr:nth-child(2) th:nth-child(14) {
    background: rgba(155, 89, 182, 0.95) !important;
    backdrop-filter: blur(12px);
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Excel风格行号列 */
.row-header {
    background: rgba(55, 65, 81, 0.95) !important;
    color: var(--text-inverse);
    text-align: center;
    font-weight: 700;
    width: 50px;
    min-width: 50px;
    max-width: 50px;
    border-right: 2px solid rgba(255, 255, 255, 0.3) !important;
}

.row-number {
    background: rgba(55, 65, 81, 0.9) !important;
    color: var(--text-inverse);
    text-align: center;
    font-weight: 600;
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
    border-right: 2px solid rgba(255, 255, 255, 0.25) !important;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease, border-color 0.2s ease;
    position: relative;
    box-sizing: border-box !important;
    padding: 8px 4px !important;
    flex-shrink: 0 !important;
}

.row-number:hover {
    background: rgba(75, 85, 99, 0.95) !important;
    border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
}

/* 选中行的行号样式 */
tr.selected .row-number {
    background: rgba(49, 130, 206, 0.9) !important;
    border-right: 2px solid rgba(255, 255, 255, 0.5) !important;
    color: white;
    font-weight: 700;
}

/* 表格行选择样式 - Excel风格 */
tbody tr {
    transition: all 0.2s ease;
    cursor: pointer;
    user-select: none;
}

tbody tr:hover {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.08) 0%, rgba(99, 179, 237, 0.05) 100%);
}

tbody tr.selected {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.25) 0%, rgba(99, 179, 237, 0.2) 100%) !important;
}

/* 拖拽选择时的临时高亮 */
tbody tr.drag-selecting {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.3) 0%, rgba(99, 179, 237, 0.25) 100%) !important;
}

/* 行号拖拽选择时的样式 */
.row-number.drag-selecting {
    background: rgba(49, 130, 206, 0.7) !important;
    border-right: 2px solid rgba(255, 255, 255, 0.6) !important;
    color: white;
    font-weight: 700;
}

/* 拖拽过程中的行号高亮 */
.row-number.drag-hover {
    background: rgba(75, 85, 99, 0.98) !important;
    border-right: 2px solid rgba(255, 255, 255, 0.5) !important;
    transform: scale(1.02);
}

/* 禁用拖拽时的文本选择 */
.table-container.dragging {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 内联编辑样式 */
.cell-editing {
    position: relative;
    padding: 0 !important;
    border: 2px solid var(--accent-blue) !important;
    background: white !important;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
    border-radius: 3px;
}

.cell-editor {
    width: 100%;
    height: 100%;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 11px;
    font-family: inherit;
    font-weight: 400;
    line-height: 1.2;
    padding: 4px 6px;
    margin: 0;
    outline: none;
    border-radius: 0;
    z-index: 100;
    box-sizing: border-box;
    vertical-align: middle;
    resize: none;
    /* 编辑状态时允许文本选择 */
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

.cell-editor:focus {
    border-color: var(--accent-blue-light);
    box-shadow: 0 2px 12px rgba(37, 99, 235, 0.4);
}

/* 可编辑单元格的悬停效果 */
td.editable-cell {
    cursor: text;
    position: relative;
    /* 禁止文本选择，只有在编辑状态时才允许选择 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

td.editable-cell:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(59, 130, 246, 0.03) 100%) !important;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

/* 编辑指示器 */
td.editable-cell::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-top: 6px solid rgba(37, 99, 235, 0.3);
    opacity: 0;
    transition: opacity 0.2s ease;
}

td.editable-cell:hover::after {
    opacity: 1;
}

/* 单元格选中状态 */
.cell-selected {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(59, 130, 246, 0.1) 100%) !important;
    border: 2px solid var(--accent-blue) !important;
    position: relative;
    z-index: 10;
}

.cell-selected::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid var(--accent-blue);
    border-radius: 4px;
    pointer-events: none;
    z-index: 1;
}

/* 多单元格选择范围 */
.cell-in-range {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.12) 0%, rgba(59, 130, 246, 0.08) 100%) !important;
    border: 1px solid var(--accent-blue) !important;
    position: relative;
    z-index: 8;
}

/* 选择范围的边框效果 */
.cell-range-top {
    border-top: 2px solid var(--accent-blue) !important;
}

.cell-range-bottom {
    border-bottom: 2px solid var(--accent-blue) !important;
}

.cell-range-left {
    border-left: 2px solid var(--accent-blue) !important;
}

.cell-range-right {
    border-right: 2px solid var(--accent-blue) !important;
}

/* Excel风格拖动手柄 - 使用独立的元素而不是伪元素 */
.cell-drag-handle {
    position: absolute;
    bottom: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    background: var(--accent-blue);
    border: 2px solid white;
    border-radius: 50%;
    cursor: crosshair;
    z-index: 20;
    pointer-events: auto;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
}

.cell-drag-handle:hover {
    transform: scale(1.2);
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.4);
}

/* 拖动时跟随鼠标的手柄 */
.drag-handle-following {
    position: fixed;
    width: 8px;
    height: 8px;
    background: var(--accent-blue);
    border: 2px solid white;
    border-radius: 50%;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    transform: translate(-50%, -50%);
}

/* 拖动过程中的视觉反馈 */
.cell-drag-source {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.25) 0%, rgba(59, 130, 246, 0.15) 100%) !important;
    border: 2px solid var(--accent-blue) !important;
}

.cell-drag-target {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%) !important;
    border: 1px dashed var(--accent-blue) !important;
}

.cell-drag-preview {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%) !important;
    border: 2px solid var(--accent-blue) !important;
    opacity: 0.8;
}

/* 拖动填充范围的醒目边框 - 只显示外围边框 */
.cell-fill-preview {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(34, 197, 94, 0.1) 100%) !important;
    border: none !important;
}

/* 填充预览范围的外围边框 */
.cell-fill-preview.fill-top {
    border-top: 4px solid #10b981 !important;
}

.cell-fill-preview.fill-bottom {
    border-bottom: 4px solid #10b981 !important;
}

.cell-fill-preview.fill-left {
    border-left: 4px solid #10b981 !important;
}

.cell-fill-preview.fill-right {
    border-right: 4px solid #10b981 !important;
}

/* V4字段背景色 - 现代蓝色系 */
td:nth-child(4), td:nth-child(6), td:nth-child(8),
td:nth-child(10), td:nth-child(11), td:nth-child(12), td:nth-child(13) {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.12) 0%, rgba(99, 179, 237, 0.06) 100%);
    border-left: 2px solid rgba(49, 130, 206, 0.3);
}

/* V6字段背景色 - 现代紫色系 */
td:nth-child(5), td:nth-child(7), td:nth-child(9),
td:nth-child(14), td:nth-child(15), td:nth-child(16), td:nth-child(17) {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.12) 0%, rgba(142, 68, 173, 0.06) 100%);
    border-left: 2px solid rgba(155, 89, 182, 0.3);
}

/* 鼠标悬停时的自然过渡效果 */
tr:hover td:nth-child(4), tr:hover td:nth-child(6), tr:hover td:nth-child(8),
tr:hover td:nth-child(10), tr:hover td:nth-child(11), tr:hover td:nth-child(12), tr:hover td:nth-child(13) {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(59, 130, 246, 0.1) 100%);
    border-left: 2px solid rgba(37, 99, 235, 0.4);
}

tr:hover td:nth-child(6), tr:hover td:nth-child(8), tr:hover td:nth-child(10),
tr:hover td:nth-child(15), tr:hover td:nth-child(16), tr:hover td:nth-child(17), tr:hover td:nth-child(18) {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.15) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-left: 2px solid rgba(5, 150, 105, 0.4);
}



tr.selected td:nth-child(4), tr.selected td:nth-child(6), tr.selected td:nth-child(8),
tr.selected td:nth-child(10), tr.selected td:nth-child(11), tr.selected td:nth-child(12), tr.selected td:nth-child(13) {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.3) 0%, rgba(99, 179, 237, 0.2) 100%) !important;
    border-left: 3px solid rgba(49, 130, 206, 0.6) !important;
}

tr.selected td:nth-child(5), tr.selected td:nth-child(7), tr.selected td:nth-child(9),
tr.selected td:nth-child(14), tr.selected td:nth-child(15), tr.selected td:nth-child(16), tr.selected td:nth-child(17) {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.3) 0%, rgba(142, 68, 173, 0.2) 100%) !important;
    border-left: 3px solid rgba(155, 89, 182, 0.6) !important;
}

/* 行操作按钮 - 现代化设计 */
.actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

#csvContainer {
    display: block;
    padding: 0 24px 24px 24px; /* 与filter-section保持一致的左右padding */
}

/* 批量编辑表单样式 - 现代化设计 */
.batch-field-control {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    padding: 12px;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: 8px;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
}

.batch-field-control:hover {
    border-color: var(--border-main);
    box-shadow: var(--shadow-sm);
}

.batch-field-control input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: var(--accent-blue);
}

.batch-field-control label {
    margin: 0;
    font-weight: 600;
    min-width: 180px;
    color: var(--text-primary);
    font-size: 14px;
}

.batch-field-control input[type="text"],
.batch-field-control select {
    flex: 1;
    min-width: 220px;
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-weight: 500;
}

.batch-field-control input:disabled {
    background: var(--bg-secondary);
    color: var(--text-tertiary);
    cursor: not-allowed;
    opacity: 0.6;
}

/* 新的批量编辑模态框样式 - 紧凑现代商务风格 */
.batch-edit-modal {
    max-width: 800px;
    width: 85vw;
    max-height: 85vh;
    overflow-y: auto;
    padding: 20px;
}

.batch-edit-modal h2 {
    color: var(--primary-dark);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
    text-align: center;
    border-bottom: 2px solid var(--primary-light);
    padding-bottom: 8px;
}

/* 批量编辑区域样式 - 紧凑版 */
.batch-section {
    margin-bottom: 20px;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(30, 41, 59, 0.1);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.batch-section h3 {
    color: var(--primary-dark);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.batch-section h3::before {
    content: '';
    width: 3px;
    height: 16px;
    background: linear-gradient(135deg, var(--primary-main) 0%, var(--primary-light) 100%);
    border-radius: 2px;
}

/* V4/V6配置分栏布局 - 紧凑版 */
.config-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-top: 8px;
    width: 100%;
    box-sizing: border-box;
}

.config-column {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    padding: 12px;
    border: 1px solid rgba(30, 41, 59, 0.08);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    min-width: 0; /* 防止内容溢出 */
    box-sizing: border-box;
}

/* V4配置栏背景 - 与主表格V4列背景一致 */
.v4-column {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.12) 0%, rgba(255, 255, 255, 0.7) 100%);
    border: 1px solid rgba(49, 130, 206, 0.25);
}

/* V6配置栏背景 - 与主表格V6列背景一致 */
.v6-column {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.12) 0%, rgba(255, 255, 255, 0.7) 100%);
    border: 1px solid rgba(155, 89, 182, 0.25);
}

.config-column h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    text-align: center;
    padding: 6px 8px;
    border-radius: 4px;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* V4配置栏 - 与主表格V4子表头一致的蓝色 */
.v4-column h4 {
    background: rgba(49, 130, 206, 0.95);
    border: 1px solid rgba(49, 130, 206, 0.4);
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* V6配置栏 - 与主表格V6子表头一致的紫色 */
.v6-column h4 {
    background: rgba(155, 89, 182, 0.95);
    border: 1px solid rgba(155, 89, 182, 0.4);
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 批量编辑字段行样式 - 紧凑版 */
.batch-field-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
    border: 1px solid rgba(30, 41, 59, 0.05);
    transition: all 0.2s ease;
    min-width: 0; /* 防止内容溢出 */
}

.batch-field-row:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(30, 41, 59, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* 批量编辑字段元素样式 - 紧凑版 */
.batch-field-row input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-main);
    cursor: pointer;
    flex-shrink: 0;
}

.batch-field-row label {
    font-weight: 500;
    color: var(--primary-dark);
    min-width: 100px;
    font-size: 13px;
    cursor: pointer;
    flex-shrink: 0;
}

.batch-field-row input[type="text"],
.batch-field-row input[type="number"] {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid rgba(30, 41, 59, 0.2);
    border-radius: 3px;
    background: white;
    color: var(--primary-dark);
    font-size: 13px;
    transition: all 0.2s ease;
    min-width: 0; /* 防止溢出 */
}

.batch-field-row input[type="text"]:focus,
.batch-field-row input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-main);
    box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
}

.batch-field-row input:disabled {
    background: rgba(248, 250, 252, 0.8);
    color: rgba(30, 41, 59, 0.5);
    cursor: not-allowed;
    border-color: rgba(30, 41, 59, 0.1);
}

/* 通用配置字段布局 - 右对齐且占据可用空间 */
.general-section .batch-field-row {
    justify-content: space-between;
}

.general-section .batch-field-row label {
    flex-shrink: 0;
    margin-right: 12px;
    width: 140px;
    min-width: 140px;
}

/* 通用配置字段输入框宽度统一 - 占据剩余空间 */
#batch_hypervisor,
#batch_local_conf_server,
#batch_notes {
    flex: 1 !important;
    min-width: 0;
    max-width: none;
}

/* V4/V6配置字段布局 - 右对齐且占据可用空间 */
.config-section .batch-field-row {
    justify-content: space-between;
}

.config-section .batch-field-row label {
    flex-shrink: 0;
    margin-right: 8px;
    width: 120px;
    min-width: 120px;
}

/* V4/V6配置字段输入框宽度统一 - 占据剩余空间 */
.config-section .batch-field-row input[type="text"],
.config-section .batch-field-row input[type="number"] {
    flex: 1 !important;
    min-width: 0;
    max-width: none;
}

/* 底部按钮区域 - 紧凑版 */
.batch-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid rgba(30, 41, 59, 0.1);
}

.batch-buttons .btn {
    padding: 8px 24px;
    font-size: 13px;
    font-weight: 600;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.batch-buttons .btn-primary {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-blue-light) 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(49, 130, 206, 0.2);
}

.batch-buttons .btn-primary:hover {
    background: linear-gradient(135deg, #2c5aa0 0%, var(--accent-blue) 100%);
    box-shadow: 0 4px 8px rgba(49, 130, 206, 0.3);
    transform: translateY(-1px);
}

.batch-buttons .btn-secondary {
    background: rgba(248, 250, 252, 0.9);
    color: var(--primary-dark);
    border: 1px solid rgba(30, 41, 59, 0.2);
}

.batch-buttons .btn-secondary:hover {
    background: rgba(241, 245, 249, 0.9);
    border-color: rgba(30, 41, 59, 0.3);
    transform: translateY(-1px);
}

/* 表格行选择样式 - 科技风格 */
tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

tbody tr:hover {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.05) 0%, rgba(99, 179, 237, 0.03) 100%);
}



/* 复选框样式 - 现代化 */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--accent-blue);
    border-radius: 3px;
}

/* 全选复选框特殊样式 */
#selectAll {
    transform: scale(1.2);
    border: 2px solid var(--accent-blue);
}

#selectAll:indeterminate {
    accent-color: var(--accent-warning);
}

/* 表格头部工具栏 - 科技风格 */
.table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    border-bottom: 2px solid var(--border-light);
    border-radius: 8px 8px 0 0;
}

.table-actions {
    display: flex;
    gap: 12px;
}

/* 搜索高亮 - 科技风格 */
.search-highlight {
    background: linear-gradient(135deg, var(--accent-warning-light) 0%, #fef5e7 100%);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    color: var(--text-primary);
    box-shadow: 0 1px 3px rgba(214, 158, 46, 0.2);
}

/* 排序指示器 - 现代化 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all 0.3s ease;
    padding-right: 20px;
}

.sortable:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    transform: translateY(-1px);
}

.sortable::after {
    content: '↕';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.6;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.sortable.sort-asc::after {
    content: '↑';
    opacity: 1;
    color: var(--accent-blue-light);
    transform: translateY(-50%) scale(1.2);
}

.sortable.sort-desc::after {
    content: '↓';
    opacity: 1;
    color: var(--accent-blue-light);
    transform: translateY(-50%) scale(1.2);
}

/* 加载状态 - 科技风格 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
    color: var(--text-secondary);
    font-weight: 600;
}

.loading::before {
    content: '';
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 - 现代化设计 */
.empty-state {
    text-align: center;
    padding: 80px 24px;
    color: var(--text-secondary);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: 12px;
    margin: 20px;
    border: 1px solid var(--border-light);
}

.empty-state h3 {
    margin-bottom: 12px;
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 700;
}

.empty-state p {
    margin-bottom: 24px;
    font-size: 16px;
    line-height: 1.6;
}

/* 错误状态 - 科技风格 */
.error-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--accent-danger);
    background: linear-gradient(135deg, rgba(229, 62, 62, 0.05) 0%, rgba(252, 129, 129, 0.03) 100%);
    border: 2px solid rgba(229, 62, 62, 0.2);
    border-radius: 12px;
    margin: 20px;
    box-shadow: var(--shadow-sm);
}

/* 成功状态 - 科技风格 */
.success-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--accent-success);
    background: linear-gradient(135deg, rgba(56, 161, 105, 0.05) 0%, rgba(104, 211, 145, 0.03) 100%);
    border: 2px solid rgba(56, 161, 105, 0.2);
    border-radius: 12px;
    margin: 20px;
    box-shadow: var(--shadow-sm);
}

/* CSV容器 - 支持侧边栏定位 */
#csvContainer {
    position: relative;
}

/* 表格和侧边栏的包装容器 - 确保总宽度与分页器一致 */
.table-with-sidebar-wrapper {
    position: relative;
    display: flex;
    width: 100%;
    align-items: flex-start;
}

/* 表格包装器 - 支持侧边栏展开按钮和固定表头 */
.table-wrapper {
    position: relative;
    display: block;
    height: 650px; /* 固定高度 */
    border-radius: 8px 0 0 0; /* 左下角改为直角，与分页器贴合 */
    overflow: hidden;
    /* 占据除侧边栏外的所有宽度 */
    flex: 1;
}

/* 表格展开侧边栏 - 和表格包装器并排显示，不重叠 */
.table-expand-sidebar {
    position: relative;
    width: 40px;
    min-width: 40px;
    background: rgba(155, 89, 182, 0.95);
    border-radius: 0 8px 0 0; /* 右下角改为直角，与分页器贴合 */
    display: flex;
    align-items: flex-start;
    justify-content: center;
    z-index: 20;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 0;
    /* 高度将通过JavaScript动态设置，只匹配table-wrapper高度 */
}

/* 垂直展开按钮 - 占满整个侧边栏区域 */
.table-expand-btn-vertical {
    background: transparent;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    padding: 0;
    transition: all 0.3s ease;
    width: 100%;
    height: 100%; /* 占满整个侧边栏高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 8px 0 0;
}

.table-expand-btn-vertical:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.02);
}

.table-expand-btn-vertical svg {
    transition: all 0.3s ease;
    color: white;
    opacity: 0.9;
}

.table-expand-btn-vertical:hover svg {
    opacity: 1;
    transform: translateX(2px);
}

.table-expand-btn-vertical.expanded:hover svg {
    transform: translateX(-2px);
}

/* 表格容器调整 - 支持固定表头 */
.table-wrapper .table-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 8px 0 0 0; /* 左下角和右下角都为直角，与分页器贴合 */
}

/* 表格表头容器 - 固定在顶部 */
.table-header-container {
    position: sticky;
    top: 0;
    z-index: 15;
    background: rgba(55, 65, 81, 0.95);
    backdrop-filter: blur(12px);
    border-radius: 8px 0 0 0;
    overflow: hidden;
}

/* 表格内容容器 - 可滚动 */
.table-content-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 自定义滚动条样式，与表头保持一致 */
.table-content-container::-webkit-scrollbar {
    width: 14px;
}

.table-content-container::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.95);
    border-radius: 0;  /* 改为直角，与右边缩放栏和底部翻页栏更好贴合 */
    backdrop-filter: blur(12px);
    border-left: 1px solid rgba(255, 255, 255, 0.15);
}

.table-content-container::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: 7px;
    border: 2px solid rgba(55, 65, 81, 0.95);  /* 使用与表头一致的背景色 */
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.table-content-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.3) 100%);
}

.table-content-container::-webkit-scrollbar-thumb:active {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.45) 0%, rgba(255, 255, 255, 0.35) 100%);
}

.table-content-container::-webkit-scrollbar-corner {
    background: rgba(55, 65, 81, 0.95);
    border-radius: 0;  /* 改为直角，与右边缩放栏和底部翻页栏更好贴合 */
}

/* 表格本身的样式调整 */
.table-wrapper table {
    width: 100%;
    border-collapse: collapse;
}

/* 固定表头的表格样式 */
.table-header-container table {
    border-radius: 8px 0 0 0;
    table-layout: fixed;
}

.table-content-container table {
    border-radius: 0;
    table-layout: fixed;
}

/* 确保表头和内容表格列宽一致 */
.table-header-container table,
.table-content-container table {
    width: 100%;
}

/* 行号列固定宽度 */
.row-header,
.row-number {
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
    box-sizing: border-box;
}

/* 确保表格使用固定布局，防止列宽变化 */
.table-wrapper table {
    table-layout: fixed !important;
    width: 100%;
    border-collapse: collapse;
}

/* 第一列（行号列）的固定宽度 */
.table-wrapper table th:first-child,
.table-wrapper table td:first-child {
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
}

/* 防止选中状态影响布局 */
tbody tr {
    box-sizing: border-box;
}

/* 确保所有表格单元格都使用border-box */
.table-wrapper table th,
.table-wrapper table td {
    box-sizing: border-box !important;
}

/* 其他列的基础宽度设置 */
.table-header-container th,
.table-content-container td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 默认隐藏配置相关的表头和列 */
.config-header,
.config-subheader,
.v4-config-data,
.v6-config-data {
    display: none;
}

/* 当表格完全展开时显示所有配置列 */
.table-full-expanded .config-header,
.table-full-expanded .config-subheader,
.table-full-expanded .v4-config-data,
.table-full-expanded .v6-config-data {
    display: table-cell;
}

/* 表格数据行中对应的列也需要隐藏/显示 */
.v4-config-data,
.v6-config-data {
    display: none;
}

/* 当V4配置展开时显示对应的数据列 */
.v4-config-expanded .v4-config-data {
    display: table-cell;
}

/* 排序功能样式 */
.sortable-header {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background-color 0.2s ease;
}

.sortable-header:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.sort-indicator {
    display: inline-block;
    margin-left: 6px;
    font-size: 12px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.sort-indicator:before {
    content: '↕';
    color: #888;
}

.sortable-header.sort-asc .sort-indicator:before {
    content: '↑';
    color: #3182ce;
    opacity: 1;
}

.sortable-header.sort-desc .sort-indicator:before {
    content: '↓';
    color: #3182ce;
    opacity: 1;
}

.sortable-header:hover .sort-indicator {
    opacity: 1;
}

/* 当V6配置展开时显示对应的数据列 */
.v6-config-expanded .v6-config-data {
    display: table-cell;
}

/* 批量添加模式样式 */
.help-text {
    font-size: 12px;
    color: var(--text-tertiary);
    font-style: italic;
    margin-left: 8px;
}

.ip-range-help {
    margin-top: 4px;
    padding: 4px 8px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 4px;
    border-left: 3px solid var(--accent-blue);
}

.ip-range-help small {
    color: var(--accent-blue);
    font-weight: 500;
}

/* 预览区域样式 */
.preview-section {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    margin-top: 16px;
}

.preview-section h3 {
    color: var(--accent-blue);
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
}

.preview-content {
    max-height: 200px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    padding: 12px;
    border: 1px solid rgba(30, 41, 59, 0.1);
}

.preview-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    margin-bottom: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 4px;
    border-left: 3px solid var(--accent-blue);
    font-size: 13px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.preview-item:last-child {
    margin-bottom: 0;
}

.preview-item .ip-address {
    font-weight: 600;
    color: var(--accent-blue);
    min-width: 120px;
}

.preview-item .hypervisor {
    color: var(--text-secondary);
    margin-left: 12px;
}

.preview-count {
    text-align: center;
    margin-top: 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-blue);
}

/* 批量添加模式下的表单调整 */
.batch-add-mode #edit_vm_ip_address {
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.batch-add-mode .ip-range-help {
    display: block !important;
}

/* 剪贴板粘贴功能样式 */
.modal-content.paste-active {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    transition: box-shadow 0.2s ease;
}

.field-filled {
    background-color: rgba(34, 197, 94, 0.1) !important;
    border-color: rgba(34, 197, 94, 0.5) !important;
    transition: all 0.3s ease;
}

.field-filled:focus {
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
}

/* 粘贴提示样式 */
.paste-hint {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(59, 130, 246, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 1000;
}

.paste-hint.show {
    opacity: 1;
    transform: translateY(0);
}
