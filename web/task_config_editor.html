<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Config Editor</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/editor.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Task Config Editor</h1>
            <div class="controls">
                <input type="file" id="fileInput" accept=".csv" style="display: none;">
                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">导入文件</button>
                <button class="btn btn-success" onclick="exportCSV()">导出CSV</button>
                <button class="btn btn-primary" onclick="showAddModal()">添加记录</button>
            </div>
        </div>

        <div class="filter-section">
            <div class="filter-grid">
                <div class="filter-group">
                    <label>搜索 IP 地址:</label>
                    <input type="text" id="searchIP" placeholder="输入IP地址" onkeyup="filterData()">
                </div>
                <div class="filter-group">
                    <label>任务名称:</label>
                    <input type="text" id="searchTask" placeholder="输入任务名称" onkeyup="filterData()">
                </div>
            </div>

            <div class="filter-group">
                <label>Hypervisor 层级选择:</label>
                <div class="hypervisor-selector">
                    <div>
                        <div class="level-selector" id="provinceSelector"></div>
                    </div>
                    <div>
                        <div class="level-selector" id="citySelector"></div>
                    </div>
                    <div>
                        <div class="level-selector" id="roomSelector"></div>
                    </div>
                    <div>
                        <div class="level-selector" id="serverSelector"></div>
                    </div>
                </div>
                <div class="filter-actions">
                    <div class="selected-count" id="selectedCount"></div>
                    <button class="btn btn-clear-filters" onclick="clearAllFilters()" title="清除所有过滤条件">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        清除过滤
                    </button>
                </div>

                <!-- 批量操作控制栏 -->
                <div class="batch-controls" id="batchControls">
                    <div class="batch-info">
                        <span id="selectedCountText">已选择 0 条记录</span>
                    </div>
                    <button class="btn btn-primary" onclick="showBatchEditModal()">批量编辑</button>
                    <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                    <button class="btn btn-warning" onclick="clearSelection()">取消选中</button>
                </div>
            </div>
        </div>



        <!-- CSV表格容器 -->
        <div id="csvContainer">
        <!-- 表格和侧边栏的包装容器 -->
        <div class="table-with-sidebar-wrapper">
            <div class="table-wrapper">
                <div class="table-container">
                    <!-- 固定表头容器 -->
                    <div class="table-header-container">
                        <table id="headerTable">
                        <thead>
                            <tr>
                                <th rowspan="2" class="row-header">#</th>
                                <th rowspan="2" class="sortable-header" data-column="vm_ip_address">
                                    VM IP Address
                                    <span class="sort-indicator"></span>
                                </th>
                                <th rowspan="2" class="sortable-header" data-column="hypervisor">
                                    Hypervisor
                                    <span class="sort-indicator"></span>
                                </th>
                                <th colspan="2" style="background: #2c3e50; text-align: center;">Task Name</th>
                                <th colspan="2" style="background: #2c3e50; text-align: center;">Limit Rate</th>
                                <th colspan="2" style="background: #2c3e50; text-align: center;">Replica</th>
                                <!-- V4 CONFIG 表头 - 默认隐藏 -->
                                <th colspan="4" style="background: #2c3e50; text-align: center;" class="config-header v4-config-header">V4 Config</th>
                                <!-- V6 CONFIG 表头 - 默认隐藏 -->
                                <th colspan="4" style="background: #2c3e50; text-align: center;" class="config-header v6-config-header">V6 Config</th>
                                <th rowspan="2" class="basic-header">Local Conf Server</th>
                                <th rowspan="2" class="basic-header">Notes</th>
                            </tr>
                            <tr>
                                <th style="background: rgb(220 120 60 / 80%); color: white;">V4</th>
                                <th style="background: #9b59b6; color: white;">V6</th>
                                <th style="background: rgb(220 120 60 / 80%); color: white;">V4</th>
                                <th style="background: #9b59b6; color: white;">V6</th>
                                <th style="background: rgb(220 120 60 / 80%); color: white;">V4</th>
                                <th style="background: #9b59b6; color: white;">V6</th>
                                <!-- V4 CONFIG 子表头 - 默认隐藏 -->
                                <th style="background: rgb(220 120 60 / 80%); color: white; font-size: 11px;" class="config-subheader v4-config-col">Download Times</th>
                                <th style="background: rgb(220 120 60 / 80%); color: white; font-size: 11px;" class="config-subheader v4-config-col">Max Time</th>
                                <th style="background: rgb(220 120 60 / 80%); color: white; font-size: 11px;" class="config-subheader v4-config-col">Max Retries</th>
                                <th style="background: rgb(220 120 60 / 80%); color: white; font-size: 11px;" class="config-subheader v4-config-col">Connect Timeout</th>
                                <!-- V6 CONFIG 子表头 - 默认隐藏 -->
                                <th style="background: #9b59b6; color: white; font-size: 11px;" class="config-subheader v6-config-col">Download Times</th>
                                <th style="background: #9b59b6; color: white; font-size: 11px;" class="config-subheader v6-config-col">Max Time</th>
                                <th style="background: #9b59b6; color: white; font-size: 11px;" class="config-subheader v6-config-col">Max Retries</th>
                                <th style="background: #9b59b6; color: white; font-size: 11px;" class="config-subheader v6-config-col">Connect Timeout</th>
                            </tr>
                        </thead>
                        </table>
                    </div>

                    <!-- 可滚动表格内容容器 -->
                    <div class="table-content-container">
                        <table id="dataTable">
                        <tbody id="tableBody">
                        </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 表格展开按钮 - 和表格并排显示 -->
            <div class="table-expand-sidebar">
                <button class="table-expand-btn-vertical" onclick="toggleFullTable()" title="展开完整表格">
                    <svg id="table-expand-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <!-- 展开图标 (默认) -->
                        <g class="expand-icon">
                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M15 18L21 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                        <!-- 收起图标 (隐藏) -->
                        <g class="collapse-icon" style="display: none;">
                            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M9 18L3 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                    </svg>
                </button>
            </div>
        </div>

        <div class="pagination">
            <div class="pagination-controls">
                <button class="btn btn-primary" onclick="previousPage()">上一页</button>
                <span id="pageInfo">第 1 页，共 1 页</span>
                <button class="btn btn-primary" onclick="nextPage()">下一页</button>
            </div>
            <select id="pageSize" onchange="changePageSize()">
                <option value="50">50条/页</option>
                <option value="100">100条/页</option>
                <option value="200">200条/页</option>
            </select>
        </div>
        </div> <!-- 关闭csvContainer -->
    </div>

    <!-- 编辑/添加模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content batch-edit-modal">
            <h2 id="modalTitle">添加记录</h2>
            <form id="editForm">

                <!-- 批量添加控制区域 -->
                <div class="batch-section general-section">
                    <h3>批量添加设置</h3>
                    <div class="batch-field-row">
                        <input type="checkbox" id="batch_add_mode" onchange="toggleBatchAddMode()">
                        <label for="batch_add_mode">启用批量添加模式</label>
                        <span class="help-text">支持IP地址范围批量生成记录</span>
                    </div>
                </div>

                <!-- 通用字段区域 -->
                <div class="batch-section general-section">
                    <h3>基本信息</h3>
                    <div class="batch-field-row">
                        <label for="edit_vm_ip_address">VM IP Address</label>
                        <input type="text" id="edit_vm_ip_address" required placeholder="************* 或 *************-110">
                        <div class="ip-range-help" id="ipRangeHelp" style="display: none;">
                            <small>批量模式：支持格式 *************-110 (生成100到110的IP)</small>
                        </div>
                    </div>
                    <div class="batch-field-row">
                        <label for="edit_hypervisor">Hypervisor</label>
                        <input type="text" id="edit_hypervisor" required placeholder="beijing-haidian-room1-server1">
                    </div>
                    <div class="batch-field-row">
                        <label for="edit_local_conf_server">Local Conf Server</label>
                        <input type="text" id="edit_local_conf_server" placeholder="config.example.com">
                    </div>
                    <div class="batch-field-row">
                        <label for="edit_notes">Notes</label>
                        <input type="text" id="edit_notes" placeholder="备注信息">
                    </div>
                </div>

                <!-- V4/V6配置区域 - 左右分栏 -->
                <div class="batch-section config-section">
                    <h3>V4/V6 配置</h3>
                    <div class="config-columns">
                        <!-- V4配置栏 -->
                        <div class="config-column v4-column">
                            <h4>V4 配置</h4>
                            <div class="batch-field-row">
                                <label for="edit_v4_task_name">Task Name</label>
                                <input type="text" id="edit_v4_task_name" placeholder="task_v4_1">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v4_limit_rate">Limit Rate</label>
                                <input type="text" id="edit_v4_limit_rate" placeholder="100M">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v4_replica">Replica</label>
                                <input type="number" id="edit_v4_replica" placeholder="2">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v4_download_times">Download Times</label>
                                <input type="number" id="edit_v4_download_times" placeholder="3">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v4_max_time">Max Time</label>
                                <input type="number" id="edit_v4_max_time" placeholder="300">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v4_max_retries">Max Retries</label>
                                <input type="number" id="edit_v4_max_retries" placeholder="3">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v4_connect_timeout">Connect Timeout</label>
                                <input type="number" id="edit_v4_connect_timeout" placeholder="30">
                            </div>
                        </div>

                        <!-- V6配置栏 -->
                        <div class="config-column v6-column">
                            <h4>V6 配置</h4>
                            <div class="batch-field-row">
                                <label for="edit_v6_task_name">Task Name</label>
                                <input type="text" id="edit_v6_task_name" placeholder="task_v6_1">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v6_limit_rate">Limit Rate</label>
                                <input type="text" id="edit_v6_limit_rate" placeholder="100M">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v6_replica">Replica</label>
                                <input type="number" id="edit_v6_replica" placeholder="2">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v6_download_times">Download Times</label>
                                <input type="number" id="edit_v6_download_times" placeholder="3">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v6_max_time">Max Time</label>
                                <input type="number" id="edit_v6_max_time" placeholder="300">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v6_max_retries">Max Retries</label>
                                <input type="number" id="edit_v6_max_retries" placeholder="3">
                            </div>
                            <div class="batch-field-row">
                                <label for="edit_v6_connect_timeout">Connect Timeout</label>
                                <input type="number" id="edit_v6_connect_timeout" placeholder="30">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 预览区域 -->
                <div class="batch-section preview-section" id="batchPreviewSection" style="display: none;">
                    <h3>批量添加预览</h3>
                    <div id="batchPreviewContent" class="preview-content">
                        <!-- 预览内容将在这里动态生成 -->
                    </div>
                </div>

                <!-- 底部按钮区域 -->
                <div class="batch-buttons">
                    <button type="submit" class="btn btn-primary" id="submitBtn">保存记录</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量编辑模态框 -->
    <div id="batchEditModal" class="modal">
        <div class="modal-content batch-edit-modal">
            <h2>批量编辑</h2>
            <form id="batchEditForm">

                <!-- 通用字段区域 -->
                <div class="batch-section general-section">
                    <h3>通用配置</h3>
                    <div class="batch-field-row">
                        <input type="checkbox" id="batch_hypervisor_check" onchange="toggleBatchField('hypervisor')">
                        <label for="batch_hypervisor_check">Hypervisor</label>
                        <input type="text" id="batch_hypervisor" disabled>
                    </div>
                    <div class="batch-field-row">
                        <input type="checkbox" id="batch_local_conf_server_check" onchange="toggleBatchField('local_conf_server')">
                        <label for="batch_local_conf_server_check">Local Conf Server</label>
                        <input type="text" id="batch_local_conf_server" disabled>
                    </div>
                    <div class="batch-field-row">
                        <input type="checkbox" id="batch_notes_check" onchange="toggleBatchField('notes')">
                        <label for="batch_notes_check">Notes</label>
                        <input type="text" id="batch_notes" disabled>
                    </div>
                </div>

                <!-- V4/V6配置区域 - 左右分栏 -->
                <div class="batch-section config-section">
                    <h3>V4/V6 配置</h3>
                    <div class="config-columns">
                        <!-- V4配置栏 -->
                        <div class="config-column v4-column">
                            <h4>V4 配置</h4>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v4_task_name_check" onchange="toggleBatchField('v4_task_name')">
                                <label for="batch_v4_task_name_check">Task Name</label>
                                <input type="text" id="batch_v4_task_name" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v4_limit_rate_check" onchange="toggleBatchField('v4_limit_rate')">
                                <label for="batch_v4_limit_rate_check">Limit Rate</label>
                                <input type="text" id="batch_v4_limit_rate" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v4_replica_check" onchange="toggleBatchField('v4_replica')">
                                <label for="batch_v4_replica_check">Replica</label>
                                <input type="number" id="batch_v4_replica" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v4_download_times_check" onchange="toggleBatchField('v4_download_times')">
                                <label for="batch_v4_download_times_check">Download Times</label>
                                <input type="number" id="batch_v4_download_times" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v4_max_time_check" onchange="toggleBatchField('v4_max_time')">
                                <label for="batch_v4_max_time_check">Max Time</label>
                                <input type="number" id="batch_v4_max_time" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v4_max_retries_check" onchange="toggleBatchField('v4_max_retries')">
                                <label for="batch_v4_max_retries_check">Max Retries</label>
                                <input type="number" id="batch_v4_max_retries" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v4_connect_timeout_check" onchange="toggleBatchField('v4_connect_timeout')">
                                <label for="batch_v4_connect_timeout_check">Connect Timeout</label>
                                <input type="number" id="batch_v4_connect_timeout" disabled>
                            </div>
                        </div>

                        <!-- V6配置栏 -->
                        <div class="config-column v6-column">
                            <h4>V6 配置</h4>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v6_task_name_check" onchange="toggleBatchField('v6_task_name')">
                                <label for="batch_v6_task_name_check">Task Name</label>
                                <input type="text" id="batch_v6_task_name" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v6_limit_rate_check" onchange="toggleBatchField('v6_limit_rate')">
                                <label for="batch_v6_limit_rate_check">Limit Rate</label>
                                <input type="text" id="batch_v6_limit_rate" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v6_replica_check" onchange="toggleBatchField('v6_replica')">
                                <label for="batch_v6_replica_check">Replica</label>
                                <input type="number" id="batch_v6_replica" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v6_download_times_check" onchange="toggleBatchField('v6_download_times')">
                                <label for="batch_v6_download_times_check">Download Times</label>
                                <input type="number" id="batch_v6_download_times" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v6_max_time_check" onchange="toggleBatchField('v6_max_time')">
                                <label for="batch_v6_max_time_check">Max Time</label>
                                <input type="number" id="batch_v6_max_time" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v6_max_retries_check" onchange="toggleBatchField('v6_max_retries')">
                                <label for="batch_v6_max_retries_check">Max Retries</label>
                                <input type="number" id="batch_v6_max_retries" disabled>
                            </div>
                            <div class="batch-field-row">
                                <input type="checkbox" id="batch_v6_connect_timeout_check" onchange="toggleBatchField('v6_connect_timeout')">
                                <label for="batch_v6_connect_timeout_check">Connect Timeout</label>
                                <input type="number" id="batch_v6_connect_timeout" disabled>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮区域 -->
                <div class="batch-buttons">
                    <button type="submit" class="btn btn-primary">批量更新</button>
                    <button type="button" class="btn btn-secondary" onclick="closeBatchEditModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 按依赖顺序加载JavaScript模块 -->
    <script src="assets/js/utils.js"></script>
    <!-- 全局拖放浮层 -->
    <div id="globalDropOverlay" class="global-drop-overlay">
        <div class="drop-zone-container">
            <div class="drop-zone-box">
                <div class="drop-zone-icon">📁</div>
                <div class="drop-zone-text">Drop CSV file here</div>
                <div class="drop-zone-subtext">Release to load task configuration</div>
            </div>
        </div>
    </div>

    <script src="assets/js/config-loader.js"></script>
    <script src="assets/js/csv-editor.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>