# Task Config Editor - 模块化重构版本

## 概述

Task Config Editor 是一个用于管理任务配置的Web应用程序。本版本已完成模块化重构，将原来的单一HTML文件分离为多个专门的模块。

## 文件结构

```
web/
├── task_config_editor.html          # 主HTML文件
├── test_data.csv                    # 测试数据文件
├── README.md                        # 说明文档
└── assets/
    ├── css/
    │   ├── main.css                 # 基础样式、布局、通用组件
    │   ├── editor.css               # 编辑器专用样式、语法高亮
    │   └── task-config-editor.css   # 原始CSS文件（保留作为备份）
    ├── js/
    │   ├── main.js                  # 主应用逻辑、页面初始化、事件绑定
    │   ├── config-loader.js         # 配置文件加载和保存功能
    │   ├── csv-editor.js            # CSV格式配置编辑功能
    │   ├── utils.js                 # 通用工具函数
    │   └── task-config-editor.js    # 原始JS文件（保留作为备份）
    └── libs/                        # 外部库目录（预留）
```

## 模块说明

### JavaScript模块

1. **utils.js** - 通用工具函数
   - `parseHypervisor()` - 解析hypervisor字段
   - `buildHierarchyData()` - 构建层级数据结构
   - `updateSelectedCount()` - 更新选中数量显示
   - `handleRowClick()` - 处理行点击事件（支持Shift多选）
   - `handleFilterClick()` - 处理层级选择器点击事件

2. **config-loader.js** - 配置文件加载和保存
   - `handleFileSelect()` - 处理文件选择
   - `parseCSV()` - 解析CSV内容
   - `exportCSV()` - 导出CSV格式
   - `exportJSON()` - 导出JSON格式
   - `importJSON()` - 导入JSON格式
   - `setupDragAndDrop()` - 设置拖拽上传
   - 本地存储功能

3. **csv-editor.js** - CSV编辑功能
   - `filterData()` - 数据过滤
   - `renderTable()` - 表格渲染
   - `updatePagination()` - 分页功能
   - CRUD操作（增删改查）
   - 批量操作功能
   - 行选择和多选功能

4. **main.js** - 主应用逻辑
   - `initializeApp()` - 应用初始化
   - `setupEventListeners()` - 事件监听器设置
   - `buildHierarchySelectors()` - 构建层级选择器
   - 键盘快捷键支持
   - 自动保存功能

### CSS模块

1. **main.css** - 基础样式
   - 全局样式重置
   - 布局和容器样式
   - 按钮和表单组件样式
   - 响应式设计
   - 通知和模态框样式

2. **editor.css** - 编辑器专用样式
   - 表格样式和颜色编码
   - 内联编辑样式
   - 批量编辑界面样式
   - 交互状态样式

## 功能特性

### 表格编辑
- **表格模式**: 直观的表格界面，支持可视化编辑
- 内联编辑功能，双击单元格即可编辑

### 文件支持
- 支持CSV格式的导入导出
- 拖拽上传功能
- 本地存储自动保存

### 高级功能
- 层级过滤（省份-城市-机房-服务器）
- 批量编辑和删除
- 多选支持（Ctrl+点击，Shift+点击）
- 分页显示
- 搜索功能

### 键盘快捷键
- `Ctrl+S`: 保存到本地存储
- `Ctrl+O`: 打开文件选择
- `Ctrl+E`: 导出CSV
- `Ctrl+A`: 全选当前页
- `ESC`: 关闭模态框
- `Delete`: 删除选中行

## 使用方法

1. **打开应用**: 在浏览器中打开 `task_config_editor.html`

2. **导入数据**: 
   - 点击"导入文件"按钮选择CSV或JSON文件
   - 或直接拖拽文件到页面上

3. **编辑数据**:
   - 在表格模式下直接编辑单个记录
   - 使用批量编辑功能修改多条记录
   - 切换到JSON模式进行高级编辑

4. **过滤和搜索**:
   - 使用层级选择器按地理位置过滤
   - 在搜索框中输入IP地址或任务名称

5. **导出数据**:
   - 点击"导出CSV"或"导出JSON"保存数据

## 测试

应用包含测试数据文件 `test_data.csv`，可用于验证功能：

```csv
vm_ip_address,hypervisor,v4_task_name,v6_task_name,...
*************,beijing-haidian-room1-server1,task_v4_1,task_v6_1,...
*************,beijing-haidian-room1-server2,task_v4_2,task_v6_2,...
...
```

## 技术特点

- **模块化架构**: 功能分离，便于维护和扩展
- **无依赖**: 纯原生JavaScript，无需外部库
- **响应式设计**: 支持各种屏幕尺寸
- **现代化UI**: 使用CSS Grid和Flexbox布局
- **用户体验**: 支持键盘快捷键和拖拽操作

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

重构过程中保持了所有原有功能，同时提升了代码的可维护性：

1. **功能完整性**: 所有原有功能均已保留
2. **性能优化**: 模块化加载，按需执行
3. **代码质量**: 函数职责单一，便于测试和调试
4. **扩展性**: 新功能可以独立模块形式添加

总代码行数从原来的1760行增加到约1800行，主要是由于模块分离和注释增加。
