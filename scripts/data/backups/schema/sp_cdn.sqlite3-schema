-- CDN提供商表
CREATE TABLE CDN_Providers (
          id TEXT PRIMARY KEY,
          provider TEXT
      );

-- CDN域名表
CREATE TABLE CDN_Domains (
          domain TEXT PRIMARY KEY,
          provider_id TEXT,
          FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id)
      );

-- CDN映射表,记录源站域名与CDN域名的对应关系
CREATE TABLE CDN_Mappings (
          origin_domain TEXT,
          cdn_domain TEXT,
          provider_id TEXT,
          PRIMARY KEY (origin_domain, cdn_domain),
          FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id)
      );

-- CDN网络表,记录CDN域名对应的IP网段信息
CREATE TABLE CDN_Networks (
          cdn_domain TEXT,
          ip_version TEXT,
          network TEXT,
          ip_prefix TEXT,
          provider_id TEXT,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (cdn_domain, network),
          FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id),
          FOREIGN KEY (ip_prefix) REFERENCES SP_GeoIP (ip_prefix)
      );

-- DNS解析记录表
CREATE TABLE SP_DNS (
      domain TEXT,
      cdn_domain TEXT,
      ip TEXT,
      ip_prefix TEXT,
      record_type TEXT,
      provider_id TEXT,
      updated_at DATETIME DEFAULT (datetime('now', 'localtime')),
      PRIMARY KEY (domain, ip),
      FOREIGN KEY (ip_prefix) REFERENCES SP_GeoIP (ip_prefix),
      FOREIGN KEY (ip) REFERENCES SP_ServiceType (ip),
      FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id)
  );

-- IP地理位置信息表
CREATE TABLE SP_GeoIP (
          ip_prefix TEXT,
          country TEXT,
          province TEXT,
          city TEXT,
          isp TEXT,
          PRIMARY KEY (ip_prefix)
      );

-- URL资源表
CREATE TABLE SP_URLs (
      category TEXT,
      domain TEXT,
      url TEXT,
      referer TEXT,
      ua_tag TEXT,
      weight INTEGER,
      size INTEGER,
      updated_at DATETIME DEFAULT (datetime('now', 'localtime')),
      PRIMARY KEY (category, domain, url)
  );

-- 服务类型表,记录IP的服务类型和速度信息
CREATE TABLE SP_ServiceType (
      ip TEXT,
      ip_prefix TEXT,
      service_type TEXT,
      avg_speed REAL,
      act_speed REAL,
      count INTEGER,
      updated_at DATETIME DEFAULT '1971-01-01 00:00:00',
      PRIMARY KEY (ip),
      FOREIGN KEY (ip_prefix) REFERENCES SP_GeoIP (ip_prefix)