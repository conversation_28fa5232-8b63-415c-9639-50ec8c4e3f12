-- database: ../sp_cdn.db

SELECT 
  CDN_Mappings.origin_domain as 源站域名,
  CDN_Networks.cdn_domain as CDN域名,
  CDN_Networks.provider_id as provider_id,
  SP_ServiceType.ip_prefix as IP前缀, 
  SP_GeoIP.province as `省份`,
  SP_ServiceType.service_type as 服务类型,
  COUNT(DISTINCT CASE WHEN SP_ServiceType.avg_speed > 0 THEN SP_ServiceType.ip END) as IP个数,
  COALESCE(COUNT(DISTINCT CASE WHEN SP_ServiceType.avg_speed > 0 THEN SP_ServiceType.ip END), 0) * 
  COALESCE(AVG(CASE WHEN SP_ServiceType.avg_speed > 0 THEN SP_ServiceType.avg_speed END), 0) as 总速率
FROM SP_ServiceType 
LEFT JOIN SP_GeoIP ON SP_ServiceType.ip_prefix = SP_GeoIP.ip_prefix
LEFT JOIN CDN_Networks ON SP_ServiceType.ip_prefix = CDN_Networks.ip_prefix
LEFT JOIN CDN_Mappings ON CDN_Networks.cdn_domain = CDN_Mappings.cdn_domain
WHERE SP_ServiceType.updated_at >= date((SELECT MAX(updated_at) FROM SP_ServiceType))
GROUP BY SP_ServiceType.ip_prefix
ORDER BY 总速率 DESC;



