-- database: ../sp_cdn.db

DROP TABLE IF EXISTS TMP_IP;
DROP TABLE IF EXISTS TMP_IP_WITH_PREFIX;
CREATE TEMPORARY TABLE IF NOT EXISTS TMP_IP (
  ip TEXT
);

INSERT INTO TMP_IP (ip) VALUES 
("**************"),
("*************"),
("*************"),
("*************"),
("*************"),
("*************"),
("*************"),
("*************"),
("*************"),
("*************"),
("*************"),
("**************"),
("**************"),
("**************"),
("**************"),
("*************"),
("**************"),
("**************"),
("**************"),
("*************"),
("************"),
("*************"),
("**************"),
("************"),
("************"),
("************"),
("*************"),
("************"),
("************"),
("*************"),
("************"),
("**************"),
("************"),
("**************"),
("************"),
("*************"),
("*************"),
("************"),
("************"),
("************"),
("************"),
("************"),
("*************"),
("2409:8c18:700:300e::38"),
("************"),
("2409:8c18:700:300e::3a"),
("2409:8c02:218:202:3::3fd"),
("2409:8c5c:b00:201::2:ab"),
("2409:8c5c:b00:201::2:a8"),
("2409:8c5c:b00:201::2:aa"),
("2409:8c02:218:202:3::3f3"),
("2409:8c02:218:202:3::3e8"),
("2409:875e:5088:e9::5f"),
("2409:875e:5088:e9::5c"),
("2409:8c5c:b00:201::2:a7"),
("2409:8c02:21c:a3:3::3dd"),
("2409:8c18:700:300e::39"),
("2409:8c02:218:202:3::3ee"),
("111.32.202.101"),
("2409:8c02:218:201:3::3ce"),
("2409:8c02:218:201:3::3d7"),
("2409:8c02:21c:a2:3::3fa"),
("120.220.66.136"),
("2409:8c3c:1300:808:3::3e8"),
("120.220.66.148"),
("2409:8c02:218:202:3::3ea"),
("2409:8c02:218:202:3::3f0"),
("2409:8c02:21c:a2:3::3db"),
("2409:8c02:21c:a3:3::3e8"),
("2409:8c02:218:201:3::3db"),
("120.220.66.150"),
("2409:8c02:218:201:3::3f6"),
("2409:8c02:21c:a3:3::3fc"),
("2409:8c02:21c:a3:3::3f4"),
("2409:8c3c:1300:808:3::3ef"),
("111.32.202.27"),
("2409:8c02:218:201:3::3c6"),
("2409:8c02:218:201:3::3f3"),
("111.32.202.28"),
("2409:8c02:21c:a2:3::3f4"),
("2409:8c02:218:201:3::3fd"),
("2409:8c02:218:201:3::3f9"),
("**************"),
("2409:8c3c:1300:808:3::3eb"),
("2409:8c02:21c:a2:3::3f1"),
("2409:8c02:218:202:3::3fe"),
("2409:8c02:218:201:3::3e5"),
("2409:8c02:21c:a3:3::3f9"),
("2409:8c3c:1300:808:3::3f4"),
("2409:8c02:218:201:3::3e6"),
("2409:8c02:21c:a2:3::3fe"),
("2409:8c02:218:201:3::3fb"),
("2409:8c02:218:201:3::3e9"),
("*************"),
("**************"),
("2409:875e:5088:80:3::7ee"),
("2409:8c02:21c:a2:3::3fc"),
("**************"),
("**************");

SELECT * FROM TMP_IP;

-- 创建临时表来存储 IP 和对应的 IP 前缀

CREATE TEMPORARY TABLE IF NOT EXISTS TMP_IP_WITH_PREFIX AS
SELECT 
  ip,
  SUBSTR(ip, 1, 
    INSTR(ip, '.') + 
    INSTR(SUBSTR(ip, INSTR(ip, '.') + 1), '.') + 
    INSTR(SUBSTR(ip, INSTR(ip, '.') + INSTR(SUBSTR(ip, INSTR(ip, '.') + 1), '.') + 1), '.')
  ) || '0' AS ip_prefix
FROM TMP_IP;

-- 验证临时表是否创建成功并包含正确的数据
SELECT * FROM TMP_IP_WITH_PREFIX;




-- 使用新创建的临时表进行查询
SELECT 
  t.ip,
  t.ip_prefix,
  g.province,
  c.provider_id,
  c.cdn_domain,
  s.service_type
FROM TMP_IP_WITH_PREFIX t
LEFT JOIN SP_GeoIP g ON t.ip_prefix = g.ip_prefix
LEFT JOIN CDN_Networks c ON t.ip_prefix = c.ip_prefix
LEFT JOIN SP_ServiceType s ON t.ip = s.ip;

