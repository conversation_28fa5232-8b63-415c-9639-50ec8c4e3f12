#!/usr/bin/env perl

use strict;
use warnings;

use LWP::UserAgent;

use utf8;
binmode(STDOUT, ":utf8");

use lib qw(./lib);

my $reportURLs = [
  "http://183.213.50.37:20000/conns", "http://183.213.50.37:20001/conns",
  "http://183.213.50.37:20002/conns", "http://183.213.50.37:20003/conns",
  "http://************:20000/conns",  "http://************:20001/conns",
  "http://************:20002/conns",  "http://************:20003/conns",
  "http://************:20000/conns",  "http://************:20001/conns",
  "http://************:20002/conns",  "http://************:20003/conns",
];

my $outputFile = "box_report.csv";
# 打开文件，如果文件不存在则创建，如果文件存在则覆盖
open my $csv_fh, '>', $outputFile or die "Could not open file '$outputFile' $!";
binmode($csv_fh, ":utf8");
# 写入CSV头
print $csv_fh "BoxID,ISP,Province,SourceIP,SourcePort,TaskID,MaxSpeed,CurrentSpeed\n";

print "==> Downloading reports from URLs\n";
foreach my $url (@$reportURLs) {
  print "  -> Report URL: $url\n";
  my $ua       = LWP::UserAgent->new;
  my $response = $ua->get($url);
  if ( $response->is_success ) {
    my $content = $response->decoded_content( charset => 'UTF-8' );
    
    foreach my $line (split /\n/, $content) {
      if ($line =~ /BoxID:\s*(\S+),\s*ISP:\s*(\S+),\s*Province:\s*(\S+),\s*IP:\s*(\S+):(\S+),\s*TaskID:\s*(\S+),\s*Max send rate:\s*([\d.]+)\s*Mbps,\s*Current rate:\s*([\d.]+)\s*Mbps/) {
          my ($boxid, $isp, $province, $srcIP, $srcPort, $taskID, $maxSpeed, $curSpeed) = ($1, $2, $3, $4, $5, $6, $7, $8);
          print $csv_fh join(',', $boxid, $isp, $province, $srcIP, $srcPort, $taskID, $maxSpeed, $curSpeed), "\n";
      }
    }
  }
  else {
    print "Failed to download from $url: " . $response->status_line . "\n";
  }
}

close $csv_fh;
