# 处理流程

1. 更新 manifest 文件(`configs/cdn_manifest.json`)，注意填充以下字段：
    - name
    - domains
    - URLs
    - probe

    空白模板如下:

    ```json
    "xxxxx_xxx": {
      "domain": "xxxxxx.com",
      "probe": "xxxxxx.com",
      "URLs": []
    }
    ```

    对 `configs/cdn_manifest.json` 文件按照顶层 key 进行排序:

    ```shell
    jq 'to_entries | sort_by(.key) | from_entries' configs/cdn_manifest.json > configs/cdn_manifest_sorted.json
    ```

2. 打开网页 <https://boce.aliyun.com/detect/dns>，逐个解析域名:
   a. 如果只需要中国移动的 IP 地址，在运营商选项中，可只勾选“中国移动”；
   b. 运行之前要点开高级设置，选择解析 A 记录或者 AAAA 记录
   c. 通过 导出 按钮，将结果导出为 Excel
   d. 将 Excel 转换成 csv 文件
   e. 重复以上 1-4 步，解析完多个域名后，将所有 csv 文件根据 IPv4 和 IPv6 分别保存在不同的目录
   f. 提取所有域名可以使用如下命令:

    ```shell
    jq -crM '[.[].domain[]]' configs/cdn_manifest.json
    ```

3. 分析解析结果, 提取 CDN 节点信息:

    ```shell
    ./sp_cdnHandler.pl extract -f "~/Downloads/optimize/dns-result/v4/*.xlsx"
    ```

    ```shell
    ./sp_cdnHandler.pl extract -f "~/Downloads/optimize/dns-result/v6/*.xlsx"
    ```

    说明：
    1. 如果 PROVIDER 栏目提示为 N/A:
       1. 可以到以下网站根据域名和公司名信息, 查询到完整的 CDN 提供商信息:
           - <[www.icpapi.com](https://www.icpapi.com/)>
           - <[icplishi.com](https://icplishi.com/)>
       2. 查询到相应信息后，需要手动更新数据库:
           - CDN_Providers:

            ```sql
            BEGIN TRANSACTION;
            INSERT INTO CDN_Providers (id, provider) VALUES 
                ('alicdn', '阿里巴巴(中国)有限公司'),
                ('tencent', '腾讯云计算（北京）有限责任公司'),
                ('baidu', '百度云计算（北京）有限公司'),
                ('huawei', '华为技术有限公司');
            COMMIT;
            ```

           - CDN_Domains:

            ```sql
            BEGIN TRANSACTION;
            INSERT INTO CDN_Domains (domain, provider_id) VALUES 
                ('17lai.org','alicdn'),
                ('alibabacapital.com','alicdn'),
                ('libabacorp.com','alicdn');
            COMMIT;
            ```

    2. 运行完成后，会生成或更新数据库中以下表格
       - `CDN_Networks`: 保存了 CDN 域名及 IP 地址段信息
       - `CDN_Mappings`: 记录 源站域名和 CDN 域名之间的映射关系

4. 安装 ProjectDiscovery 工具包中的 `httpx` 工具
5. 开始探测

    以下命令可以完整探测出相应类别可用的节点，而且可以实时输出探测结果。(远程批量探测步骤，详见下文。)

    ```shell
    ./sp_cdnHandler.pl probe -c "apple_swcdn" -d
    ```

    说明：
    1. 参数说明:
       - `-c` 参数指定 manifest 文件((`configs/cdn_manifest.json`)中的类别名称
       - `-d` dryrun, 仅打印探测参数, 不实际执行
    2. Domains report 说明:
       - 探测规则保存在 `configs/cdn_filter.json` 配置文件中
       - GeoIP 信息明确, 且位于 exclude 列表规则中的 IP 地址段, 不会探测（标注 `*` 号）
       - ServiceType 信息明确, 且为非计费地址段, 不会探测（标注 `*` 号）
       - 同时缺失 GeoIP 和 ServiceType 信息的 IP 地址段, 不会进行探测（标注 `N` ）
       - 其他情况会进行探测（标注 `>` 号）
    3. 补全 IP 地理信息
        - 运行后会自动列出所有未收录的 IP
        - 可直接运行以下命令在线查询:

            ```shell
            ./sp_cdnHandler.pl geoip -g
            ```

        - 也可以从在线 IP 查询，并将结复制给 `sp_cdnHandler.pl geoip -g` 命令加工处理
        - 将更新后的信息添加到数据库:

            ```sql
            BEGIN TRANSACTION;
            INSERT INTO SP_GeoIP (ip_prefix, country, province, city, isp) VALUES 
                ('*************', '中国', '广东', '深圳', '移动'),
                ('***********', '中国', '甘肃', '兰州', '移动'),
                ('************', '中国', '湖南', '长沙', '移动'),
                ('*************', '中国', '江苏', '连云港', '移动'),
                ('*************', '中国', '江苏', '常州', '移动'),
                ('************', '中国', '湖北', '武汉', '移动');
            COMMIT;
            ```

        - ⚠️注意⚠️：
          - 整理时可以根据需要删除不可用的地址段，并在 "cdn_filterlist.json" 文件中配置过滤规则，包括：
            - 移动自建 CDN
            - 铁通地址
            - BGP 代播地址
        - 结果加工，在线 IP 查询网站参考：
            > - IPv4:
            >   - [站长之家 - IP批量查询](https://ip.chinaz.com/ipbatch)
            >   - [纯真网络](https://update.cz88.net)
            > - IPv6:
            >   - [IPv6地址查询工具](https://ip.zxinc.org/ipquery/)
            >   - [IP125.com](https://ip125.com)
    4. 补全 ServiceType 信息
        - ServiceType 信息来源于日常匹配的报表以及批量匹配的报表
        - 运行以下命令可手动生成 ServiceType 配置

            ```shell
            ./sp_cdnHandler.pl iptype -g -f "assets/report/iptypes/*.csv"
            ```

    如果需要在远程服务器上进行探测, 可以参考以下步骤:

    a. 在远程服务器上安装 `httpx` 工具

      ```shell
      go install -v github.com/projectdiscovery/httpx/cmd/httpx@latest
      ```

    b. 在本地生成探测命令(仅探测 AAAA 记录)

      ```shell
      rm -f /tmp/probe_*.*
      rm -f /tmp/missing_geoip.txt
      for className in $(jq -crM 'keys | .[]' configs/cdn_manifest.json); do ./sp_cdnHandler.pl probe -c "$className" -t AAAA -d; done
      ```

      以上命令可自动枚举 manifest 中每个类别名称, 并生成探测脚本。缺失的 GeoIP 信息会记录在 `/tmp/missing_geoip.txt` 文件中。可以通过以下命令来查看缺失的 GeoIP 信息:

      ```shell
      cat /tmp/missing_geoip.txt | sort | uniq | sort -V
      ```

    c. 将探测命令上传到远程服务器

      ```shell
      # 将探测文件打包, 文件名包含时间戳
      tar czf /tmp/probe_files_$(date +%Y%m%d%H%M%S).tar.gz -C /tmp $(cd /tmp && ls -l probe_*.txt | awk '{print$9}')
      
      # 删除远程服务器上的旧文件
      ssh root@*************** 'rm -f /tmp/probe_files_*.tar.gz /tmp/probe_*.txt'

      # 将打包文件上传到远程服务器的 /tmp 目录
      scp /tmp/probe_files_*.tar.gz root@***************:/tmp/
      
      # 在远程服务器上解压文件到 /tmp 目录
      ssh root@*************** 'tar --warning=no-unknown-keyword -xzf /tmp/probe_files_*.tar.gz -C /tmp'
      ```

      如果文件数量不多，可以直接上传:

      ```shell
      scp /tmp/probe_*.txt root@***************:/tmp/
      ```

    d. 远程服务器上后台执行探测命令

      ```shell
      screen -S probe01
      sh /tmp/probe_xxxxx.txt
      Ctrl+A Ctrl+D
      ```

    可以使用以下脚本一次性后台开启多个探测会话:

      ```shell
      #!/bin/bash

      # 获取所有 probe_*.txt 文件
      probe_files=(probe_*.txt)
    
      # 遍历每个 probe 文件
      for file in "${probe_files[@]}"; do
          # 从文件名中提取会话名称 (例如: probe_01.txt -> probe01)
        session_name=$(echo "$file" | sed 's/\.txt$//' | tr '.' '_')
        
        # 创建新的 detached screen 会话并在其中运行 probe 脚本 
        screen -dmS "$session_name" sh -c "sh $file && exit"
        
        echo "Started $file in screen session: $session_name"
      done
      ```

      以下是直接远程调用该脚本的命令:

      ```shell
      ssh root@*************** 'cd /tmp && ./probe.sh'
      ```

    e. 远程服务器上查看探测结果:

      ```shell
      cat /tmp/ip_avail_*.txt | wc -l
      ```

      ```shell
      screen -ls
      screen -r probe01
      ```

      格式化分类输出:

      ```shell
      for file in ip_avail_*.txt; do 
        count=$(wc -l < "$file")
        printf "%-36s : %8d\n" "$file" "$count"
      done
      ```

    f. 结束探测会话(如果没有自动结束的话)

      ```shell
      # 列出所有 screen 会话
      screen -ls
      
      # 结束所有 screen 会话
      screen -ls | grep '(Detached)' | awk '{print $1}' | xargs -I % screen -S % -X quit
      
      # 确认所有会话已结束
      screen -ls
      ```

    g. 拷贝探测结果

      ```shell
      # 远程打包压缩
      ssh root@*************** 'cd /tmp && tar czf ip_avail_$(date +%Y%m%d%H%M%S).tar.gz ip_avail_*.txt'
      
      # 删除本地旧文件
      rm -f /tmp/ip_avail_*.tar.gz /tmp/ip_avail_*.txt

      # 拷贝压缩包至本地
      scp root@***************:/tmp/ip_avail_*.tar.gz /tmp/
      
      # 本地解压缩
      tar xzf /tmp/ip_avail_*.tar.gz -C /tmp
      ```

      如果文件数量不多，可以直接拷贝:

      ```shell
      scp root@***************:/tmp/ip_avail_*.txt /tmp/
      ```

6. 导入 `httpx` 探测结果

    ```shell
    ./sp_cdnHandler.pl dns -u -c "all" -f "/tmp/ip_avail_*.txt"
    ```

    说明：
    1. 参数说明:
       - `-u` 参数开关说明此处为根据探测结果 ip_avail_xxx.txt 更新 dns config 配置文件
       - `-c` 参数指定 manifest 文件(`configs/cdn_manifest.json`)中的类别名称
         - `-c all` 表示自动枚举所有类型
       - `-f` 指定探测结果 ip_avail_xxx.txt 文件路径

7. (可选) 备份 sp_cdn.db 数据库至 `data/backups/json` 目录(JSON格式) 和 `data/backups/db` 目录(SQLite格式)
    a. 备份其它数据库至 `data/backups/json` 目录

      ```shell
      ./sp_dbHandler.pl dump
      ./sp_dbHandler.pl backup
      ```

    b. 运行以下命令可重新生成排序后的 GeoIP  `data/backups/json/sp_geoip.json` 文件(可选)

      ```shell
      ./sp_cdnHandler.pl geoip -r
      ```

    c. (谨慎操作) 运行以下命令可以恢复数据库

      ```shell
      ./sp_dbHandler.pl restore
      ```

8. 更新 URL 地址库

    ```shell
    ./sp_cdnHandler.pl url -c all
    ```

9. 查看收录的 IP 地址总数：
    a. 查看任务包中收录的 IP 地址总数

    ```shell
    ./sp_cdnHandler.pl task -e -a 1 -t task-Pull-ALL
    ```

    说明：
    1. 参数说明:
       - `-e` 参数用于评估任务包中收录的 IP 类别，并给出详细报表
       - `-a` 参数指定采集数据的天数，默认 30 天，即采集评估最近 30 天内写入的所有数据记录
       - `-t` 参数指定任务包名称

    b. 按类别查看各个类别收录的 IP 地址总数

    ```shell
    ./sp_cdnHandler.pl dns -e -c all
    ```

    说明：
    2. 参数说明:
       - `-c` 参数指定 manifest 文件(`configs/cdn_manifest.json`)中的类别名称
         - `-c all` 表示自动枚举所有类型

10. 生成并发布任务包

    ```shell
    ./sp_cdnHandler.pl task -g -a 1 -t all
    ./sp_cdnHandler.pl task -d -a 1 -t all
    ```

    说明：
    1. 参数说明:
       - `-g` 参数用于生成任务中所涉及的配置文件，并自动打包
       - `-d` 参数用于发布任务包
       - `-t` 参数指定任务包名称, all 代表自动枚举所有任务名

11. 清理工作目录  

    ```shell
    rm -rf /tmp/ip_dns_*
    rm -rf /tmp/probe_*.txt
    rm -rf /tmp/probe_files_*.tar.gz
    rm -rf /tmp/ip_avail_*.txt
    rm -rf /tmp/ip_avail_*.tar.gz
    rm -rf /tmp/missing_geoip.txt
    ```

12. 数据库日常维护

    ```shell
    sqlite3 scripts/data/sp_cdn.db
    VACUUM;
    .exit
    ```

13. 查找并排序 docker 日志

    ```shell
    find /opt/docker/containers -name "*.log" -exec cat {} + | grep ,200 | awk -F\" '{print$4}' | awk -F, '{print$3" "$7}' | sort | uniq -c | sort -V
    ```
