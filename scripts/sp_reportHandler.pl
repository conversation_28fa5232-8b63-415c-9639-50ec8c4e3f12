#!/usr/bin/env perl

use strict;
use warnings;
use File::Basename;
use Data::Dumper;

use FindBin;
use lib "$FindBin::Bin/modules";
use SP::NetAddr;

use utf8;
use open qw( :std :encoding(utf8) );
binmode(STDOUT, ":utf8");
binmode(STDERR, ":utf8");

# 输入文件名
my $input_file = qq($FindBin::Bin/assets/report/csv/20[0-9][0-9][0-1][0-9][0-3][0-9].csv);

# 输出文件名
my $output_file = qq($FindBin::Bin/assets/report/csv/merged.csv);

# 服务端业务类型文件名
my $service_type_file = qq($FindBin::Bin/assets/report/iptypes/daily_ip_service_types.csv);

# 服务端业务类型数据
# 说明: 
#   有效类别包括:
#     - IDC外部客户
#     - 互联网专线
#     - 家宽和企宽
#   BLANK 表示空值("")
#   其他所有无效类别, 包括但不限于:
#     - #N/A
#     - IDC专业公司云能
#     - IDC专业公司非云能
#     - GPRS(PS域核心网)
#     - 网络设备地址
my %service_type_db=(
  "valid_types" => [
    "IDC外部客户",
    "互联网专线",
    "家宽和企宽"
  ],
  "ip" => {}
);

# 统计每日汇总数据
my %daily_report=();

# 记录开始和结束日期
my ($date_start, $date_end);

# 检查输出文件是否已经存在，如果存在则删除
unlink $output_file if -f $output_file;

# 添加 CSV 头部到输出文件
open my $out_fh, '>', $output_file or die "Cannot open $output_file: $!";
binmode($out_fh, ":utf8");
print $out_fh "IP,service_type,average_speed,date\r\n";

print "==> Processing csv files ...\n";
# 读取 CSV 文件
for my $csv_file (glob $input_file) {
  print "  -> Processing file $csv_file ... ";
  
  # 跳过输出文件本身, 防止死循环
  if ($csv_file eq $output_file) {
      print "skip\n";
      next;
  }

  # 跳过非日期数字名称的文件名, 不解析不相关文件
  if ( basename($csv_file) !~ /^\d{8,}\.csv/) {
      print "skip\n";
      next;
  }
  
  # 读取CSV文件内容并添加到输出文件中, 同时添加文件名字段
  open my $in_fh, '<', $csv_file or die "Cannot open $csv_file: $!";
  <$in_fh>; # 跳过文件头部

  $/ = "\r\n";
  my $line_count = 0;
  while (my $line = <$in_fh>) {
    chomp $line;
    $line_count++;

    my ($ip, $throughput, $avgspeed, $service_type) = split /,/, $line;
    $avgspeed = 0 unless $avgspeed =~ /^-?\d+\.?\d*$/;

    my $date = basename($csv_file, '.csv');

    $service_type eq "" and $service_type = "BLANK";
    $service_type eq "0" and $service_type = "#N/A";

    print $out_fh "$ip,$service_type,$avgspeed,$date\r\n";

    if ($service_type ne "#N/A") {
      $service_type_db{"ip"}->{$ip}->{"type"} = $service_type;
      $service_type_db{"ip"}->{$ip}->{"count"} //= 0;
      $service_type_db{"ip"}->{$ip}->{"avgspeed"} //= 0;
      $service_type_db{"ip"}->{$ip}->{"moving_avg_speed"} //= [];
      $service_type_db{"ip"}->{$ip}->{"updated_at"} = sprintf("%04d-%02d-%02d %02d:%02d:%02d", substr($date, 0, 4), substr($date, 4, 2), substr($date, 6, 2), 0, 0, 0);

      push @{$service_type_db{"ip"}->{$ip}->{"moving_avg_speed"}}, $avgspeed;
      
      # Keep only the last 7 speed values for moving average
      if (@{$service_type_db{"ip"}->{$ip}->{"moving_avg_speed"}} > 7) {
        shift @{$service_type_db{"ip"}->{$ip}->{"moving_avg_speed"}};
      }
      
      my $sum_of_speed = 0;
      $sum_of_speed += $_ for @{$service_type_db{"ip"}->{$ip}->{"moving_avg_speed"}};
      my $num_of_speeds = scalar(@{$service_type_db{"ip"}->{$ip}->{"moving_avg_speed"}});
      
      $service_type_db{"ip"}->{$ip}->{"avgspeed"} = 
        ($service_type_db{"ip"}->{$ip}->{"avgspeed"} * $service_type_db{"ip"}->{$ip}->{"count"} + $avgspeed) / ($service_type_db{"ip"}->{$ip}->{"count"} + 1);
      $service_type_db{"ip"}->{$ip}->{"count"}++;
      $service_type_db{"ip"}->{$ip}->{"moving_avgspeed"} = $sum_of_speed / $num_of_speeds;
    }

    $date_start = $date unless defined $date_start;
    $date_end   = $date unless defined $date_end;

    $date_start > $date and $date_start = $date;
    $date_end   < $date and $date_end   = $date;
  }
  
  close $in_fh;
  print $line_count, " ... done\n";
}

close $out_fh;

my $merged_file = sprintf("$FindBin::Bin/assets/report/csv/merged_%s_%s.csv", $date_start, $date_end);

rename $output_file, $merged_file;

print "  -> Merge completed. The output file is $merged_file\n";

# print Dumper(\%service_type_db), "\n";

# 将服务端业务类型写入 csv 文件
unlink $service_type_file if -f $service_type_file;

print "==> Saving service type data to $service_type_file ... ";
# 添加CSV头部到输出文件
open my $service_type_fh, '>', $service_type_file or die "Cannot open $service_type_file: $!";
binmode($service_type_fh, ":utf8");
print $service_type_fh "IP,服务端业务类型,移动平均速率,重复次数,更新时间\n";

# 依次写入 IP 地址
for my $ip (sort {ip_to_packed($a) cmp ip_to_packed($b)} keys %{$service_type_db{"ip"}}) {
  my $type = $service_type_db{"ip"}->{$ip}->{"type"};
  my $moving_avgspeed = $service_type_db{"ip"}->{$ip}->{"moving_avgspeed"};
  my $count = $service_type_db{"ip"}->{$ip}->{"count"};
  my $updated_at = $service_type_db{"ip"}->{$ip}->{"updated_at"};
  printf $service_type_fh "%s,%s,%.3f,%d,%s\n", $ip,$type,$moving_avgspeed,$count,$updated_at;
}

close $service_type_fh;

print "done\n";

# 读取 merged 后的文件, 生成日报表
print "==> Generating overall report ...\n";

open my $merged_file_fh, '<:encoding(UTF-8)', $merged_file or die "Can't open $merged_file: $!";
<$merged_file_fh>; # 跳过文件头部

my $last_date = "";

$/ = "\r\n";
while (my $line = <$merged_file_fh>) {
  chomp $line;

  my ($ip, $service_type, $avgspeed, $date) = split "," , $line;

  $ip eq "" and next;
  $avgspeed eq "" and $avgspeed = 0;

  if ($last_date ne $date) {
    $last_date = $date;
    print "  -> Generating report for ", $date, " ...\n";

    $daily_report{$date}->{"total_count"} = 0;
    $daily_report{$date}->{"daily"}->{"matched"}->{"total"} = 0;
    $daily_report{$date}->{"daily"}->{"matched"}->{"valid"} = 0;
    $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{"IDC外部客户"} = 0;
    $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{"互联网专线"} = 0;
    $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{"家宽和企宽"} = 0;
    $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{"无效"} = 0;

    $daily_report{$date}->{"daily"}->{"avgspeed"}->{"IDC外部客户"} = 0;
    $daily_report{$date}->{"daily"}->{"avgspeed"}->{"互联网专线"} = 0;
    $daily_report{$date}->{"daily"}->{"avgspeed"}->{"家宽和企宽"} = 0;
    $daily_report{$date}->{"daily"}->{"avgspeed"}->{"无效"} = 0;

    $daily_report{$date}->{"adjusted"}->{"matched"}->{"total"} = 0;
    $daily_report{$date}->{"adjusted"}->{"matched"}->{"valid"} = 0;
    $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{"IDC外部客户"} = 0;
    $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{"互联网专线"} = 0;
    $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{"家宽和企宽"} = 0;
    $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{"无效"} = 0;
  }
  
  $daily_report{$date}->{"total_count"}++;

  if ($service_type eq "#N/A") {
    my $adjusted_service_type = $service_type_db{"ip"}->{$ip}->{"type"};
    if (defined $adjusted_service_type and $adjusted_service_type ne "#N/A") {
      $daily_report{$date}->{"adjusted"}->{"matched"}->{"total"}++;
      if (grep { $_ eq $adjusted_service_type } @{$service_type_db{"valid_types"}}) {
        $daily_report{$date}->{"adjusted"}->{"matched"}->{"valid"}++;
      } else {
        $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{"无效"}++;
      }
      unless (defined $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{$adjusted_service_type}) {
        $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{$adjusted_service_type} = 0;
      } else {
        $daily_report{$date}->{"adjusted"}->{"matched"}->{"details"}->{$adjusted_service_type}++;
      }
    }
  } else {
    $daily_report{$date}->{"daily"}->{"matched"}->{"total"}++;
    if (grep { $_ eq $service_type } @{$service_type_db{"valid_types"}}) {
      $daily_report{$date}->{"daily"}->{"matched"}->{"valid"}++;
    } else {
      $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{"无效"}++;
      $daily_report{$date}->{"daily"}->{"avgspeed"}->{"无效"} += $avgspeed;
    }
    unless (defined $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{$service_type}) {
      $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{$service_type} = 0;
      $daily_report{$date}->{"daily"}->{"avgspeed"}->{$service_type} = 0;
    } else {
      $daily_report{$date}->{"daily"}->{"matched"}->{"details"}->{$service_type}++;
      $daily_report{$date}->{"daily"}->{"avgspeed"}->{$service_type} += $avgspeed;
    }
  }
}

close $merged_file_fh;

# print Dumper(\%daily_report), "\n";

# 输出为 CSV 报表
my $report_file = sprintf("$FindBin::Bin/assets/report/csv/report_%s_%s.csv", $date_start, $date_end);

my @column_names=qw(
    日期
    IP总数
    IDC外部客户
    互联网专线
    家宽和企宽
    无效
    小计
    IDC外部客户流入
    互联网专线流入
    家宽和企宽流入
    无效流入
    流入小计
    当日有效数
    当日匹配数
    当日有效率
    当日匹配率
    IDC外部客户
    互联网专线
    家宽和企宽
    无效
    修正有效数
    修正匹配数
    修正后有效率
    修正后匹配率
);

open my $report_file_fh, '>', $report_file or die "Can't open $report_file: $!";
binmode($report_file_fh, ":utf8");
print $report_file_fh join ',', @column_names, "\r\n";

for my $date (sort keys %daily_report) {
  # IP 总数
  my $total_count = $daily_report{$date}->{"total_count"};
  # 当日有效数
  my $daily_valid = $daily_report{$date}->{"daily"}->{"matched"}->{"valid"};
  # 当日匹配数
  my $daily_matched = $daily_report{$date}->{"daily"}->{"matched"}->{"total"};
  # 当日有效率
  my $daily_valid_ratio;
  if ($daily_matched > 0) {
    $daily_valid_ratio = sprintf("%3.2f%%", $daily_valid*100/$daily_matched) 
  } else {
    $daily_valid_ratio = "0%";
  }
  # 当日匹配率
  my $daily_match_ratio = sprintf("%3.2f%%", $daily_matched*100/$total_count);
  # 修正有效数
  my $adjusted_valid = $daily_report{$date}->{"adjusted"}->{"matched"}->{"valid"} + $daily_valid;
  # 修正匹配数
  my $adjusted_matched = $daily_report{$date}->{"adjusted"}->{"matched"}->{"total"} + $daily_matched;
  # 修正后有效率
  my $adjusted_valid_ratio = sprintf("%3.2f%%", $adjusted_valid*100/$adjusted_matched);
  # 修正后匹配率
  my $adjusted_match_ratio = sprintf("%3.2f%%", $adjusted_matched*100/$total_count);

  my $daily = $daily_report{$date}->{"daily"};
  my $details = $daily->{"matched"}->{"details"};
  my $avgspeed = $daily->{"avgspeed"};

  my $adjusted = $daily_report{$date}->{"adjusted"};
  my $adj_details = $adjusted->{"matched"}->{"details"};

  printf $report_file_fh "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\r\n",
      $date,
      $total_count,
      $details->{"IDC外部客户"},
      $details->{"互联网专线"},
      $details->{"家宽和企宽"},
      $details->{"无效"},
      $daily_matched,
      sprintf("%3.2f", $avgspeed->{"IDC外部客户"}),
      sprintf("%3.2f", $avgspeed->{"互联网专线"}),
      sprintf("%3.2f", $avgspeed->{"家宽和企宽"}),
      sprintf("%3.2f", $avgspeed->{"无效"}),
      sprintf("%3.2f", $avgspeed->{"IDC外部客户"} 
        + $avgspeed->{"互联网专线"} 
        + $avgspeed->{"家宽和企宽"} 
        + $avgspeed->{"无效"}),

      $daily_valid,
      $daily_matched,
      $daily_valid_ratio,
      $daily_match_ratio,
      $adj_details->{"IDC外部客户"},
      $adj_details->{"互联网专线"},
      $adj_details->{"家宽和企宽"},
      $adj_details->{"无效"},
      $adjusted_valid,
      $adjusted_matched,
      $adjusted_valid_ratio,
      $adjusted_match_ratio;
}

close $report_file_fh;

print "==> All done\n";

