{"task-Pull-ALL": {"description": "默认规则, 包含所有记录", "conditions": {}, "evaluate_only": true}, "task-Pull-ISP-cmcc-Ex-Prov-js-01": {"description": "只筛选ISP为移动方向的记录, 江苏除外", "conditions": {"include": {"isp": "移动"}, "exclude": {"province": "江苏"}}, "evaluate_only": true}, "task-Pull-ISP-cmcc-RT-AAAA-In-Prov-tj-01": {"description": "只筛选ISP为移动方向的记录,包括天津, 山东, 海南, 广西, 吉林", "conditions": {"include": {"isp": "移动", "province": ["天津", "山东", "海南", "河南", "湖南", "广西", "吉林", "辽宁", "宁夏", "青海", "西藏", "黑龙江"], "record_type": "AAAA"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-AAAA-In-Prov-gz-01": {"description": "只筛选ISP为移动方向的记录,包括贵州, 山西, 河北, 甘肃, 内蒙古", "conditions": {"include": {"isp": "移动", "province": ["贵州", "山西", "河北", "四川", "重庆", "江西", "安徽", "云南", "甘肃", "陕西", "内蒙古"], "record_type": "AAAA"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-AAAA-In-Prov-bj-01": {"description": "只筛选ISP为移动方向的记录,包括北京, 上海, 浙江, 福建, 广东, 湖北", "conditions": {"include": {"isp": "移动", "province": ["北京", "上海", "浙江", "福建", "广东", "湖北"], "record_type": "AAAA"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-A-In-R-Tier1-01": {"description": "只筛选ISP为移动方向的记录,包括北京, 上海, 广东", "conditions": {"include": {"isp": "移动", "province": ["广东", "北京", "上海"], "record_type": "A"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-A-In-R-EastChina-01": {"description": "只筛选ISP为移动方向的记录,包括浙江, 福建, 山东", "conditions": {"include": {"isp": "移动", "province": ["浙江", "福建", "山东"], "record_type": "A"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-A-In-R-Central-01": {"description": "只筛选ISP为移动方向的记录,包括河南, 湖北, 江西", "conditions": {"include": {"isp": "移动", "province": ["河南", "湖北", "江西"], "record_type": "A"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-A-In-R-Southwest-01": {"description": "只筛选ISP为移动方向的记录,包括四川, 重庆, 陕西, 云南, 广西, 海南, 西藏", "conditions": {"include": {"isp": "移动", "province": ["四川", "陕西", "云南", "广西", "海南", "重庆", "西藏"], "record_type": "A"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-A-In-R-NorthChina-01": {"description": "只筛选ISP为移动方向的记录,包括辽宁, 黑龙江, 吉林, 内蒙古", "conditions": {"include": {"isp": "移动", "province": ["辽宁", "黑龙江", "吉林", "内蒙古", "宁夏", "新疆", "青海"], "record_type": "A"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ISP-cmcc-RT-A-In-R-Midwest-01": {"description": "只筛选ISP为移动方向的记录,包括安徽, 贵州, 天津, 湖南", "conditions": {"include": {"isp": "移动", "province": ["安徽", "贵州", "天津", "湖南"], "record_type": "A"}, "exclude": {"providerID": ["hwcdn", "cmcdn"]}}}, "task-Pull-ST-ILL-Ex-Prov-js-01": {"description": "只筛选服务类型为互联网专线的记录, 江苏除外", "conditions": {"include": {"serviceType": "互联网专线", "isp": "移动"}, "filter": {"speed": ">= 0.01"}, "exclude": {"domain": ["static.mofangjiasu.com", "cdn.aixifan.com"], "province": ["江苏"]}}}, "task-Pull-ST-ILL-RT-AAAA-Ex-Prov-js-01": {"description": "只筛选服务类型为互联网专线的记录, 江苏除外", "conditions": {"include": {"serviceType": "互联网专线", "isp": "移动", "record_type": "AAAA"}, "exclude": {"province": ["江苏"]}}}, "task-Pull-ST-HBEB-Ex-Prov-js-01": {"description": "只筛选服务类型为家宽和企宽的记录, 江苏除外", "conditions": {"include": {"serviceType": "家宽和企宽", "isp": "移动"}, "exclude": {"province": "江苏"}}}, "task-Pull-P-tencent-Ex-Prov-js-01": {"description": "只筛选Provider为tencent的记录, 江苏除外", "conditions": {"include": {"providerID": "tencent", "isp": "移动", "province": ["海南", "广西", "山东"]}, "exclude": {"province": "江苏"}}}, "task-Pull-D-iflyrec-Ex-Prov-js-01": {"description": "只筛选 域名 为 www.iflyrec.com 的记录, 江苏除外", "conditions": {"include": {"domain": "www.iflyrec.com", "isp": "移动"}, "exclude": {"province": "江苏"}}}, "task-Pull-ISP-cmcc-RT-AAAA-In-Prov-ln-01": {"description": "只筛选ISP为移动方向的记录,包括辽宁", "conditions": {"include": {"isp": "移动", "province": ["辽宁", "海南"], "record_type": "AAAA"}, "exclude": {"providerID": ["hwcdn", "cmcdn", "tencent"], "province": ["河北"]}}}}