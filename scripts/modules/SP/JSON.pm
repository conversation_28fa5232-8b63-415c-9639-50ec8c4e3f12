package SP::JSON;

use strict;
use warnings;

use utf8;
use open qw(:std :encoding(UTF-8));
binmode(STDOUT, ":utf8");

use JSON::XS;
use Encode qw(encode_utf8);

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  load_json_file
  save_json_file
);

# 模块版本
our $VERSION = '1.00';


# 读取 JSON 文件, 返回 HASH REF
sub load_json_file {
  my $json_file = shift;

  open my $fh, '<:encoding(UTF-8)', $json_file or die "Can't open $json_file: $!\n";

  # 读取文件到一个字符串变量
  my $json_text = do {
    local $/;
    <$fh>;
  };

  # 解析 JSON 字符串
  my $data = decode_json( encode_utf8( $json_text ) );

  # 关闭文件句柄
  close $fh;

  return $data;
}

# 将 HASH REF 写入 JSON 文件
sub save_json_file {
  my $data      = shift;
  my $json_file = shift;

  open my $fh, ">", $json_file or die "Can't write to file $json_file: $!\n";

  binmode($fh, ":utf8");

  my $json = JSON::XS->new->pretty->canonical->space_before(0)->space_after(0);
  print $fh $json->encode($data), "\n";

  close $fh;
}

1;