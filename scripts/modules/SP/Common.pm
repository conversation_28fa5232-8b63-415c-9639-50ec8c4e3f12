package SP::Common;

use strict;
use warnings;

use utf8;
use open qw(:std :encoding(UTF-8));
binmode(STDOUT, ":utf8");

use FindBin;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  CDN_DB_ARCHIVE_DIR
  CDN_PROVIDERS_FILE
  CDN_MANIFEST_FILE
  CDN_MAPPINGS_FILE
  CDN_NETWORKS_FILE
  CDN_FILTER_FILE
  CDN_PROVIDERS_DIR
  CDN_DOMAINS_DIR
  CDN_DB_FILE
  DOCKER_COMPOSE_FILE
  DOMAIN_SUFFIX_FILE
  SERVICE_TYPE_CONF
  SP_DNS_CONF
  SP_GEOIP_FILE
  SP_PROFILES_FILE
  TASK_LIST_FILE
  TASK_BUNDLE_DIR
  TASK_CONFIG_FILE
  REMOTE_CONF_SERVER
  LOCAL_CONF_SERVER
  UA_CONF_MAP
);

use constant {
  CDN_DB_ARCHIVE_DIR   => "$FindBin::Bin/data/backups/db/",
  CDN_PROVIDERS_FILE   => "$FindBin::Bin/data/backups/json/cdn_providers.json",
  CDN_MAPPINGS_FILE    => "$FindBin::Bin/data/backups/json/cdn_mappings.json",
  CDN_NETWORKS_FILE    => "$FindBin::Bin/data/backups/json/cdn_networks.json",
  SP_GEOIP_FILE        => "$FindBin::Bin/data/backups/json/sp_geoip.json",
  SP_DNS_CONF          => "$FindBin::Bin/data/backups/json/sp_dns.json",
  SERVICE_TYPE_CONF    => "$FindBin::Bin/data/backups/json/sp_iptype.json",
  CDN_MANIFEST_FILE    => "$FindBin::Bin/configs/cdn_manifest.json",
  CDN_FILTER_FILE      => "$FindBin::Bin/configs/cdn_filterlist.json",
  CDN_PROVIDERS_DIR    => "$FindBin::Bin/assets/cdn/providers",
  CDN_DOMAINS_DIR      => "$FindBin::Bin/assets/cdn/domains",
  CDN_DB_FILE          => "$FindBin::Bin/data/sp_cdn.db",
  DOMAIN_SUFFIX_FILE   => "$FindBin::Bin/configs/public_suffix_list.dat",
  TASK_LIST_FILE       => "$FindBin::Bin/configs/task_list.json",
  TASK_CONFIG_FILE     => "$FindBin::Bin/configs/task_configs.csv",
  TASK_BUNDLE_DIR      => "$FindBin::Bin/assets/tasks",
  SP_PROFILES_FILE     => "$FindBin::Bin/../configs/sp_profiles.json",
  DOCKER_COMPOSE_FILE  => "$FindBin::Bin/../docker-compose.yaml",
  REMOTE_CONF_SERVER   => "http://**************:8500",
  LOCAL_CONF_SERVER    => "http://***************:8500",
  UA_CONF_MAP          => {
    'windows' => [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ],
    'macos' => [
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    ],
    'mobile' => [
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1',
    ],
    'networkquality' => [
      'networkQuality/147 CFNetwork/1568.200.51 Darwin/24.1.0',
    ],
    "amap" => [
      'xcdn-0/IOS_2.1.2407.8',
    ],
    'baidumap' => [
      'MobileMap',
    ],
    "wechat" => [
      'MicroMessenger Client',
    ],
    'computer' => [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
    ],
    '<nil>' => [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
    ]
  },
};

1;
