package SP::DNS;

use strict;
use warnings;

use utf8;
use open qw(:std :encoding(UTF-8));
binmode( STDOUT, ":utf8" );

use File::Basename;
use Text::ANSITable;

use Data::Dumper;

use SP::Common;
use SP::JSON;
use SP::GeoIP;
use SP::NetAddr;
use SP::IPType;
use SP::SQLite;
use SP::CDN;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  generate_dns_cfg
  update_dns_data
  evaluate_dns_data
  dns_cfg_stat
);

# 根据预定义的资源网站类别, 读取 httpx 探测到的 IP 地址, 生成 DNS 配置文件
#
# 说明：
#    - 参数1: 预定义的类别名称, all 表示同时生成所有类别
#    - 参数2: httpx 探测结果文件, 格式为 TXT, 内容为 IP 地址, 以换行符分隔
#           如果 参数1 为 all, 这里仅读取目录名称
# 依赖:
#    - cdn_manifest.json, 保存了 预定义的资源网站信息, 包括类别、域名、URL 等
#    - cdn_mapping.json, 保存了 源站域名和 CDN 域名之间的映射关系
#    - cdn_domains.json, 保存了 CDN 域名和 IP 地址段及源站域名 之间的映射关系
#    - cdn_filterlist.json, 保存了 CDN IP 地址段筛选规则
#    - providers 目录下 CDN 域名及 IP 具体信息
#    - configs/sp_iptype.json, 保存了 IP 和 服务类型 service type 之间的映射关系
#    - configs/sp_geoip.json, 保存了 IP 和 地理位置以及所属运营商 之间的映射关系
# 输出:
#    - assets 目录下多个 JSON 文件, 以 cdn manifest 名称命名, 保存了下载域名的 CNAME、A 以及 AAAA 记录
# 调用示例：
#    update_dns_data "apple_swcdn" ip_avail.txt
sub update_dns_data {
  my $category = shift;
  my $filename = shift;

  defined $category or die "Category is required\n";
  defined $filename or die "Filename is required\n";

  my $cdn_manifest = load_json_file(CDN_MANIFEST_FILE);

  # 如果用户输入的 category 为 all, 则为生成所有类别的 DNS 配置文件
  if ( $category eq "all" ) {

    # 递归调用, 输出每个 category 下对应的 DNS 配置文件
    my $basedir = dirname($filename);
    for my $category ( keys %{$cdn_manifest} ) {

      # 使用 eval 来捕获可能导致程序退出的错误
      eval {
        update_dns_data( $category,
          File::Spec->catfile( $basedir, "ip_avail_$category.txt" ) );
      };
    }

    print "\n";
    print "==> Merging DNS configs ... \n";
    my $global_dnscfg;

    # 汇总 DNS 配置文件
    for my $asset_file ( glob( CDN_DOMAINS_DIR . "/*.json" ) ) {
      print "  -> $asset_file \n";
      my $category_dnscfg = load_json_file($asset_file);

      # 合并数据并去重
      for my $key ( keys %$category_dnscfg ) {
        if ( exists $global_dnscfg->{$key} ) {

          # 假设值是数组，将两个数组合并去重
          my %global_data = map { $_->{data} => 1 } @{ $global_dnscfg->{$key} };

          for my $item ( @{ $category_dnscfg->{$key} } ) {
            if ( not exists $global_data{ $item->{data} } ) {
              push @{ $global_dnscfg->{$key} }, $item;
            }
          }
        }
        else {
          # 直接存储新的数据
          $global_dnscfg->{$key} = $category_dnscfg->{$key};
        }
      }
    }

    print "\n";
    print "==> Saving DNS configs to ", SP_DNS_CONF, " ... ";
    save_json_file( $global_dnscfg, SP_DNS_CONF );
    print "done \n";

    return;
  }

  # 加载域名 -> CDN 域名映射
  my $cdn_mappings = load_cdn_mappings();

  # 加载 Service Type
  my $servicetype_mapping = load_sp_service_type_brief();

  # 加载 GeoIP Mapping
  my $geoip_mapping = load_sp_geoip();

  # 加载 CDN 域名 -> IP 映射
  my $cdn_networks = load_cdn_networks();

  # DNS Config
  my $dnscfg;

  # NetIP Attribute
  my $netaddr_attr;

  # 保存所有有效的 RR 记录
  my $RRs;

  print "\n";
  print "==> Processing $category ... \n";

  # 在 manifest 中查找每个源站域名
  my $domain = $cdn_manifest->{$category}->{"domain"}->[0];
  print "  -> $domain \n";

  # 在 cdn 域名 HASH 数组中查找每个 CDN 域名
  for my $cdn_domain ( @{ $cdn_mappings->{$domain} } ) {
    my $provider = $cdn_networks->{$cdn_domain}->{"provider"};
    $provider = "N/A" unless defined($provider);
    my $provider_id = $cdn_networks->{$cdn_domain}->{"providerID"};
    $provider_id = "N/A" unless defined($provider_id);

    print "    > $cdn_domain [$provider] \n";

    # 查找 CDN 每个 IP 地址段对应的属性
    for my $ip_block (
      @{ $cdn_networks->{$cdn_domain}->{"networks"}->{"IPv4"} },
      @{ $cdn_networks->{$cdn_domain}->{"networks"}->{"IPv6"} }
      )
    {
      my $netaddr  = get_network_addr($ip_block);
      my $location = get_geoip_location( $geoip_mapping, $netaddr );

      # 简化地址
      $netaddr =~ s/0000:0000:0000:0000$//;

      $netaddr_attr->{$netaddr}->{"domain"}     = $domain;
      $netaddr_attr->{$netaddr}->{"cdnDomain"}  = $cdn_domain;
      $netaddr_attr->{$netaddr}->{"provider"}   = $provider;
      $netaddr_attr->{$netaddr}->{"providerID"} = $provider_id;
      $netaddr_attr->{$netaddr}->{"location"}   = $location;
    }
  }

  # 读取 httpx 探测到的 IP 地址
  print "  -> Loading probe result from $filename ... ";
  unless (-e $filename) {
      print "not found\n";
      next;
  }
  my $ip_avail;
  open my $fh, "<", $filename or die "Can't read file $filename: $!\n";
  while ( my $ipaddr = <$fh> ) {
    chomp $ipaddr;

    my $ip = NetAddr::IP->new($ipaddr);
    if ( defined $ip ) {
      my $netaddr;
      if ( $ip->version() == 4 ) {
        $netaddr = get_network_addr( $ipaddr . "/24" );
      }
      elsif ( $ip->version() == 6 ) {
        $netaddr = get_network_addr( $ipaddr . "/120" );

        # 简化地址
        $netaddr =~ s/0000:0000:0000:0000$//;
      }
      else {
        next;
      }

      my $rr;

      my $domain =
        defined $netaddr_attr->{$netaddr}->{"domain"}
        ? $netaddr_attr->{$netaddr}->{"domain"}
        : "N/A";
      my $cdn_domain =
        defined $netaddr_attr->{$netaddr}->{"cdnDomain"}
        ? $netaddr_attr->{$netaddr}->{"cdnDomain"}
        : "N/A";
      $rr->{"data"}       = $ip->canon();
      $rr->{"type"}       = $ip->version == 4 ? "A" : "AAAA";
      $rr->{"providerID"} = $netaddr_attr->{$netaddr}->{"providerID"};
      $rr->{"provider"}   = $netaddr_attr->{$netaddr}->{"provider"};
      $rr->{"location"}   = $netaddr_attr->{$netaddr}->{"location"};
      $rr->{"serviceType"} =
        get_service_type( $servicetype_mapping, $ip->canon() );

      if ( $domain eq "N/A" or $cdn_domain eq "N/A" ) {
        print "Invalid RR mapping: $domain -> $cdn_domain: ", $ip->addr(), "\n";
        next;
      }

      # 保存所有有效的 RR 记录
      push @{ $RRs->{$domain}->{$cdn_domain} }, $rr;
    }
  }
  print "done\n";
  close $fh;

  # 将 DNS 配置文件保存到 SQLite 数据库
  print "  -> Storing DNS data ... ";
  store_sp_dns($RRs);
  print "done\n";

  print "  -> Writing to asset file ... ";

  for my $domain ( keys %{$RRs} ) {
    for my $cdn_domain ( keys %{ $RRs->{$domain} } ) {
      my $fake_cdn_domain = $category . "." . $cdn_domain;
      push @{ $dnscfg->{$domain} },
        {
        "type" => "CNAME",
        "data" => $cdn_domain,
        };
      $dnscfg->{$cdn_domain} = $RRs->{$domain}->{$cdn_domain};
    }
  }

  my $path = CDN_DOMAINS_DIR;

  mkdir($path);

  $filename = $category . ".json";
  my $full_path = File::Spec->catfile( $path, $filename );

  save_json_file( $dnscfg, $full_path );

  print "done\n";
}

# 根据条件生成 DNS 配置文件
#
# 参数:
#   $conditions - 包含筛选条件的哈希引用, 格式如下:
#     {
#       "include" => {                      # 包含条件
#         domain      => ["domain1", ...],  # 域名列表
#         cdn_domain  => undef,             # CDN 域名
#         record_type => undef,             # 记录类型
#         isp         => "移动",            # 运营商
#         province    => undef,             # 省份
#         serviceType => ["type1", ...],    # 服务类型列表
#         providerID  => undef,             # 服务商 ID
#       },
#       "exclude" => {                      # 排除条件,格式同上
#         ...
#       },
#       days_ago => 30                      # 查询最近多少天的数据
#     }
#   $output_file - 输出文件路径
#
# 依赖:
#   - CDN_DB_FILE: SQLite 数据库文件
#   - SP_DNS 表: 存储 DNS 记录
#   - SP_ServiceType 表: 存储 IP 服务类型
#   - SP_GeoIP 表: 存储 IP 地理位置信息
#   - CDN_Providers 表: 存储 CDN 服务商信息
#
# 输出:
#   - 生成的 DNS 配置文件, JSON 格式
#   - 配置文件结构:
#     {
#       "domain1" => [
#         {
#           "data" => "ip1",               # IP 地址
#           "type" => "A/AAAA",           # 记录类型
#           "location" => "location1",     # 地理位置
#           "provider" => "provider1",     # 服务商
#           "serviceType" => "type1"       # 服务类型
#         },
#         ...
#       ],
#       ...
#     }
sub generate_dns_cfg {
  my $conditions = shift;
  my $output_file = shift;

  # $conditions->{"include"} = {
  #   domain      => [ "swcdn.apple.com", "updates.cdn-apple.com" ],
  #   cdn_domain  => undef,
  #   record_type => undef,
  #   isp         => "移动",
  #   province    => undef,
  #   serviceType => [ "IDC外部客户", "互联网专线" ],
  #   providerID  => undef,
  # };

  # $conditions->{"exclude"} = {
  #   domain      => undef,
  #   cdn_domain  => undef,
  #   record_type => undef,
  #   isp         => undef,
  #   province    => "江苏",
  #   serviceType => [ "自有业务", "BLANK", "其他" ],
  #   providerID  => undef,
  # };

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      SP_DNS.domain, 
      SP_DNS.cdn_domain,
      SP_DNS.ip,
      SP_DNS.record_type,
      SP_GeoIP.country, 
      SP_GeoIP.province, 
      SP_GeoIP.city,
      SP_GeoIP.isp,
      SP_ServiceType.service_type,
      CDN_Providers.id as provider_id,
      CDN_Providers.provider
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " ORDER BY `domain` ASC";

  # print $sql, "\n";
  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $result = $sth->fetchall_arrayref();

  my $dnscfg = {};

  my $counter = 0;

  for my $row (@$result) {
    my (
      $domain,       $cdn_domain,  $ip,   $record_type,
      $country,      $province,    $city, $isp,
      $service_type, $provider_id, $provider
    ) = @$row;
    my $geoip = {
      "country"  => $country,
      "province" => $province,
      "city"     => $city,
      "isp"      => $isp
    };
    my $rr = {
      "data"        => $ip,
      "type"        => $record_type,
      # "location"    => geoip_info_to_str($geoip),
      "location"    => $geoip,
      "provider"    => $provider_id,
      "serviceType" => $service_type ? $service_type : "N/A"
    };
    push @{ $dnscfg->{$domain} }, $rr;

    $counter++;
  }

  print "    > $counter records loaded\n";

  # 关闭数据库连接
  db_close($dbh);

  save_json_file( $dnscfg, $output_file );
}

sub evaluate_dns_data {
  my $category = shift;

  defined $category or die "Category is required\n";

  my $cdn_manifest = load_json_file(CDN_MANIFEST_FILE);

  # 当用户输入的 category 为 all 时, 则枚举所有类别
  if ( $category eq "all" ) {
    for my $category ( keys %{$cdn_manifest} ) {
      # 使用 eval 来捕获可能导致程序退出的错误
      eval {
        evaluate_dns_data($category);
      };
    }
    return;
  }

  # 加载域名 -> CDN 域名映射
  my $cdn_mappings = load_cdn_mappings();

  # 加载 Service Type
  my $servicetype_mapping = load_sp_service_type_brief();

  # 加载 GeoIP Mapping
  my $geoip_mapping = load_sp_geoip();

  # 加载 CDN 域名 -> IP 映射
  my $cdn_networks = load_cdn_networks();

  # 加载 CDN filter list
  my $cdn_filter = load_json_file(CDN_FILTER_FILE);

  # 读取最新的 DNS 记录
  my $dns_statistics = load_dns_statistics();

  # DNS Config
  my $dnscfg;

  print "-" x 32, "\n";
  print " Category: $category \n";
  print "-" x 32, "\n";

  # 在 manifest 中查找每个源站域名
  my $domain = $cdn_manifest->{$category}->{"domain"}->[0];
  print "==> $domain \n";

  # 在 cdn 域名 HASH 数组中查找每个 CDN 域名
  for my $cdn_domain ( @{ $cdn_mappings->{$domain} } ) {
    my $provider = $cdn_networks->{$cdn_domain}->{"provider"};
    $provider //= "N/A";
    my $provider_id = $cdn_networks->{$cdn_domain}->{"providerID"};
    $provider_id //= "N/A";

    print "  -> $cdn_domain [$provider] ($provider_id) \n";

    my @table_data;

    # 查找 CDN 每个 IPv4 地址段对应的属性
    for my $ip_block (
      @{ $cdn_networks->{$cdn_domain}->{"networks"}->{"IPv4"} }
      )
    {
      my $ip = NetAddr::IP->new($ip_block);
      next unless ( defined $ip );
      my $netaddr      = get_network_addr( $ip->addr() . "/24" );
      my $geoip        = get_geoip_info( $geoip_mapping, $netaddr );
      my $service_type = get_service_type( $servicetype_mapping, $ip->first()->canon() );

      my $result       = check_cdn_filter(
          $cdn_filter,
          {
            "ip_block"    => $ip_block,
            "domain"      => $cdn_domain,
            "serviceType" => $service_type,
            "geoip"       => $geoip,
          }
        );

      my $ip_count = $dns_statistics->{$netaddr}->{"ip_count"};
      $ip_count //= 0;
      push @table_data, [$ip_block, $ip_count, $service_type, $result, $geoip->{country}, $geoip->{province}, $geoip->{city}, $geoip->{isp}];
    }

    # 查找 CDN 每个 IPv6 地址段对应的属性
    for my $ip_block (
      @{ $cdn_networks->{$cdn_domain}->{"networks"}->{"IPv6"} }
      )
    {
      my $ip = NetAddr::IP->new($ip_block);
      next unless ( defined $ip );
      my $netaddr      = get_network_addr( $ip->addr() . "/64" );
      my $geoip        = get_geoip_info( $geoip_mapping, $netaddr );
      my $service_type = get_service_type( $servicetype_mapping, $ip->first()->canon() );

      my $result       = check_cdn_filter(
          $cdn_filter,
          {
            "ip_block"    => $ip_block,
            "domain"      => $cdn_domain,
            "serviceType" => $service_type,
            "geoip"       => $geoip,
          }
        );

      my $location = get_geoip_location($geoip_mapping, $netaddr);
      $netaddr = get_network_addr( $ip->addr() . "/64" );
      # 简化地址
      $netaddr =~ s/0000:0000:0000:0000$//;
      my $ip_count = $dns_statistics->{$netaddr}->{"ip_count"};
      $ip_count //= 0;
      push @table_data, [$ip_block, $ip_count, $service_type, $result, $geoip->{country}, $geoip->{province}, $geoip->{city}, $geoip->{isp}]; 
    }


    draw_reports_table($cdn_domain, ['IP', 'IP Count', 'ServiceType', 'Filter', 'Country', 'Province', 'City', 'ISP'], \@table_data);
  }

}

# 统计 DNS 配置文件中的各项指标
#
# 说明：
#    - 参数1: 条件参数, 包含 include/exclude 过滤条件, 以及时间范围等
# 依赖:
#    - get_providerID_stats, 统计 CDN 服务商 ID 分布情况
#    - get_provider_stats, 统计 CDN 服务商分布情况
#    - get_province_stats, 统计 IP 地理位置分布情况
#    - get_ipversion_stats, 统计 IP 版本分布情况
#    - get_servicetype_stats, 统计 IP 服务类型分布情况
#    - get_domain_stats, 统计域名分布情况
# 输出:
#    - 打印各项统计指标到标准输出
# 调用示例：
#    dns_cfg_stat({ "days_ago" => 30 })
sub dns_cfg_stat {
  my $conditions = shift;

  get_providerID_stats($conditions);
  get_provider_stats($conditions);
  get_isp_stats($conditions);
  get_province_stats($conditions);
  get_ipversion_stats($conditions);
  get_servicetype_stats($conditions);
  get_domain_stats($conditions);
}

# 生成 SQL WHERE 子句和绑定值
#
# 说明：
#    - 参数1: 包含筛选条件的哈希引用, 格式如下:
#      {
#        "include" => {                      # 包含条件
#          domain      => ["domain1", ...],  # 域名列表
#          cdn_domain  => undef,             # CDN 域名
#          record_type => undef,             # 记录类型
#          isp         => "移动",            # 运营商
#          province    => undef,             # 省份
#          serviceType => ["type1", ...],    # 服务类型列表
#          providerID  => undef,             # 服务商 ID
#        },
#        "exclude" => {                      # 排除条件,格式同上
#          ...
#        }
#      }
# 返回值:
#    - 返回一个数组引用列表:
#      [0] - WHERE 子句数组引用
#      [1] - 绑定值数组引用
# 调用示例：
#    my ($where_clauses, $bind_values) = generate_where_clauses($conditions);
sub generate_where_clauses {
  my ($conditions) = @_;
  my @where_clauses;
  my @bind_values;

  # 遍历条件类型: include(包含), exclude(排除), filter(过滤)
  foreach my $condition_type ( "include", "exclude", "filter" ) {
    # 处理 domain, cdn_domain, record_type, province, isp, serviceType, provider, providerID 相关的过滤条件
    foreach my $field (
      qw(domain cdn_domain record_type province isp serviceType provider providerID)
      )
    {
      # 检查该字段是否存在且有值
      if ( exists $conditions->{$condition_type}->{$field}
        && defined $conditions->{$condition_type}->{$field} )
      {
        # 根据条件类型设置操作符
        my $operator = $condition_type eq "include" ? "=" : "<>";

        # 根据字段类型确定对应的数据表
        my $table =
          $field eq "serviceType"
          ? "SP_ServiceType"
          : ( $field =~ /^(province|isp)$/ ? "SP_GeoIP" : "SP_DNS" );

        # 处理特殊字段名映射
        my $column;
        if ( $field eq "serviceType" ) {
          $column = "service_type";
        }
        elsif ( $field eq "providerID" ) {
          $column = "provider_id";
        }
        else {
          $column = $field;
        }

        # 如果字段值是数组
        if ( ref $conditions->{$condition_type}->{$field} eq 'ARRAY' ) {
          # 为数组中每个值生成条件语句
          my @field_conditions = map { "$table.$column $operator ?" }
            @{ $conditions->{$condition_type}->{$field} };
          # 根据条件类型用OR或AND连接多个条件
          push @where_clauses,
            "("
            . join(
            $condition_type eq "include" ? " OR " : " AND ",
            @field_conditions
            ) . ")";
          push @bind_values, @{ $conditions->{$condition_type}->{$field} };
        }
        # 如果字段值是单个值
        else {
          push @where_clauses, "$table.$column $operator ?";
          push @bind_values,   $conditions->{$condition_type}->{$field};
        }
      }
    }

    # 处理 speed 相关的过滤条件
    foreach my $field ( qw(speed) ) {
      # 检查是否存在该字段的过滤条件
      if ( exists $conditions->{$condition_type}->{$field }
        && defined $conditions->{$condition_type}->{$field} )
      {
        # 设置表名和列名
        my $table = "SP_ServiceType";
        my $column = "act_speed";
        my $value = $conditions->{$condition_type}->{$field};
        my $operator;

        # 从值中提取运算符(>, <, >=, <=, <>, =)和实际值
        if ($value =~ /^([<>]=?|<>|=)(.*)$/) {
            $operator = $1;
            $value = $2;
        } else {
            next; # 如果没有找到有效的运算符则跳过
        }

        # 添加到 WHERE 子句和绑定值中
        push @where_clauses, "$table.$column $operator ?";
        push @bind_values,   $value;
      }
    }
  }

  return (\@where_clauses, \@bind_values);
}

# 统计每个 CDN 服务商 ID 的 IP 数量分布
#
# 参数:
#   $conditions - 包含筛选条件的哈希引用, 格式如下:
#     {
#       "include" => {                      # 包含条件
#         domain      => ["domain1", ...],  # 域名列表
#         cdn_domain  => undef,             # CDN 域名
#         record_type => undef,             # 记录类型
#         isp         => "移动",            # 运营商
#         province    => undef,             # 省份
#         serviceType => ["type1", ...],    # 服务类型列表
#         providerID  => undef,             # 服务商 ID
#       },
#       "exclude" => {                      # 排除条件,格式同上
#         ...
#       },
#       days_ago => 30                      # 查询最近多少天的数据
#     }
#
# 依赖:
#   - CDN_DB_FILE: SQLite 数据库文件
#   - SP_DNS 表: 存储 DNS 记录
#   - SP_ServiceType 表: 存储 IP 服务类型
#   - SP_GeoIP 表: 存储 IP 地理位置信息
#   - CDN_Providers 表: 存储 CDN 服务商信息
#
# 输出:
#   - 打印统计表, 包含以下列:
#     - ProviderID: CDN 服务商 ID
#     - IP数: 该服务商拥有的唯一 IP 数量
#     - 占比: 该服务商 IP 数量占总数的百分比
sub get_providerID_stats {
  my $conditions = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      SP_DNS.provider_id AS provider_id,
      COUNT(DISTINCT SP_DNS.ip) AS unique_ips
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " GROUP BY `provider_id`";
  $sql .= " ORDER BY `unique_ips` DESC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $results = $sth->fetchall_arrayref({});

  # 转换数据格式
  my $total;
  map { $total += $_->{unique_ips} } @$results;
  my @table_data = map { 
    [$_->{provider_id}, $_->{unique_ips}, sprintf("%.2f %%", $_->{unique_ips} / $total * 100)] 
  } @$results;

  # 绘制统计表
  draw_statistics_table('ProviderID Statistics', ['ProviderID', 'IP数', '占比'], \@table_data);

  db_close($dbh);
}

# 统计 CDN 服务商的 IP 分布情况
#
# 参数:
#   $conditions - 包含筛选条件的哈希引用, 格式如下:
#     {
#       "include" => {                      # 包含条件
#         domain      => ["domain1", ...],  # 域名列表
#         cdn_domain  => undef,             # CDN 域名
#         record_type => undef,             # 记录类型
#         isp         => "移动",            # 运营商
#         province    => undef,             # 省份
#         serviceType => ["type1", ...],    # 服务类型列表
#         providerID  => undef,             # 服务商 ID
#       },
#       "exclude" => {                      # 排除条件,格式同上
#         ...
#       },
#       days_ago => 30                      # 查询最近多少天的数据
#     }
#
# 依赖:
#   - CDN_DB_FILE: SQLite 数据库文件
#   - SP_DNS 表: 存储 DNS 记录
#   - SP_ServiceType 表: 存储 IP 服务类型
#   - SP_GeoIP 表: 存储 IP 地理位置信息
#   - CDN_Providers 表: 存储 CDN 服务商信息
#
# 输出:
#   - 打印统计表, 包含以下列:
#     - Provider: CDN 服务商名称
#     - IP数: 该服务商拥有的唯一 IP 数量
#     - 占比: 该服务商 IP 数量占总数的百分比
sub get_provider_stats {
  my $conditions = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      CDN_Providers.provider AS provider,
      COUNT(DISTINCT SP_DNS.ip) AS unique_ips
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " GROUP BY `provider`";
  $sql .= " ORDER BY `unique_ips` DESC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $results = $sth->fetchall_arrayref({});

  # 转换数据格式
  my $total;
  map { $total += $_->{unique_ips} } @$results;
  my @table_data = map { 
    [$_->{provider}, $_->{unique_ips}, sprintf("%.2f %%", $_->{unique_ips} / $total * 100)] 
  } @$results;

  # 绘制统计表
  draw_statistics_table('Provider Statistics', ['Provider', 'IP数', '占比'], \@table_data);

  db_close($dbh);
}

# 统计 运营商 的 IP 分布情况
#
# 参数:
#   $conditions - 包含筛选条件的哈希引用, 格式如下:
#     {
#       "include" => {                      # 包含条件
#         domain      => ["domain1", ...],  # 域名列表
#         cdn_domain  => undef,             # CDN 域名
#         record_type => undef,             # 记录类型
#         isp         => "移动",            # 运营商
#         province    => undef,             # 省份
#         serviceType => ["type1", ...],    # 服务类型列表
#         providerID  => undef,             # 服务商 ID
#       },
#       "exclude" => {                      # 排除条件,格式同上
#         ...
#       },
#       days_ago => 30                      # 查询最近多少天的数据
#     }
#
# 依赖:
#   - CDN_DB_FILE: SQLite 数据库文件
#   - SP_DNS 表: 存储 DNS 记录
#   - SP_ServiceType 表: 存储 IP 服务类型
#   - SP_GeoIP 表: 存储 IP 地理位置信息
#   - CDN_Providers 表: 存储 CDN 服务商信息
#
# 输出:
#   - 打印统计表, 包含以下列:
#     - ISP: ISP 运营商的名称
#     - IP数: 该服务商拥有的唯一 IP 数量
#     - 占比: 该服务商 IP 数量占总数的百分比
sub get_isp_stats {
  my $conditions = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      SP_GeoIP.isp AS isp,
      COUNT(DISTINCT SP_DNS.ip) AS unique_ips
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " GROUP BY `isp`";
  $sql .= " ORDER BY `unique_ips` DESC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $results = $sth->fetchall_arrayref({});

  # 转换数据格式
  my $total;
  map { $total += $_->{unique_ips} } @$results;
  my @table_data = map { 
    [$_->{isp}, $_->{unique_ips}, sprintf("%.2f %%", $_->{unique_ips} / $total * 100)] 
  } @$results;

  # 绘制统计表
  draw_statistics_table('ISP Statistics', ['ISP', 'IP数', '占比'], \@table_data);

  db_close($dbh);
}

# 统计 DNS 配置文件中各省份的 IP 分布情况
#
# 参数:
#   $conditions - 包含筛选条件的哈希引用, 格式如下:
#     {
#       "include" => {                      # 包含条件
#         domain      => ["domain1", ...],  # 域名列表
#         cdn_domain  => undef,             # CDN 域名
#         record_type => undef,             # 记录类型
#         isp         => "移动",            # 运营商
#         province    => undef,             # 省份
#         serviceType => ["type1", ...],    # 服务类型列表
#         providerID  => undef,             # 服务商 ID
#       },
#       "exclude" => {                      # 排除条件,格式同上
#         ...
#       },
#       days_ago => 30                      # 查询最近多少天的数据
#     }
#
# 依赖:
#   - CDN_DB_FILE: SQLite 数据库文件
#   - SP_DNS 表: 存储 DNS 记录
#   - SP_ServiceType 表: 存储 IP 服务类型
#   - SP_GeoIP 表: 存储 IP 地理位置信息
#   - CDN_Providers 表: 存储 CDN 服务商信息
#
# 输出:
#   - 打印统计表, 包含以下列:
#     - 省份: 省份名称
#     - IP数: 该省份拥有的唯一 IP 数量
#     - 占比: 该省份 IP 数量占总数的百分比
sub get_province_stats {
  my $conditions = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      SP_GeoIP.province AS province,
      COUNT(DISTINCT SP_DNS.ip) AS unique_ips
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " GROUP BY province";
  $sql .= " ORDER BY unique_ips DESC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $results = $sth->fetchall_arrayref({});

  # 转换数据格式
  my $total;
  map { $total += $_->{unique_ips} } @$results;
  my @table_data = map { 
    [$_->{province}, $_->{unique_ips}, sprintf("%.2f %%", $_->{unique_ips} / $total * 100)] 
  } @$results;

  # 绘制统计表
  draw_statistics_table('Province Statistics', ['省份', 'IP数', '占比'], \@table_data);

  db_close($dbh);
}

sub get_ipversion_stats {
  my $conditions = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      CASE 
        WHEN SP_DNS.record_type = 'A' THEN 'IPv4'
        WHEN SP_DNS.record_type = 'AAAA' THEN 'IPv6'
        ELSE 'OTHER'
      END AS ip_version,
      COUNT(DISTINCT SP_DNS.ip) AS unique_ips
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " GROUP BY ip_version";
  $sql .= " ORDER BY unique_ips DESC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $results = $sth->fetchall_arrayref({});

  # 转换数据格式
  my $total;
  map { $total += $_->{unique_ips} } @$results;
  my @table_data = map { 
    [$_->{ip_version}, $_->{unique_ips}, sprintf("%.2f %%", $_->{unique_ips} / $total * 100)] 
  } @$results;

  # 绘制统计表
  draw_statistics_table('IP Version Statistics', ['IPv4/IPv6', 'IP数', '占比'], \@table_data);

  db_close($dbh);
}

# 统计 DNS 配置文件中的服务类型分布
#
# 参数:
#   $conditions - 包含筛选条件的哈希引用, 格式如下:
#     {
#       "include" => {                      # 包含条件
#         domain      => ["domain1", ...],  # 域名列表
#         cdn_domain  => undef,             # CDN 域名
#         record_type => undef,             # 记录类型
#         isp         => "移动",            # 运营商
#         province    => undef,             # 省份
#         serviceType => ["type1", ...],    # 服务类型列表
#         providerID  => undef,             # 服务商 ID
#       },
#       "exclude" => {                      # 排除条件,格式同上
#         ...
#       },
#       days_ago => 30                      # 查询最近多少天的数据
#     }
#
# 输出:
#   - 统计表格, 包含以下列:
#     - 服务端业务类型: 业务类型名称
#     - IP数: 该业务类型的唯一 IP 数量
#     - 占比: 该业务类型 IP 数量占总数的百分比
sub get_servicetype_stats {
  my $conditions = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      COALESCE(SP_ServiceType.service_type, 'N/A') AS service_type,
      COUNT(DISTINCT SP_DNS.ip) AS unique_ips
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " GROUP BY COALESCE(service_type, 'N/A')";
  $sql .= " ORDER BY unique_ips DESC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $results = $sth->fetchall_arrayref({});

  # 转换数据格式
  my $total = 0;
  map { $total += $_->{unique_ips} } @$results;
  my @table_data = map { 
    [$_->{service_type}, $_->{unique_ips}, sprintf("%.2f %%", $_->{unique_ips} / $total * 100)] 
  } @$results;

  # 绘制统计表
  draw_statistics_table('Service Type Statistics', ['服务端业务类型', 'IP数', '占比'], \@table_data);

  db_close($dbh);
}

# 统计域名相关指标
#
# 参数:
#   $conditions - 包含筛选条件的哈希引用, 格式如下:
#     {
#       "include" => {                      # 包含条件
#         domain      => ["domain1", ...],  # 域名列表
#         cdn_domain  => undef,             # CDN 域名
#         record_type => undef,             # 记录类型
#         isp         => "移动",            # 运营商
#         province    => undef,             # 省份
#         serviceType => ["type1", ...],    # 服务类型列表
#         providerID  => undef,             # 服务商 ID
#       },
#       "exclude" => {                      # 排除条件,格式同上
#         ...
#       },
#       days_ago => 30                      # 查询最近多少天的数据
#     }
#
# 依赖:
#   - CDN_DB_FILE: SQLite 数据库文件
#   - SP_DNS 表: 存储 DNS 记录
#   - SP_ServiceType 表: 存储 IP 服务类型
#   - SP_GeoIP 表: 存储 IP 地理位置信息
#   - CDN_Providers 表: 存储 CDN 服务商信息
#
# 输出:
#   - 统计表格, 包含以下列:
#     - 域名: 域名名称
#     - IP数: 该域名的唯一 IP 数量
#     - 占比: 该域名 IP 数量占总数的百分比
sub get_domain_stats {
  my $conditions = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      SP_DNS.domain,
      COUNT(DISTINCT SP_DNS.ip) AS unique_ips
    FROM SP_DNS
    LEFT JOIN SP_ServiceType ON SP_DNS.IP = SP_ServiceType.IP
    LEFT JOIN SP_GeoIP ON SP_DNS.ip_prefix = SP_GeoIP.ip_prefix
    LEFT JOIN CDN_Providers ON SP_DNS.provider_id = CDN_Providers.id
    WHERE 1=1
  };

  my ($where_clauses, $bind_values) = generate_where_clauses($conditions);

  if (@$where_clauses) {
    $sql .= " AND " . join( " AND ", @$where_clauses );
  }

  # Filter records based on updated_at date
  $sql .= " AND SP_DNS.updated_at >= date('now', '-" . $conditions->{days_ago} . " days')";

  $sql .= " GROUP BY SP_DNS.domain";
  $sql .= " ORDER BY unique_ips DESC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@$bind_values);
  my $results = $sth->fetchall_arrayref({});

  # 转换数据格式
  my $total = 0;
  map { $total += $_->{unique_ips} } @$results;
  my @table_data = map { 
    [$_->{domain}, $_->{unique_ips}, sprintf("%.2f %%", $_->{unique_ips} / $total * 100)] 
  } @$results;

  # 绘制统计表
  draw_statistics_table('Domain Statistics', ['域名', 'IP数', '占比'], \@table_data);

  db_close($dbh);
}

sub draw_reports_table {
  my ($heading_text, $columns, $data) = @_;

  # Create a new ASCII table with dynamic heading and columns
  my $t = Text::ANSITable->new();
  my $is_plain_mode = 0;
  if ($is_plain_mode) {
    $t->use_utf8(0);
    $t->use_box_chars(0);
    $t->use_color(0);
    $t->border_style('ASCII::SingleLine');
  } else {
    $t->use_utf8();
    $t->border_style('UTF8::SingleLineBoldHeader');
    $t->color_theme('Standard::NoGradation');
  }

  $t->header_align('center');
  $t->set_column_style(0, align  => 'left');
  $t->set_column_style(1, align  => 'right');
  $t->set_column_style(2, align  => 'center');
  $t->set_column_style(3, align  => 'center');
  $t->set_column_style(4, align  => 'center');
  $t->set_column_style(5, align  => 'center');
  $t->set_column_style(6, align  => 'center');
  $t->columns($columns);

  # Add rows to the table
  my $row_count = 0;
  foreach my $row (@$data) {
    $t->add_row($row);
    $t->set_row_style($row_count, bgcolor => '5353ff') if $row->[3] == 0;
    $row_count++;
  }

  # Print the table
  print $t->draw;
}

# 绘制统计表
# 绘制统计表格
#
# 参数:
#   $heading_text - 表格标题文本
#   $columns - 列名数组引用, 包含每列的标题
#   $data - 表格数据数组引用, 每行是一个数组引用, 包含每列的值
#
# 说明:
#   - 使用 Text::ANSITable 模块绘制表格
#   - 支持普通模式和 UTF8 模式
#   - 普通模式使用 ASCII 字符绘制表格边框
#   - UTF8 模式使用 UTF8 字符绘制表格边框,并支持颜色
#   - 表格样式:
#     - 表头居中对齐
#     - 第一列左对齐
#     - 其他列右对齐
#     - 最后一行显示汇总数据
#     - 汇总行使用灰色背景突出显示
sub draw_statistics_table {
  my ($heading_text, $columns, $data) = @_;

  # Create a new ASCII table with dynamic heading and columns
  my $t = Text::ANSITable->new();
  my $is_plain_mode = 0;
  if ($is_plain_mode) {
    $t->use_utf8(0);
    $t->use_box_chars(0);
    $t->use_color(0);
    $t->border_style('ASCII::SingleLine');
  } else {
    $t->use_utf8();
    $t->border_style('UTF8::SingleLineBoldHeader');
    $t->color_theme('Standard::NoGradation');
  }

  $t->header_align('center');
  $t->set_column_style(0, align  => 'left');
  $t->set_column_style(1, align  => 'right');
  $t->set_column_style(2, align  => 'right');
  $t->columns($columns);

  # Add rows to the table, and calculate total
  my $total = 0;
  foreach my $row (@$data) {
    $t->add_row($row);
    $total += $row->[1];
  }

  # Add total row
  $t->add_row_separator();
  $t->add_row(['汇总', $total, "100.00 %"]);
  # Set the last row to have the same style as the header
  $t->set_cell_style(scalar(@$data), 0, fgcolor => '919191');

  # Print the table
  print $t->draw;
}

1;
