package SP::CDN;

use strict;
use warnings;

use Net::CIDR;

use SP::Common;
use SP::JSON;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  load_cdn_catalogue
  save_cdn_mapping
  load_cdn_providers
  save_cdn_providers
  check_cdn_filter
);

# 模块版本
our $VERSION = '1.00';

sub load_cdn_catalogue {
  my $file = CDN_PROVIDERS_FILE;
  my $data = load_json_file($file);

  my $mapping;
  for my $category ( keys %{$data} ) {
    my $provider = $data->{$category}->{"provider"};
    for my $domain ( @{ $data->{$category}->{"domains"} } ) {
      if ( exists $mapping->{$domain} ) {
        print "ERROR: Duplicate domain found: $domain \n";
      }
      else {
        $mapping->{$domain}->{"category"} = $category;
        $mapping->{$domain}->{"provider"} = $provider;
      }
    }
  }

  return $mapping;
}

# 保存 源站域名 -> CDN 域名映射
sub save_cdn_mapping {
  my $cdn_mappings = shift;

  save_json_file( $cdn_mappings, CDN_MAPPINGS_FILE );
}

# 逐个读取 provider 文件, 加载 CDN 厂家类别信息
sub load_cdn_providers {
  my $category_data;

  my $path = CDN_PROVIDERS_DIR;

  for my $filename ( glob "$path/*.json" ) {
    my $data = load_json_file($filename);

    $category_data->{ $data->{"category"} } = $data;
  }

  return $category_data;
}

# 根据 CDN 厂家类别, 逐个写入 providers 文件
sub save_cdn_providers {
  my $category_data = shift;

  my $path = CDN_PROVIDERS_DIR;

  mkdir($path);
  for my $category ( keys %{$category_data} ) {
    next if $category eq '';
    next if $category eq 'N/A';
    my $filename  = $category . ".json";
    my $full_path = File::Spec->catfile( $path, $filename );

    save_json_file( $category_data->{"$category"}, $full_path );
  }
}

# 根据给定的 CDN filter list 和 ip 地址段信息来判断是否符合条件
#
# 说明：
#    - 参数1: CDN filter list 的 HASH REF
#    - 参数2: IP 网段信息的 HASH REF
# 返回:
#    - 0: 不符合条件
#    - 1: 符合条件
#    - 2: 待定（信息不全）
sub check_cdn_filter {
  my $filter        = shift;
  my $ip_block_info = shift;

  # 检查 include 列表, 要求每条记录都必须匹配, 如果不在 include 列表, 直接返回 0 (不符合条件)
  for my $key ( keys %{ $filter->{"include"} } ) {
    return 0 unless ( defined $ip_block_info->{$key} );
    my $match = 0;
    if ( $key eq 'geoip') {
      $ip_block_info->{"geoip"}->{"isp"} eq 'N/A' and return 2;
      
      my $location_info = $ip_block_info->{"geoip"};
      for my $geoip_key ( keys %{ $filter->{"include"}->{"geoip"} } ) {
        my $filter_values = $filter->{"include"}->{"geoip"}->{$geoip_key};
        my $location_value = $location_info->{$geoip_key};
        for my $filter_value (@$filter_values) {
          if ( $location_value eq $filter_value ) {
            $match = 1;
            last;
          }
        }
        $match == 0 and return 0;
      }
    } else {
      for my $condition ( @{ $filter->{"include"}->{$key} } ) {
        # 如果同一 Key 下的匹配条件有多个, 满足一个即可退出
        $ip_block_info->{$key} =~ /$condition/ and $match = 1 and last;
      }

      # 当前 key 下所有条件均不满足, 则表示不满足白名单规则
      $match == 0 and return 0;
    }
  }

  # 检查 exclude 列表, 如果匹配, 则返回 0 (不符合条件)
  for my $key ( keys %{ $filter->{"exclude"} } ) {
    if ( defined $ip_block_info->{$key} ) {
      my $match = 0;
      if ( $key eq 'geoip') {        
        my $location_info = $ip_block_info->{"geoip"};
        for my $geoip_key ( keys %{ $filter->{"exclude"}->{"geoip"} } ) {
          my $filter_values = $filter->{"exclude"}->{"geoip"}->{$geoip_key};
          my $location_value = $location_info->{$geoip_key};
          for my $filter_value (@$filter_values) {
            if ( $location_value eq $filter_value ) {
              $match = 1;
              last;
            }
          }

          # 当前 key 下满足任意一个条件, 则表示满足 exclude 列表规则
          $match == 1 and return 0;
        }
      } elsif ( $key eq 'ip_block' ) {
        # 遍历 exclude 列表中的 IP 地址段
        for my $condition ( @{ $filter->{"exclude"}->{$key} } ) {
          # 检查当前 IP 地址段是否在排除的 CIDR 范围内
          # 如果在范围内,设置 match=1 并退出循环
          Net::CIDR::cidrlookup($ip_block_info->{$key}, $condition) and $match = 1 and last;
        }
        # 如果当前 IP 地址段在排除的 CIDR 范围内, 则返回 0 (不符合条件)
        $match == 1 and return 0;
      } else {
        for my $condition ( @{ $filter->{"exclude"}->{$key} } ) {
          # 如果同一 Key 下的匹配条件有多个, 满足一个即可退出
          $ip_block_info->{$key} =~ /$condition/ and $match = 1 and last;
        }

        # 当前 key 下满足任意一个条件, 则表示满足 exclude 列表规则
        $match == 1 and return 0;
      }
    }
  }

  # 通过以上所有检查, 默认返回 1 (符合条件)
  return 1;
}

1;
