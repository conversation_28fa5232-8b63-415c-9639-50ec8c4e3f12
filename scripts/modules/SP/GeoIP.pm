package SP::GeoIP;

use strict;
use warnings;

use LWP::UserAgent;

use SP::Common;
use SP::JSON;
use SP::NetAddr;
use SP::SQLite;

use JSON::XS;
use Encode qw(encode_utf8);

use utf8;
use open qw( :std :encoding(utf8) );
binmode( STDOUT, ":utf8" );
binmode( STDERR, ":utf8" );


# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  sp_geoip
  get_geoip_info
  get_geoip_location
  extract_location_info
  regularize_location_str
  regularize_isp_str
  gen_geoip_cfg
  refresh_geoip_cfg
  geoip_info_to_str
);

# 模块版本
our $VERSION = '1.00';

my $region_names = {

  # 国家列表
  "countries" => [
    qw(
      阿富汗
      阿尔巴尼亚
      阿尔及利亚
      美属萨摩亚
      安道尔
      安哥拉
      安圭拉岛
      南极洲
      安提瓜和巴布达
      阿根廷
      亚美尼亚
      阿鲁巴岛
      澳大利亚
      奥地利
      阿塞拜疆
      巴哈马
      巴林
      孟加拉国
      巴巴多斯
      白俄罗斯
      比利时
      伯利兹
      贝宁
      百慕大
      不丹
      玻利维亚
      波斯尼亚和黑塞哥维那
      博茨瓦纳
      巴西
      英属印度洋领地
      英属维尔京群岛
      文莱
      保加利亚
      布基纳法索
      缅甸
      布隆迪
      柬埔寨
      喀麦隆
      加拿大
      佛得角
      开曼群岛
      中非共和国
      乍得
      智利
      中国
      圣诞岛
      克利珀顿岛
      科科斯（基林）群岛
      哥伦比亚
      科摩罗
      刚果民主共和国
      刚果共和国
      库克群岛
      珊瑚海群岛
      哥斯达黎加
      科特迪瓦
      克罗地亚
      古巴
      塞浦路斯
      捷克共和国
      丹麦
      吉布地
      多米尼克
      多明尼加共和国
      厄瓜多尔
      埃及
      萨尔瓦多
      赤道几内亚
      厄立特里亚
      爱沙尼亚
      埃塞俄比亚
      欧罗巴岛
      福克兰群岛
      法罗群岛
      斐济
      芬兰
      法国
      法属圭亚那
      法属波利尼西亚
      加蓬
      冈比亚
      乔治亚
      德国
      加纳
      直布罗陀
      格洛里厄斯群岛
      希腊
      格陵兰
      格林纳达
      瓜德罗普岛
      关岛
      危地马拉
      根西岛
      几内亚
      几内亚比绍
      圭亚那
      海地
      梵蒂冈
      洪都拉斯
      匈牙利
      冰岛
      印度
      印度尼西亚
      伊朗
      伊拉克
      爱尔兰
      马恩岛
      以色列
      意大利
      牙买加
      扬马延岛
      日本
      泽西岛
      约旦
      新胡安岛
      哈萨克斯坦
      肯尼亚
      基里巴斯
      科威特
      吉尔吉斯斯坦
      老挝
      拉脱维亚
      黎巴嫩
      莱索托
      利比里亚
      利比亚
      列支敦士登
      立陶宛
      卢森堡
      马其顿
      马达加斯加
      马拉维
      马来西亚
      马尔代夫
      马里
      马耳他
      马绍尔群岛
      马提尼克岛
      毛里塔尼亚
      毛里求斯
      马约特岛
      墨西哥
      密克罗尼西亚联邦
      摩尔多瓦
      摩纳哥
      蒙古
      蒙特塞拉特
      摩洛哥
      莫桑比克
      纳米比亚
      瑙鲁
      纳瓦萨岛
      尼泊尔
      荷兰
      荷属安的列斯
      新喀里多尼亚
      新西兰
      尼加拉瓜
      尼日尔
      尼日利亚
      纽埃
      诺福克岛
      朝鲜
      北马里亚纳群岛
      挪威
      阿曼
      巴基斯坦
      帕劳
      巴拿马
      巴布亚新几内亚
      西沙群岛
      巴拉圭
      秘鲁
      菲律宾
      皮特凯恩群岛
      波兰
      葡萄牙
      波多黎各
      卡塔尔
      留尼汪
      罗马尼亚
      俄罗斯
      卢旺达
      圣赫勒拿岛
      圣基茨和尼维斯
      圣卢西亚岛
      圣皮埃尔和密克隆群岛
      圣文森特和格林纳丁斯
      萨摩亚
      圣马力诺
      圣多美和普林西比
      沙特阿拉伯
      塞内加尔
      塞尔维亚和黑山
      塞舌尔群岛
      塞拉利昂
      新加坡
      斯洛伐克
      斯洛文尼亚
      所罗门群岛
      索马里
      南非
      韩国
      西班牙
      南沙群岛
      斯里兰卡
      苏丹
      苏里南
      斯瓦尔巴群岛
      斯威士兰
      瑞典
      瑞士
      叙利亚
      塔吉克斯坦
      坦桑尼亚
      泰国
      东帝汶
      多哥
      托克劳
      汤加
      特立尼达和多巴哥
      特罗姆兰岛
      突尼斯
      土耳其
      土库曼斯坦
      特克斯和凯科斯群岛
      图瓦卢
      乌干达
      乌克兰
      阿拉伯联合酋长国
      英国
      美国
      乌拉圭
      乌兹别克斯坦
      瓦努阿图
      委内瑞拉
      越南
      维尔京群岛
      威克岛
      瓦利斯和富图纳群岛
      西撒哈拉
      也门
      赞比亚
      津巴布韦
    )
  ],

  # 特别行政区列表
  "special_administrative_regions" => [qw(香港 澳门)],

  # 直辖市列表
  "municipalities" => [qw(北京 上海 天津 重庆)],

  # 自治区列表
  "autonomous_regions" => [qw(新疆 西藏 内蒙古 广西 宁夏)],

  # 省列表
  "provinces" =>
    [qw(河北 山西 辽宁 吉林 黑龙江 江苏 浙江 安徽 福建 江西 山东 河南 湖北 湖南 广东 海南 四川 贵州 云南 陕西 甘肃 青海 台湾)],

  # 自治州列表
  "autonomous_prefectures" => [
    qw(巴音郭楞 博尔塔拉 甘南 甘孜 果洛 大理 伊犁 克孜勒苏柯尔克孜 临夏 凉山 怒江 西双版纳 海北 海南 黄南 昌吉 楚雄 玉树 延边 阿坝 文山 德宏 海西 红河 黔东南 黔南 黔西南 恩施 湘西)
  ],
};

# 将 geoip_info 哈希转换为字符串
sub geoip_info_to_str {
  my $geoip = shift;

  my ( $country, $province, $city, $isp ) = (
    $geoip->{"country"}, $geoip->{"province"},
    $geoip->{"city"},    $geoip->{"isp"},
  );

  my $location = "";
  $country eq "N/A" and $country = "";
  $location .= $country;

  $province eq "N/A" and $province = "";
  $location .= $province;
  if ( grep { $_ eq $province } @{ $region_names->{provinces} } ) {
    $location .= "省";
  }
  elsif ( grep { $_ eq $province } @{ $region_names->{autonomous_regions} } ) {
    $location .= "自治区";
  }
  elsif ( grep { $_ eq $province }
    @{ $region_names->{special_administrative_regions} } )
  {
    $location .= "特别行政区";
  }
  elsif ( grep { $_ eq $province } @{ $region_names->{municipalities} } ) {
    $location .= "市";
  }

  $city eq "N/A" and $city = "";
  $location .= $city;
  if ( grep { $_ eq $city } @{ $region_names->{autonomous_prefectures} } ) {
    $location .= "州";
  }
  elsif ( $city ne "" ) {
    $location .= "市";
  }

  $location .= " " . $geoip->{"isp"};
  $location =~ s/^ //;

  return $location;
}

# 根据输入的 IP/prefix, 查找并返回 GeoIP 信息
sub get_geoip_info {
  my $geoip_mapping = shift;
  my $netaddr       = shift;

  my $geoip_info = {
    "country"  => "N/A",
    "province" => "N/A",
    "city"     => "N/A",
    "isp"      => "N/A"
  };

  my $ip = NetAddr::IP->new($netaddr);

  unless ( defined $ip ) {
    print "[GeoIP]: Invalid address: '", $netaddr, "'\n";
    return $geoip_info;
  }

  # IPv4 地址, 按照 /24 掩码聚合; IPv6 地址, 按照 /64 前缀聚合
  my $geoip;
  if ( $ip->version() == 4 ) {
    $geoip = $geoip_mapping->{ get_network_addr( $ip->addr() . "/24" ) };
  }
  elsif ( $ip->version() == 6 ) {
    $netaddr = get_network_addr( $ip->addr() . "/64" );

    # 简化地址
    $netaddr =~ s/0000:0000:0000:0000$//;
    $geoip = $geoip_mapping->{$netaddr};
  }
  else {
    return $geoip_info;
  }

  defined($geoip) and $geoip_info = $geoip;

  return $geoip_info;
}

# 根据输入的 IP/prefix, 查找并返回 Geo Location
sub get_geoip_location {
  my $geoip_mapping = shift;
  my $netaddr       = shift;

  my $geoip = get_geoip_info( $geoip_mapping, $netaddr );

  my $location = geoip_info_to_str($geoip);
  
  return $location;
}

sub sp_geoip {
  my $ipaddr = shift;

  my $ip = NetAddr::IP->new($ipaddr);

  return "N/A" unless defined $ip;

  my $netaddr;
  if ( defined $ip ) {
    if ( $ip->version == 4 ) {
      $netaddr = $ip->addr();
    }
    elsif ( $ip->version == 6 ) {
      $netaddr = $ip->full6();
    }
  }

  # 初始化 UserAgent 对象
  my $ua = LWP::UserAgent->new;
  $ua->default_header( 'Content-Type' => 'application/json' );
  $ua->default_header( 'Accept'       => '*/*' );
  $ua->default_header( 'User-Agent'   => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36' );
  # $ua->proxy(['http', 'https'], 'http://127.0.0.1:9090');

  # 发送 HTTP 请求
  my $response = $ua->get("https://ip.zxinc.org/api.php?type=json&ip=$netaddr");

  # 如果请求失败，直接返回
  return "N/A" unless $response->is_success;

  # 解析 JSON 响应
  my $content = $response->decoded_content;
  my $json    = decode_json( encode_utf8($content) );

  # 检查状态码
  return "N/A" unless defined $json->{code} && $json->{code} == 0;

  # 获取 location 字段并处理
  my $location = $json->{data}->{location} // '';
  $location =~ s/[–]+//g;           # 替换自带分隔符
  $location =~ s/\x{3000}/ /g;      # 替换全角空格
  $location =~ s/[\t\n\r]//g;       # 删除制表符、新行符和回车符
  $location =~ s/[[:cntrl:]]//g;    # 删除控制字符

  return $location;
}

sub regularize_location_str {
  my $location = shift;

  return "N/A" if $location =~ /N\/A/;

  my $matched = 0;

  # 特别行政区
  for my $region ( @{ $region_names->{special_administrative_regions} } ) {
    $location =~ /$region/ and $matched = 1;
    $location =~ s/$region(?!特别行政区)/${region}特别行政区/;
    $location =~ s/${region}特别行政区(.*?)$/${region}特别行政区/;
  }

  # 直辖市
  for my $city ( @{ $region_names->{municipalities} } ) {
    $location =~ /$city/ and $matched = 1;
    $location =~ s/$city(?!市)/${city}市/;
    $location =~ s/${city}市(.*?)$/${city}市/;
  }

  # 自治区
  for my $region ( @{ $region_names->{autonomous_regions} } ) {
    $location =~ /$region/ and $matched = 1;
    $location =~ s/$region(?!自治区)/${region}自治区/;
    $location =~ s/自治区区/自治区/;
  }

  # 省
  for my $province ( @{ $region_names->{provinces} } ) {
    $location =~ /$province/ and $matched = 1;
    $location =~ s/$province(?!省)/${province}省/;
  }

  # 自治州
  my $is_autonomous_prefectures = 0;
  for my $prefecture ( @{ $region_names->{autonomous_prefectures} } ) {
    $location =~ /$prefecture/ and $is_autonomous_prefectures = 1;
    $location =~ s/$prefecture(?!州)$/${prefecture}州/;
  }

  # 普通城市
  if ( !$is_autonomous_prefectures ) {
    $location =~ /[国省市区县]+$/ or $location .= "市";
    $location =~ s/([市]+).*[县区]+$/$1/;
  }

  # 位于国内, 添加“中国”
  if ( $matched and $location !~ /^中国/ ) {
    $location = "中国" . $location;
  }

  return $location;
}

sub regularize_isp_str {
  my $isp = shift;

  return "N/A" unless defined $isp;

  if ($isp =~ /移动/) {
    return "移动";
  } elsif ($isp =~ /电信/) {
    return "电信";
  } elsif ($isp =~ /联通/) {
    return "联通";
  } elsif ($isp =~ /铁通/) {
    return "铁通";
  } else {
    return $isp;
  }
}

sub extract_location_info {
  my $location = shift;
  my ( $country, $province, $city ) = ( "", "", "" );

  return ( "N/A", "N/A", "N/A" ) if $location =~ /N\/A/;

  # 国家
  for my $region ( @{ $region_names->{countries} } ) {
    if ( $location =~ /^${region}/ ) {
      $country = $region;
      $location =~ s/^.*?${region}//;
    }
  }

  # 特别行政区
  for my $region ( @{ $region_names->{special_administrative_regions} } ) {
    if ( $location =~ /${region}特别行政区/ ) {
      $province = $region;
      $location =~ s/^.*?${region}特别行政区//;
    }
  }

  # 直辖市
  for my $region ( @{ $region_names->{municipalities} } ) {
    if ( $location =~ /${region}市/ ) {
      $province = $region;
      $location =~ s/^.*?${region}市//;
    }
  }

  # 自治区
  for my $region ( @{ $region_names->{autonomous_regions} } ) {
    if ( $location =~ /${region}自治区/ ) {
      $province = $region;
      $location =~ s/^.*?${region}自治区//;
    }
  }

  # 省
  for my $region ( @{ $region_names->{provinces} } ) {
    if ( $location =~ /${region}省/ ) {
      $province = $region;
      $location =~ s/^.*?${region}省//;
    }
  }

  # 自治州
  my $is_autonomous_prefectures = 0;
  for my $region ( @{ $region_names->{autonomous_prefectures} } ) {
    if ( $location =~ /${region}州/ ) {
      $city = $region;
      $location =~ s/^.*?${region}州//;
      $is_autonomous_prefectures = 1;
    }
  }

  # 普通城市
  if ( $location =~ /(.*)[市县区]+$/ && length($1) >= 2 ) {
    $city = $1;
  }
  elsif ( !$is_autonomous_prefectures ) {
    $city = $location;
  }

  return ( $country, $province, $city );
}

# 根据在线网站解析结果生成 GeoIP 配置
#
# 说明：
#    - 读取 <STDIN> 输入, 内容为 IP Location ISP
#    - 如果输入部分只有 IP, 没有 Location 及 ISP 字段，则自动从本地数据库查询
#      如果本地查询失败, 则自动进行远程查询
# 返回:
#    - 在 <STDOUT> 打印输出 JSON 格式的 GeoIP 配置文件内容
sub gen_geoip_cfg {

  # 初始化一个 HASH REF, 用于存储每一行的数据
  my $data;

  # 打开数据库
  my $dbh = db_open(CDN_DB_FILE);

  # 加载 GeoIP Mapping
  my $geoip_mapping = load_sp_geoip($dbh);

  # 关闭数据库
  db_close($dbh);

  print "Please input data (ip location isp): \n";

  # 从标准输入读取数据
  while (<STDIN>) {
    chomp;                                                   # 去掉每行末尾的换行符
    my ( $ipaddr, $location, $isp ) = split( /\s+/, $_ );    # 假设数据是用空格分隔的

    defined $location or $location = "N/A";
    defined $isp      or $isp      = "N/A";

    my $ip = NetAddr::IP->new($ipaddr);
    my $netaddr;
    if ( defined $ip ) {
      if ( $ip->version == 4 ) {
        $netaddr = get_network_addr( $ip->addr() . "/24" );
      }
      elsif ( $ip->version == 6 ) {
        $netaddr = get_network_addr( $ip->addr() . "/64" );
      }
    }
    else {
      print "Invalid input: ", $_, "\n";
      next;
    }

    if ( $location eq "N/A" ) {
      # 查询本地缓存
      ( $location, $isp ) = split /\s+/,
        get_geoip_location( $geoip_mapping, $netaddr );
      if ( $location eq "N/A" ) {
        # 查询远程服务器
        ( $location, $isp ) = split /\s+/, sp_geoip($netaddr);
      }
    }

    # 规范化 location 和 isp 字符串
    $location = regularize_location_str($location);
    $isp      = regularize_isp_str($isp);

    $location eq "N/A" and $isp = "";

    # 简化地址
    $netaddr =~ s/0000:0000:0000:0000$//;

    # 将每一行数据存储到数组中
    $data->{$netaddr} = {
      ip       => $netaddr,
      location => $location . " " . $isp,
    };

    # 缓存当前结果, 避免重复向远程服务器查询
    my ($country, $province, $city) = extract_location_info($location);
    $geoip_mapping->{$netaddr} = {
      ip       => $netaddr,
      country  => $country,
      province => $province,
      city     => $city,
      isp      => $isp,
    };
  }

  print "\n";

  # my $json = JSON::XS->new->pretty->canonical;
  # print $json->encode($data), "\n";

  for my $netaddr ( keys %{$data} ) {
    my ( $location, $isp ) = split /\s+/, $data->{$netaddr}->{"location"};
    my ( $country, $province, $city ) = extract_location_info($location);
    printf( "%s,%s,%s,%s,%s\n", $netaddr, $country, $province, $city, $isp );
  }
}

# 刷新当前 GeoIP 映射库
#
# 说明：
#    - (无)
# 返回:
#    - (无)
# 依赖文件:
#    - SP_GEOIP_FILE 原始 GeoIP 映射文件
# 输出文件:
#    - SP_GEOIP_FILE 原始 GeoIP 映射文件
# 调用示例：
#    refresh_geoip_cfg
sub refresh_geoip_cfg {

  # 加载 GeoIP Mapping
  my $geoip_mapping = load_json_file(SP_GEOIP_FILE);

  # 按照 KEY (IP) 顺序排序
  tie my %h, 'Hash::Ordered';

  # 创建一个 Hash::Ordered 对象
  my $ordered_hash = tied %h;

  # 将排序后的哈希数组插入到 Hash::Ordered 对象中
  foreach my $ip (
    sort { ip_to_packed($a) cmp ip_to_packed($b) }
    keys %{$geoip_mapping}
    )
  {
    $ordered_hash->push( $ip, $geoip_mapping->{$ip} );
  }

  save_json_file( \%h, SP_GEOIP_FILE );
}

1;
