package SP::Domain;

use strict;
use warnings;

use SP::Common;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  get_second_level_domain
  get_third_level_domain
  load_suffix_list
);

# 模块版本
our $VERSION = '1.00';


sub get_third_level_domain {
  my ( $domain, $suffixes ) = @_;
  my @parts = split( /\./, $domain );

  for my $i ( 0 .. $#parts ) {
    my $candidate = join( '.', @parts[ $i .. $#parts ] );
    if ( grep { $_ eq $candidate } @$suffixes ) {
      if ( $i - 2 >= 0 ) {
        return join( '.', @parts[ $i - 2, $i - 1, $i .. $#parts ] );
      }
      elsif ( $i - 1 >= 0 ) {
        return join( '.', @parts[ $i - 1, $i .. $#parts ] );
      }
      return $candidate;
    }
  }
  return $domain;
}

sub get_second_level_domain {
  my ( $domain, $suffixes ) = @_;
  my @parts = split( /\./, $domain );

  for my $i ( 0 .. $#parts ) {
    my $candidate = join( '.', @parts[ $i .. $#parts ] );
    if ( grep { $_ eq $candidate } @$suffixes ) {
      my $second_level = join( '.', @parts[ $i - 1, $i, $#parts ] );
      if ( $i - 1 >= 0 ) {
        return join( '.', @parts[ $i - 1, $i .. $#parts ] );
      }
      return $candidate;
    }
  }
  return $domain;
}

sub load_suffix_list {
  my $file = DOMAIN_SUFFIX_FILE;
  open my $fh, '<', $file or die "Cannot open $file $file: $!";
  my @suffixes;

  while ( my $line = <$fh> ) {
    chomp $line;
    next if $line =~ /^\s*$/;    # Skip empty lines
    next if $line =~ /^\/\//;    # Skip comments
    push @suffixes, $line;
  }

  close $fh;
  return \@suffixes;
}

1;