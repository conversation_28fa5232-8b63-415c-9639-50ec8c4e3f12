package SP::NetAddr;

use strict;
use warnings;

use Socket qw(inet_pton inet_ntop AF_INET AF_INET6);
use NetAddr::IP;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  aggregate_ipv4_blocks
  aggregate_ipv6_blocks
  get_ip_prefix
  get_network_addr
  ip_to_packed
  uniq
);

# 模块版本
our $VERSION = '1.00';


sub ip_to_packed {
  my $ip = shift;
  my $packed_ip;

  defined $ip or return "NA";

  if ( $ip =~ /(.*?)\/(.*)/ ) {
    $ip = $1;
  }

  # Try to convert IPv4
  if ( inet_pton( AF_INET, $ip ) ) {
    $packed_ip = inet_pton( AF_INET6, "::ffff:$ip" );
  }

  # Try to convert IPv6
  elsif ( $packed_ip = inet_pton( AF_INET6, $ip ) ) {

    # Already packed, no further action needed
  }
  else {
    $packed_ip = "NA";
  }

  return $packed_ip;
}

sub compare_ip {
  my $packed_ip1 = ip_to_packed($a);
  my $packed_ip2 = ip_to_packed($b);

  return $packed_ip1 cmp $packed_ip2;
}

sub uniq {
  my @array = @_;
  my %hash;
  @hash{@array} = ();
  return keys %hash;
}

# 按照 /24 掩码 聚合 IPv4 地址
sub aggregate_ipv4_blocks {
  my @ips = @_;
  my @cidr_blocks;

  foreach my $ip (@ips) {
    my $cidr_block = get_network_addr( $ip . "/24" );
    push @cidr_blocks, $cidr_block . "/24" if $cidr_block ne "";
  }

  my @uniq_sorted_blocks = sort compare_ip uniq(@cidr_blocks);

  return \@uniq_sorted_blocks;
}

# 按照 /120 掩码聚合 IPv6 地址
sub aggregate_ipv6_blocks {
  my @ips = @_;
  my @cidr_blocks;

  foreach my $ip (@ips) {
    my $cidr_block = get_network_addr( $ip . "/120" );

    # print "IP: $ip\t CIDR: $cidr_block \n";
    push @cidr_blocks, $cidr_block . "/120" if $cidr_block ne "";
  }

  my @uniq_sorted_blocks = sort compare_ip uniq(@cidr_blocks);

  return \@uniq_sorted_blocks;
}

# 根据输入的 IP/prefix, 计算并返回 network 地址
sub get_network_addr {
  my $ip_with_prefix = shift;

  my $ip = NetAddr::IP->new($ip_with_prefix);

  return "" unless ( defined $ip );

  if ( $ip->version() == 4 ) {
    return $ip->network()->addr();
  }
  else {
    my $full6 = $ip->network()->full6();

    return lc($full6);
  }
}

# 根据输入的 IP/prefix, 计算并返回 ip prefix 地址
sub get_ip_prefix {
  my $ip_with_prefix = shift;

  my $ip = NetAddr::IP->new($ip_with_prefix);

  unless ( defined $ip ) {
    print "[IPPrefix]: Invalid address: '", $ip_with_prefix, "'\n";
    return "N/A";
  }

  # IPv4 地址, 按照 /24 掩码聚合; IPv6 地址, 按照 /64 前缀聚合
  my $ip_prefix;
  if ( $ip->version() == 4 ) {
    $ip_prefix = get_network_addr( $ip->addr() . "/24" );
  }
  elsif ( $ip->version() == 6 ) {
    $ip_prefix = get_network_addr( $ip->addr() . "/64" );

    # 简化地址
    $ip_prefix =~ s/0000:0000:0000:0000$//;
  }
  else {
    return "N/A";
  }

  return lc($ip_prefix);
}


1; # 模块必须以 1 或真值结尾