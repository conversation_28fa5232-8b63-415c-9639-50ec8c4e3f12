package SP::Task;

use strict;
use warnings;

use utf8;
use open qw(:std :encoding(UTF-8));
binmode( STDOUT, ":utf8" );

use File::Basename;
use Digest::MD5;
use Archive::Zip;
use POSIX qw(strftime);
use Text::CSV;

use SP::Common;
use SP::JSON;
use SP::YAML;
use SP::DNS;
use SP::URL;
use SP::SQLite;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  generate_task_bundle
  deploy_task_bundle
  evaluate_task_bundle
  generate_task_config
  deploy_task_config
);

# task_list.json 说明：
# - 任务名：task-<主题>-<包含条件描述>-Ex-<排除条件描述>-<编号>
#   - 包含条件描述：使用缩写和值描述任务包含的条件。
#   - 排除条件描述 (Ex): 明确指出被排除的条件，使用 `Ex` 前缀。
#   - 条件缩写：
#     - Domain (D): 如 apple.com
#     - CDN Domain (CDN): 如 cdn.apple.com
#     - Record Type (RT): 如 A, AAAA, CNAME
#     - ISP (ISP): 如 移动, 联通
#     - Region (R): 如 Tier1, EastChina, Central, WestChina, NorthChina, Midwest
#     - Province (Prov): 如 江苏, 浙江
#     - Service Type (ST): 如 IDC, 互联网专线
#     - ProviderID (P): 如 alicdn, tencent
#   - 例子：
#     - 假设您有一个任务是为apple.com域名生成A记录类型的CDN配置，针对所有ISP，但需要排除江苏省的记录，可以命名为：
#       - task-Pull-D-apple.com-RT-A-Ex-Prov-js-01
# - 文件内容:
#   - 范例:
#     {
#       "include": {
#         "domain": ["swcdn.apple.com", "updates.cdn-apple.com"],
#         "isp": "移动",
#         "serviceType": ["IDC外部客户", "互联网专线"]
#       },
#       "exclude": {
#         "province": "江苏",
#         "serviceType": ["自有业务", "BLANK", "其他"]
#       }
#     }
#   - 结构说明：  
#     - include: 包含的条件，满足这些条件的记录将被选取。
#     - exclude: 排除的条件，满足这些条件的记录将被排除。
#     - 每个条件可以包含以下字段:
#       - domain: 域名，如 apple.com
#       - cdn_domain: CDN 域名，如 cdn.apple.com
#       - record_type: 记录类型，如 A, AAAA, CNAME
#       - isp: ISP，如 移动, 联通
#       - province: 省份，如 江苏, 浙江
#       - serviceType: 服务类型，如 IDC, 互联网专线
#         - 以下是中文术语及其对应的英文简写：
#           - IDC外部客户 - IDC_EC (IDC External Customer)
#           - 互联网专线 - ILL (Internet Leased Line)
#           - 家宽和企宽 - HBEB (Home Broadband and Enterprise Broadband)
#           - 自有业务 - OB (Owned Business)
#           - BLANK - BLK (Blank)
#           - 其他 - OTH (Other)
#       - providerID: 提供商 ID，如 alicdn, tencent
#  
sub generate_task_bundle {
  my ($task_name, $conditions) = @_;

  if ( !$task_name ) {
    print "ERROR: Task name is required\n";
    return;
  }

  my $task_list = load_json_file(TASK_LIST_FILE);

  if ( $task_name eq 'all' ) {
    print "==> Generating task bundles for all tasks\n";
    for my $name (keys %$task_list) {
      # Skip tasks marked as evaluate_only
      if (exists $task_list->{$name}->{evaluate_only} && $task_list->{$name}->{evaluate_only}) {
        print "  -> Skipping task '$name' (evaluate_only)\n";
        next;
      }
      my $merged_conditions = { %{$task_list->{$name}->{conditions}}, %$conditions };
      generate_single_task_bundle($name, $task_list->{$name}->{description}, $merged_conditions);
    }
  } else {
    if ( !exists $task_list->{$task_name} ) {
      print "ERROR: Task $task_name not found\n";
      return;
    }
    # Skip tasks marked as evaluate_only
    if (exists $task_list->{$task_name}->{evaluate_only} && $task_list->{$task_name}->{evaluate_only}) {
      print "==> Skipping task '$task_name' (evaluate_only)\n";
      return;
    }
    my $merged_conditions = { %{$task_list->{$task_name}->{conditions}}, %$conditions };
    generate_single_task_bundle($task_name, $task_list->{$task_name}->{description}, $merged_conditions);
  }
}

sub generate_single_task_bundle {
  my ($task_name, $task_description, $task_conditions) = @_;

  print "==> Generating task bundle for '" . $task_name . "'\n";
  print "  -> Description: $task_description\n";

  # 创建任务目录
  my $bundle_dir = TASK_BUNDLE_DIR . "/" . $task_name;
  mkdir $bundle_dir unless -d $bundle_dir;

  # 创建任务描述文件
  print "  -> Generating task description ... \n";
  my $description_file = "$bundle_dir/task_description.json";
  my $description_data = {
    description => $task_description,
    conditions  => $task_conditions,
    timestamp   => strftime("%Y-%m-%dT%H:%M:%S%z", localtime)
  };
  save_json_file($description_data, $description_file);
  print "    > Task description file created: ", basename($description_file), "\n";

  # 创建任务配置文件
  print "  -> Generating DNS configuration ... \n";
  my $dns_conf = "$bundle_dir/sp_dns.json";
  generate_dns_cfg($task_conditions, $dns_conf);

  # 从 DNS 配置文件中获取域名
  my $url_cfg = load_json_file($dns_conf);
  my @domains = keys %$url_cfg;
  # 如果域名数量为 0，则跳过 URL 配置
  if (scalar @domains == 0) {
    print "  -> No domains found, skipping URL configuration\n";
    # 删除目录下的所有文件
    opendir(my $dh, $bundle_dir) || die "Can't open directory: $!";
    while (my $file = readdir($dh)) {
      next if $file =~ /^\.\.?$/;  # 跳过 . 和 ..
      unlink "$bundle_dir/$file";
    }
    closedir($dh);
    # 删除目录本身
    rmdir $bundle_dir;

    # 输出错误信息
    print "  -> Task bundle generation failed\n";
    return;
  }
  
  $task_conditions->{include}->{domain} = \@domains;

  print "  -> Generating URL configuration ... \n";
  my $url_conf = "$bundle_dir/sp_url.json";
  generate_url_cfg($task_conditions, $url_conf);

  # 生成校验文件
  print "  -> Generating checksum file ... \n";
  my $checksum_file = "$bundle_dir/checksum.md5";
  generate_checksum_file($checksum_file, $bundle_dir, $dns_conf, $url_conf);

  # 生成任务包
  print "  -> Generating task bundle ... \n";
  pack_task_bundle($bundle_dir);
}

# 评估任务包
sub evaluate_task_bundle {
  my ($task_name, $conditions) = @_;

  my $task_list = load_json_file(TASK_LIST_FILE);

  my $merged_conditions = { %{$task_list->{$task_name}->{conditions}}, %$conditions };

  dns_cfg_stat($merged_conditions);
}

# 打包任务包
sub pack_task_bundle {
  my ($bundle_dir) = @_;

  my $task_name = basename($bundle_dir);

  my $task_bundle = TASK_BUNDLE_DIR . "/" . $task_name . ".zip";
  my $zip = Archive::Zip->new();
  
  # 遍历 $bundle_dir 下的文件
  opendir(my $dh, $bundle_dir) || die "Can't open directory: $!";
  while (my $file = readdir($dh)) {
    next if $file =~ /^\.\.?$/;  # 跳过 . 和 ..
    my $full_path = "$bundle_dir/$file";
    if (-f $full_path) {
      $zip->addFile($full_path, $file);
    }
  }
  closedir($dh);
  
  $zip->writeToFileNamed($task_bundle);

  print "    > Task bundle created: ", basename($task_bundle), "\n";
}

# 生成校验文件
sub generate_checksum_file {
  my ($checksum_file, $bundle_dir, @files) = @_;

  open my $checksum_fh, '>', $checksum_file or die "Could not open file '$checksum_file' $!";

  for my $file (@files) {
    open my $fh, '<', $file or die "Could not open file '$file' $!";
    binmode $fh; 
    my $md5 = Digest::MD5->new->addfile($fh)->hexdigest;
    close $fh;

    my $relative_path = File::Spec->abs2rel($file, $bundle_dir);
    print $checksum_fh "$md5  $relative_path\n";
    print "    > $relative_path -> $md5\n";
  }

  close $checksum_fh;
}

# 部署任务包
sub deploy_task_bundle {
  my ($task_name, $remote_server) = @_;

  if ($task_name eq 'all') {
    print "==> Deploying all task bundles\n";
    opendir(my $dh, TASK_BUNDLE_DIR) || die "Can't open directory: $!";
    while (my $file = readdir($dh)) {
      next unless $file =~ /\.zip$/;
      my $bundle_name = basename($file, '.zip');
      deploy_single_bundle($bundle_name, $remote_server);
    }
    closedir($dh);
  } else {
    deploy_single_bundle($task_name, $remote_server);
  }
}

sub deploy_single_bundle {
  my ($task_name, $remote_server) = @_;

  print "==> Deploying task bundle for $task_name\n";

  my $task_bundle = TASK_BUNDLE_DIR . "/" . $task_name . ".zip";

  # 检查任务包是否存在且大小大于0
  unless (-f $task_bundle) {
    print "  -> Task bundle not found: $task_bundle\n";
    return;
  }

  unless (-s $task_bundle) {
    print "  -> Task bundle is empty: $task_bundle\n"; 
    return;
  }

  my $path = "/tasks";
  my $remote_path = $remote_server =~ s/\/$//r . '/' . $path =~ s/^\/+//r;
  
  print "  -> Uploading $task_bundle to $remote_path\n";
  
  # 生成临时文件名
  my $tmp_archive = $task_name . "_" . time() . ".zip";

  # 上传到临时文件
  print "    > Uploading to temp file: $tmp_archive\n";
  my $upload_cmd = "curl -s -T \"$task_bundle\" \"$remote_server/$path/$tmp_archive\"";
  system($upload_cmd);

  if ($? == 0) {
    print "    > Upload successful\n";
    
    # 使用 MOVE 方法覆盖原文件
    print "    > Moving temp file to final location\n";
    my $move_cmd = "curl -s -X MOVE \"$remote_server/$path/$tmp_archive\" -H \"Destination: $remote_server/$path/$task_name.zip\"";
    system($move_cmd);
    
    if ($? == 0) {
      print "==> Task bundle deployed successfully\n";
    } else {
      print "    > Failed to move temp file. Error code: $?\n";
    }
  } else {
    print "==> Upload failed. Error code: $?\n";
  }
}

sub generate_task_config {
  my $csv_file = TASK_CONFIG_FILE;
  
  # 创建 CSV 解析器
  my $csv = Text::CSV->new({
    binary => 1,
    auto_diag => 1,
  }) or die "Cannot use CSV: " . Text::CSV->error_diag();
  
  # 打开并读取 CSV 文件
  open(my $fh, '<', $csv_file) or die "Cannot open $csv_file: $!";
  
  # 读取标题行
  my $headers = $csv->getline($fh);
  
  # 初始化结果哈希
  my %result;
  
  # 处理每一行数据
  while (my $row = $csv->getline($fh)) {
    my %row;
    @row{@$headers} = @$row;
    
    my $ip = $row{vm_ip_address};
    next unless $ip;
    
    # 初始化该 IP 的数据结构
    $result{$ip} //= {
      v4 => {},
      v6 => {},
    };
    
    # 处理每个字段
    for my $key (keys %row) {
      if ($key =~ /^v4_(.+)/) {
        # v4 相关字段
        $result{$ip}{v4}{$1} = $row{$key};
      }
      elsif ($key =~ /^v6_(.+)/) {
        # v6 相关字段
        $result{$ip}{v6}{$1} = $row{$key};
      }
      elsif ($key ne 'vm_ip_address') {
        # 其他字段
        $result{$ip}{$key} = $row{$key};
      }
    }
  }

  close $fh;
  
  return \%result;
}

sub deploy_task_config {
  my ($remote_server) = @_;

  print "==> Deploying task config\n";

  my $task_config = generate_task_config();

  # 创建任务配置目录
  my $task_config_dir = TASK_BUNDLE_DIR . "/task-Config";
  mkdir $task_config_dir unless -d $task_config_dir;

  # 保存配置为 JSON 文件
  my $config_file = $task_config_dir . "/task_config.json";
  SP::JSON::save_json_file($task_config, $config_file);

  # 生成 checksum 文件
  my $checksum_file = $task_config_dir . "/checksum.md5";
  generate_checksum_file($checksum_file, $task_config_dir, $config_file);

  # 打包 zip 文件
  pack_task_bundle($task_config_dir);

  # 上传到远程服务器
  deploy_task_bundle("task-Config", $remote_server);

  # 生成 docker compose 配置
  print "==> Generating docker compose config\n";

  # 创建 docker compose 配置目录
  my $docker_compose_config_dir = TASK_BUNDLE_DIR . "/task-Compose";
  mkdir $docker_compose_config_dir unless -d $docker_compose_config_dir;

  # 读取 docker compose 源文件
  my $docker_compose_source = load_yaml_file( DOCKER_COMPOSE_FILE );

  # Dump docker compose source for debugging
  # use Data::Dumper;
  # print "Docker compose source:\n", Dumper($docker_compose_source);

  # 生成 docker compose 配置
  for my $ip (keys %$task_config) {
    my $docker_compose_config = generate_docker_compose_config($ip, $task_config->{$ip}, $docker_compose_config_dir, $docker_compose_source);
    my $config_file = $docker_compose_config_dir . "/$ip.yaml";
    save_yaml_file($docker_compose_config, $config_file);
  }

  # 打包 docker compose 配置
  print "==> Packing docker compose config\n";
  pack_task_bundle($docker_compose_config_dir);

  # 上传 docker compose 配置
  deploy_task_bundle("task-Compose", $remote_server);
}

sub generate_docker_compose_config {
  my ($ip, $task_config, $docker_compose_config_dir, $docker_compose_source) = @_;

  # 读取 docker compose 模板文件
  my $docker_compose_config = $docker_compose_source;

  # 修改 docker compose 配置
  $docker_compose_config->{services}{downloader_v4}{deploy}{replicas} = 
    $task_config->{v4}->{replica};
  
  $docker_compose_config->{services}{downloader_v6}{deploy}{replicas} = 
    $task_config->{v6}->{replica};
  
  $docker_compose_config->{services}{x_downloader_template}{environment}{LOCAL_CONF_SERVER} = 
    $task_config->{local_conf_server};

  # 返回模板内容
  return $docker_compose_config;
}

1;
