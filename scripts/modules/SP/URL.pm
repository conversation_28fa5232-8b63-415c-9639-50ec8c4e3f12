package SP::URL;

use strict;
use warnings;

use utf8;
use open qw(:std :encoding(UTF-8));
binmode( STDOUT, ":utf8" );

use LWP::UserAgent;

use SP::Common;
use SP::JSON;
use SP::SQLite;

use Data::Dumper;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  probe_urls
  generate_url_cfg
);

# 探测URL, 并将探测结果保存到数据库 
sub probe_urls {
  my ($category) = @_;

  my $data = load_json_file(CDN_MANIFEST_FILE);
  my $browser_profiles = load_json_file(SP_PROFILES_FILE);
  my $ua = LWP::UserAgent->new(
    ssl_opts => {
      verify_hostname => 0,
      SSL_verify_mode => 0,
      SSL_use_cert => 0
    }
  );
  my $result = [];

  my $success_count = 0;
  my $failed_count  = 0;
  my @failed_probes;

  foreach my $cat ( keys %{$data} ) {
    if ( $category eq 'all' || $category eq $cat ) {
      print "==> Probing URLs for category: $cat\n";
      my $urls = $data->{$cat}->{'URLs'};
      my $referer = $data->{$cat}->{'referer'};
      my $weight = $data->{$cat}->{'weight'} // 1;
      my $profile_tag = $data->{$cat}->{'tag'} // 'default';

      # 从 sp_profiles.json 中获取浏览器配置
      my $profile;
      # 将数组格式的 sp_profiles 转换为以 tag 为键的哈希
      my %profiles_map;
      foreach my $p (@$browser_profiles) {
        foreach my $tag (@{$p->{tag}}) {
          push @{$profiles_map{$tag}}, $p;
        }
      }
      my $profiles = $profiles_map{$profile_tag};
      $profile = $profiles->[rand @$profiles] if $profiles;
      die "Profile '$profile_tag' not found in sp_profiles.json" unless $profile;

      my $headers = $profile->{'headers'} // {};
      my $user_agent = $headers->{'User-Agent'} // 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
      
      print "  -> Profile: $profile_tag\n";
      print "  -> Referer: " . (!defined $referer || $referer eq '' ? "< Not specified >" : $referer) . "\n";
      print "  -> User-Agent: $user_agent\n";

      foreach my $url (@$urls) {
        my $domain = ( $url =~ m{^https?://([^/]+)} )[0];

        # 设置User-Agent和其他请求头
        $ua->agent($user_agent);
        $ua->default_header('Referer' => "$referer") if defined $referer && $referer ne '';
        $ua->default_header('Host' => "$domain");
        
        # 添加配置文件中定义的其他请求头
        for my $header_name (keys %$headers) {
          $ua->default_header($header_name => $headers->{$header_name});
        }

        my $response = $ua->head($url);
        # # Print detailed request headers for debugging
        # print "    > Request Headers:\n";
        # foreach my $header ($ua->default_headers->header_field_names) {
        #     print "      $header: " . $ua->default_headers->header($header) . "\n";
        # }
        # # Print detailed response headers for debugging
        # print "    > Response Headers:\n";
        # foreach my $header ($response->header_field_names) {
        #     print "      $header: " . $response->header($header) . "\n";
        # }
        # print "    > Status: " . $response->status_line . "\n";
        if ( $response->is_success ) {
          $success_count++;
          my $size   = $response->header('Content-Length');
          print "  -> Probed URL: $url\n";
          print "    > domain: $domain\n";
          print "    > size: " . format_file_size($size) . "\n";
          push @$result,
            {
              category    => $cat,
              domain      => $domain,
              url         => $url,
              referer     => $referer,
              tag         => $profile_tag,
              weight      => $weight,
              size        => $size,
            };
        }
        else {
          $failed_count++;
          push @failed_probes, { category => $cat, url => $url, referer => $referer, response => $response->status_line };
          print "  -> Failed to access $url: " . $response->status_line . "\n";
        }
      }
    }
  }

  print "\n=== Probe Summary ===\n";
  print "Successful probes: $success_count\n";
  print "Failed probes: $failed_count\n";

  if (@failed_probes) {
    print "\n=== Failed Probes ===\n";
    foreach my $failed (@failed_probes) {
      print "Category: $failed->{category}, URL: $failed->{url}, Response: $failed->{response}\n";
    }
  }

  store_sp_URLs($result);
}

# 生成URL配置文件
sub generate_url_cfg {
  my $conditions = shift;
  my $output_file = shift;

  my $dbh = db_open(CDN_DB_FILE);
  my $sql = qq{
    SELECT 
      SP_URLs.category,
      SP_URLs.domain, 
      SP_URLs.url,
      SP_URLs.referer,
      SP_URLs.tag,
      SP_URLs.weight,
      SP_URLs.size
    FROM SP_URLs
    WHERE 1=1
  };

  my @where_clauses;
  my @bind_values;

  # 根据条件从数据库中查询URL
  foreach my $condition_type ( "include", "exclude" ) {
    foreach my $field ( qw(domain size) ) {
      if ( exists $conditions->{$condition_type}->{$field}
        && defined $conditions->{$condition_type}->{$field} )
      {
        my $operator = $condition_type eq "include" ? "=" : "<>";
        my $table = "SP_URLs";
        my $column = $field;

        if ( ref $conditions->{$condition_type}->{$field} eq 'ARRAY' ) {
          my @field_conditions = map { "$table.$column $operator ?" }
            @{ $conditions->{$condition_type}->{$field} };
          push @where_clauses,
            "("
            . join(
            $condition_type eq "include" ? " OR " : " AND ",
            @field_conditions
            ) . ")";
          push @bind_values, @{ $conditions->{$condition_type}->{$field} };
        }
        else {
          push @where_clauses, "$table.$column $operator ?";
          push @bind_values,   $conditions->{$condition_type}->{$field};
        }
      }
    }
  }

  if (@where_clauses) {
    $sql .= " AND " . join( " AND ", @where_clauses );
  }

  $sql .= " ORDER BY `domain` ASC";

  my $sth = $dbh->prepare($sql);
  $sth->execute(@bind_values);
  my $result = $sth->fetchall_arrayref();

  db_close($dbh);

  my $url_cfg = [];
  my $browser_profiles = load_json_file(SP_PROFILES_FILE);

  # 将查询结果转换为URL配置文件
  my $counter = 0;
  for my $row (@$result) {
    my ( $category, $domain, $url, $referer, $tag, $weight, $size ) = @$row;
    # 根据 $weight 数值来决定在 URL 配置文件中出现的次数
    for my $i ( 1 .. $weight ) {
      my $url_entry = {
        category => $category,
        domain   => $domain,
        url      => $url,
        tag      => $tag,
        size     => $size,
      };
      $url_entry->{referer} = $referer if $referer;
      
      push @$url_cfg, $url_entry;
    }
    $counter++;
  }

  print "    > $counter records loaded\n";

  save_json_file( $url_cfg, $output_file );
}

sub format_file_size {
  my $size = shift;
  if ( $size < 1024 ) {
    return $size . ' B';
  }
  elsif ( $size < 1024**2 ) {
    return sprintf( "%.2f KB", $size / 1024 );
  }
  elsif ( $size < 1024**3 ) {
    return sprintf( "%.2f MB", $size / 1024**2 );
  }
  elsif ( $size < 1024**4 ) {
    return sprintf( "%.2f GB", $size / 1024**3 );
  }
  elsif ( $size < 1024**5 ) {
    return sprintf( "%.2f TB", $size / 1024**4 );
  }
  else {
    return sprintf( "%.2f PB", $size / 1024**5 );
  }
}

1;
