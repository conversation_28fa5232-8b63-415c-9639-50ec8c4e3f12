package SP::IPType;

use strict;
use warnings;

use Text::CSV;
use File::Basename;
use DBI;
use DBD::SQLite;

use POSIX qw(strftime);

use SP::Common;
use SP::JSON;
use SP::NetAddr;
use SP::GeoIP;
use SP::SQLite;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  get_service_type
  gen_service_type_cfg
  refresh_service_types_cfg
  update_ip_speed
);

# 根据输入的 IP/prefix, 查找并返回 service type
sub get_service_type {
  my $servicetype_mapping = shift;
  my $ipaddr              = shift;

  my $ip = NetAddr::IP->new($ipaddr);

  unless ( defined $ip ) {
    print "[SvcType]: Invalid address: '", $ipaddr, "'\n";
    return "N/A";
  }

  my $service_type = $servicetype_mapping->{$ip->canon()};
  return defined $service_type ? $service_type : "N/A";
}

# 根据现有的 IP -> service_type 映射, 创建 IP type 映射库
#
# 说明：
#    - 参数1: CSV 文件的保存目录
# 返回:
#    - (无)
# 依赖文件:
#    - 输入参数 1 所指代的原始 CSV 映射文件
#    - GeoIP Mapping 文件
# 输出文件:
#    - SERVICE_TYPE_CONF 配置文件
# 调用示例：
#    gen_service_type_cfg "./report/iptypes/daily_ip_service_types.csv"
sub gen_service_type_cfg {
  # 打开 CSV 文件
  my ($file) = @_;

  my $service_type_mappings;
  my $ip_blocks;

  my $dbh = db_open( CDN_DB_FILE );

  # 加载 GeoIP Mapping
  my $geoip_mapping = load_sp_geoip($dbh);

  # 加载 CDN filter list
  my $cdn_filter = load_json_file(CDN_FILTER_FILE);

  for my $filename ( sort glob $file ) {
    print "==> Reading file: $filename ...\n";

    # 提取文件名部分（去除路径）
    my $basename = basename($filename);

    # 解析出文件名中的日期
    my $date;
    if ($basename =~ /^(\d{8})_/) {
        $date = $1;
    }

    open my $fh, '<:encoding(UTF-8)', $filename or die "Could not open '$filename' $!\n";

    # 创建 CSV 解析器
    my $csv = Text::CSV->new( { binary => 1, auto_diag => 1 } );

    # 从文件读取 CSV 数据
    while ( my $row = $csv->getline($fh) ) {
      my $ip_addr            = $row->[0];
      my $service_type       = $row->[1];
      my $avg_speed          = $row->[2];
      my $count              = $row->[3];
      my $updated_at         = $row->[4];

      $service_type eq "#N/A" and $service_type = "N/A";
      $service_type eq "" and $service_type = "BLANK";
      $avg_speed //= 0;
      $count //= 0;
      $avg_speed eq "" and $avg_speed = 0;
      $count eq "" and $count = 0;
      
      # 如果更新时间未定义，则根据文件名中的日期或当前日期设置更新时间
      unless ( defined $updated_at ) {
        if ( defined $date ) {
          # 如果文件名中包含日期，则使用该日期作为更新时间
          $updated_at = sprintf("%04d-%02d-%02d %02d:%02d:%02d", substr($date, 0, 4), substr($date, 4, 2), substr($date, 6, 2), 0, 0, 0);
        }
        else {
          # 如果文件名中不包含日期，则使用当前日期作为更新时间
          $updated_at = strftime("%Y-%m-%d %H:%M:%S", localtime);
        }
      }

      my $ip = NetAddr::IP->new($ip_addr);

      next unless defined $ip;

      my $netaddr;
      if ( $ip->version == 4 ) {
        $netaddr = get_network_addr( $ip->addr() . "/24" );
      }
      elsif ( $ip->version == 6 ) {
        $netaddr = get_network_addr( $ip->addr() . "/64" );
      }

      # 检查网址是否在指定的 include 列表中, 如果不在, 则跳过
      # 例如: 只检查 ISP 为 移动 的 IP 地址
      my $geoip        = get_geoip_info( $geoip_mapping, $netaddr );
      my $match = 0;
      for my $condition ( @{ $cdn_filter->{"include"}->{"geoip"}->{"isp"} } ) {
        if ( defined $geoip->{"isp"} and $geoip->{"isp"} eq $condition ) {
          $match = 1;
          last;
        }
      }

      if ( $match != 1 ) {
        my $location = get_geoip_location($geoip_mapping, $netaddr);
        print "  -> Ignore $ip: $location\n";
        next;
      }

      # 检查是否已经处理过该网段
      if ( exists $ip_blocks->{$netaddr} ) {
        # 如果已处理过，只更新当前IP的信息
        $service_type_mappings->{$ip->canon()}->{"serviceType"} = $service_type;
        $service_type_mappings->{$ip->canon()}->{"avgSpeed"} = $avg_speed;
        $service_type_mappings->{$ip->canon()}->{"count"} = $count;
        $service_type_mappings->{$ip->canon()}->{"updatedAt"} = $updated_at;
      }
      else {
        # 如果 service_type 为 N/A 或 BLANK, 则跳过
        if ( $service_type eq "N/A" or $service_type eq "BLANK" ) {
          next;
        }

        # 根据IP版本创建网段对象
        my $netip;
        if ( $ip->version == 4) {
          $netip = NetAddr::IP->new( $ip->addr() . "/24");  # IPv4使用/24子网
        }
        elsif ( $ip->version == 6) {
          $netip = NetAddr::IP->new( $ip->addr() . "/120");  # IPv6使用/120子网
        }

        # 遍历网段中的所有IP，并设置相同的服务类型
        my $p;
        for ( $p = $netip->first() ; $p <= $netip->last() ; $p++ ) {
          $service_type_mappings->{$p->canon()}->{"serviceType"} = $service_type;
          $service_type_mappings->{$p->canon()}->{"avgSpeed"} = 0;
          $service_type_mappings->{$p->canon()}->{"count"} = 0;
          $service_type_mappings->{$p->canon()}->{"updatedAt"} = $updated_at;
        }

        # 标记该网段已处理
        $ip_blocks->{$netaddr} = 1;
      }
    }

    # 关闭文件句柄
    close $fh;
  }

  # # # 按照 KEY (IP) 顺序排序
  # tie my %h, 'Hash::Ordered';

  # # 创建一个 Hash::Ordered 对象
  # my $ordered_hash = tied %h;

  # # 将排序后的哈希数组插入到 Hash::Ordered 对象中
  # foreach my $ip ( sort {ip_to_packed($a) cmp ip_to_packed($b)} keys %{$service_type_mappings} ) {
  #   $ordered_hash->push( $ip, $service_type_mappings->{$ip} );
  # }

  # save_json_file( \%h, SERVICE_TYPE_CONF );

  store_sp_service_type( $service_type_mappings, $dbh );

  db_close($dbh);
}

# 更新 IP 地址的实际速度
# 参数:
#   $csv_file - CSV 文件路径，包含 IP 地址和实际速度数据
sub update_ip_speed {
  my $csv_file = shift;

  open my $fh, '<:encoding(UTF-8)', $csv_file or die "Could not open '$csv_file' $!\n";

  # 创建 CSV 解析器
  my $csv = Text::CSV->new( { binary => 1, auto_diag => 1 } );

  # 打开数据库
  my $dbh = db_open( CDN_DB_FILE );

  my $sth = $dbh->prepare("UPDATE SP_ServiceType SET act_speed = ? WHERE ip = ?");

  # 从文件读取 CSV 数据
  my $count = 0;
  while ( my $row = $csv->getline($fh) ) {
    my $ip_addr            = $row->[0];
    my $act_speed          = $row->[1];

    my $ip = NetAddr::IP->new($ip_addr);
    # Round act_speed to 3 decimal places
    $act_speed = sprintf("%.3f", $act_speed);

    next unless defined $ip;

    print "==> Updating IP: ", $ip->canon(), ", speed: ", $act_speed, "\n";

    $sth->execute(
      $act_speed,
      $ip->canon()
    ) or die $sth->errstr;

    $count++;
  }

  print "==> Successfully updated $count IP records\n";

  # 提交事务并关闭连接
  $dbh->commit;
  db_close($dbh);
}

1;
