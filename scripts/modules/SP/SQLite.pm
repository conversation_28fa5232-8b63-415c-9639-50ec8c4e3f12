package SP::SQLite;

use strict;
use warnings;

use utf8;
use open qw(:std :encoding(UTF-8));
binmode(STDOUT, ":utf8");

use DBI;
use DBD::SQLite;

use FindBin;

use SP::Common;
use SP::JSON;
use SP::Domain;
use SP::GeoIP;
use SP::NetAddr;

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  db_open
  db_close
  db_backup
  dump_to_json
  load_cdn_domains
  load_cdn_providers
  load_cdn_mappings
  load_cdn_networks
  load_dns_statistics
  load_sp_dns
  load_sp_geoip
  load_sp_service_type
  load_sp_service_type_brief
  restore_from_json
  store_cdn_providers
  store_cdn_mappings
  store_cdn_networks
  store_sp_dns
  store_sp_geoip
  store_sp_service_type
  store_sp_URLs
);

sub db_open {
  my ( $db_file ) = @_;

  # 连接到 SQLite 数据库
  my $dbh = DBI->connect(
    "dbi:SQLite:dbname=$db_file",
    "", "",
    {
      RaiseError => 1,      # 如果数据库操作失败, 则自动抛出异常
      AutoCommit => 0,      # 每个数据库操作不会立即生效, 需要手动提交
      sqlite_unicode => 1,  # 添加此行以启用 UTF-8 编码
    }
  ) or die $DBI::errstr;

  return $dbh;
}

sub db_close {
  my ( $dbh ) = @_;
  # 关闭连接
  $dbh->disconnect;
}

sub db_attach {
  my ( $dbh, $db_file, $db_name ) = @_;
  $dbh->do("ATTACH DATABASE '$db_file' AS $db_name");
}

sub db_detach {
  my ( $dbh, $db_name ) = @_;
  $dbh->do("DETACH DATABASE $db_name");
}

sub db_backup {
  my ($dbh) = @_;

  print "==> Backing up database ... \n";
  
  # Get current timestamp
  my ($sec, $min, $hour, $mday, $mon, $year) = localtime(time);
  my $timestamp = sprintf("%04d%02d%02d_%02d%02d%02d", $year+1900, $mon+1, $mday, $hour, $min, $sec);
  
  # Construct backup filename
  my $backup_file = CDN_DB_ARCHIVE_DIR . "sp_cdn_$timestamp.db";
  
  # Ensure the backup directory exists
  unless (-d CDN_DB_ARCHIVE_DIR) {
    mkdir CDN_DB_ARCHIVE_DIR or die "Failed to create backup directory: $!";
  }
  
  # Perform the backup
  print "  -> Performing the backup...\n";
  $dbh->sqlite_backup_to_file($backup_file);
  
  print "  -> Database backed up to: \n\t", $backup_file, "\n";
}

sub restore_from_json {
  my $data;

  print "==> Restoring CDN database from JSON files ... \n";

  print "  -> Storing CDN catalogue data ... \n";
  $data = load_json_file(CDN_PROVIDERS_FILE);
  store_cdn_providers( $data );

  print "  -> Storing CDN mapping data ... \n";
  $data = load_json_file(CDN_MAPPINGS_FILE);
  store_cdn_mappings( $data );

  print "  -> Storing CDN networks data ... \n";
  $data = load_json_file(CDN_NETWORKS_FILE);
  store_cdn_networks( $data );

  print "  -> Storing DNS data ... \n";
  $data = load_json_file(SP_DNS_CONF);
  store_sp_dns( $data );
  
  print "  -> Storing GeoIP data ... \n";
  $data = load_json_file(SP_GEOIP_FILE);
  store_sp_geoip( $data );

  print "  -> Storing IP service type data ... \n";
  $data = load_json_file(SERVICE_TYPE_CONF);
  store_sp_service_type( $data );

  print "  -> Data successfully stored in SQLite database: \n\t", CDN_DB_FILE, "\n";
}

sub dump_to_json {
  my $data;

  print "==> Dumping CDN database to JSON files ... \n";
  
  print "  -> Dumping CDN providers data ... \n";
  $data = load_cdn_providers();
  save_json_file( $data, CDN_PROVIDERS_FILE );

  print "  -> Dumping CDN mappings data ... \n";
  $data = load_cdn_mappings();
  save_json_file( $data, CDN_MAPPINGS_FILE );

  print "  -> Dumping CDN networks data ... \n";
  $data = load_cdn_networks();
  save_json_file( $data, CDN_NETWORKS_FILE );

  print "  -> Dumping IP service type data ... \n";
  $data = load_sp_service_type();
  save_json_file( $data, SERVICE_TYPE_CONF );

  print "  -> Dumping GeoIP data ... \n";
  $data = load_sp_geoip();
  save_json_file( $data, SP_GEOIP_FILE );

  print "  -> Dumping DNS data ... \n";
  $data = load_sp_dns();
  save_json_file( $data, SP_DNS_CONF );

  print "==> Data successfully dumped to JSON files: \n\t", 
    CDN_PROVIDERS_FILE, "\n\t", 
    CDN_MAPPINGS_FILE, "\n\t", 
    CDN_NETWORKS_FILE, "\n\t", 
    SERVICE_TYPE_CONF, "\n\t",
    SP_GEOIP_FILE, "\n\t", 
    SP_DNS_CONF, "\n";
}

sub store_cdn_providers {
  my ( $data ) = @_;

  my $dbh = db_open(CDN_DB_FILE);

  # 创建 providers 表
  $dbh->do(
    q{
      DROP TABLE IF EXISTS CDN_Providers
    }
  );
  $dbh->do(
    q{
      CREATE TABLE IF NOT EXISTS CDN_Providers (
          id TEXT PRIMARY KEY,
          provider TEXT
      )
    }
  );

  # 创建 domains 表
  $dbh->do(
    q{
      DROP TABLE IF EXISTS CDN_Domains
    }
  );
  $dbh->do(
    q{
      CREATE TABLE IF NOT EXISTS CDN_Domains (
          domain TEXT PRIMARY KEY,
          provider_id TEXT,
          FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id)
      )
    }
  );

  # 准备插入语句
  my $insert_provider = $dbh->prepare(
    "INSERT OR IGNORE INTO CDN_Providers (id, provider) VALUES (?, ?)");
  my $insert_domain = $dbh->prepare(
    "INSERT OR IGNORE INTO CDN_Domains (domain, provider_id) VALUES (?, ?)");

  # 插入数据
  foreach my $id ( sort keys %$data ) {
    my $info = $data->{$id};

    # 插入 provider
    $insert_provider->execute( $id, $info->{provider} );

    # 插入 domains
    foreach my $domain ( @{ $info->{domains} } ) {
      $insert_domain->execute( $domain, $id );
    }
  }

  # 提交更改并关闭连接
  $dbh->commit;
  db_close($dbh);
}

sub store_cdn_mappings {
  my ( $data ) = @_;

  my $dbh = db_open(CDN_DB_FILE);

  # 创建 CDN_Mappings 表
  $dbh->do(
    q{
      CREATE TABLE IF NOT EXISTS CDN_Mappings (
          origin_domain TEXT,
          cdn_domain TEXT,
          provider_id TEXT,
          PRIMARY KEY (origin_domain, cdn_domain),
          FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id)
      )
    }
  );

  # Load suffix list
  my $suffixes = load_suffix_list();

  # 插入 CDN mapping 数据
  my $insert_mapping = $dbh->prepare(
    "INSERT OR REPLACE INTO CDN_Mappings (origin_domain, cdn_domain, provider_id) VALUES (?, ?, ?)"
  );

  # 删除旧的 origin_domain 对应的 CDN mapping 数据
  my $delete_mapping = $dbh->prepare(
    "DELETE FROM CDN_Mappings WHERE origin_domain = ?"
  );

  foreach my $origin_domain ( keys %$data ) {
    $delete_mapping->execute( lc($origin_domain) );

    foreach my $cdn_domain ( @{ $data->{$origin_domain} } ) {
      my $second_level_domain = get_second_level_domain( $cdn_domain, $suffixes );
      my $sth = $dbh->prepare("SELECT provider_id FROM CDN_Domains WHERE domain = ?");
      $sth->execute($second_level_domain);
      my ($provider_id) = $sth->fetchrow_array();
      $insert_mapping->execute( lc($origin_domain), lc($cdn_domain), $provider_id );
    }
  }

  # 提交更改并关闭连接
  $dbh->commit;
  db_close($dbh);
}

sub store_cdn_networks {
  my ( $data ) = @_;

  my $dbh = db_open(CDN_DB_FILE);

  # 创建 CDN_Networks 表，添加 updated_at 字段
  $dbh->do(
    q{
      CREATE TABLE IF NOT EXISTS CDN_Networks (
          cdn_domain TEXT,
          ip_version TEXT,
          network TEXT,
          ip_prefix TEXT,
          provider_id TEXT,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (cdn_domain, network),
          FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id),
          FOREIGN KEY (ip_prefix) REFERENCES SP_GeoIP (ip_prefix)
      )
    }
  );

  # Load suffix list
  my $suffixes = load_suffix_list();

  # 插入或更新 CDN network 数据，使用 REPLACE 以更新现有记录或插入新记录
  my $upsert_network = $dbh->prepare(
    "REPLACE INTO CDN_Networks (cdn_domain, ip_version, network, ip_prefix, provider_id, updated_at) VALUES (?, ?, ?, ?, ?, datetime('now', 'localtime'))"
  );

  # 删除域名对应的旧的 CDN network 数据（可能会删除有用数据，暂时禁用）
  # my $delete_network = $dbh->prepare(
  #   "DELETE FROM CDN_Networks WHERE cdn_domain = ?"
  # );

  foreach my $cdn_domain ( keys %$data ) {
    # 删除域名对应的旧的 CDN network 数据（可能会删除有用数据，暂时禁用）
    # $delete_network->execute( lc($cdn_domain) );

    my $second_level_domain = get_second_level_domain( $cdn_domain, $suffixes );
    my $sth = $dbh->prepare("SELECT provider_id FROM CDN_Domains WHERE domain = ?");
    $sth->execute($second_level_domain);
    my ($provider_id) = $sth->fetchrow_array();

    foreach my $ip_version ( keys %{ $data->{$cdn_domain}->{"networks"} } ) {
      foreach my $network  ( @{ $data->{$cdn_domain}->{"networks"}->{$ip_version} } ) {
        # 插入或更新 CDN network 数据
        $upsert_network->execute( lc($cdn_domain), $ip_version, $network, get_ip_prefix($network), $provider_id );
      }
    }
  }

  # 提交更改并关闭连接
  $dbh->commit;
  db_close($dbh);
}

sub store_sp_geoip {
  my ( $data ) = @_;

  my $dbh = db_open(CDN_DB_FILE);

  # 创建 CDN_GeoIP 表
  $dbh->do(
    q{
      CREATE TABLE IF NOT EXISTS SP_GeoIP (
          ip_prefix TEXT,
          country TEXT,
          province TEXT,
          city TEXT,
          isp TEXT,
          PRIMARY KEY (ip_prefix)
      )
    }
  );

  my $insert_geoip = $dbh->prepare(
    "INSERT OR REPLACE INTO SP_GeoIP (ip_prefix, country, province, city, isp) VALUES (?, ?, ?, ?, ?)"
  );

  # 插入数据
  foreach my $ip_prefix ( keys %$data ) {
    my $info = $data->{$ip_prefix};
    my ($country, $province, $city, $isp) = (
      $info->{country}, 
      $info->{province}, 
      $info->{city}, 
      $info->{isp}
    );

    $insert_geoip->execute( $ip_prefix, $country, $province, $city, $isp );
  }

  # 提交更改并关闭连接
  $dbh->commit;
  db_close($dbh);
}

sub store_sp_service_type {
  my ( $data ) = @_;

  my $dbh = db_open(CDN_DB_FILE);

  # 删除现有的 SP_ServiceType 表
  # $dbh->do("DROP TABLE IF EXISTS SP_ServiceType");

  # 创建表（如果不存在）
  $dbh->do("CREATE TABLE IF NOT EXISTS SP_ServiceType (
      ip TEXT,
      ip_prefix TEXT,
      service_type TEXT,
      avg_speed REAL,
      act_speed REAL,
      count INTEGER,
      updated_at DATETIME DEFAULT '1971-01-01 00:00:00',
      PRIMARY KEY (ip),
      FOREIGN KEY (ip_prefix) REFERENCES SP_GeoIP (ip_prefix)
  )") or die $dbh->errstr;

  # 创建索引
  $dbh->do("CREATE INDEX IF NOT EXISTS idx_sp_service_type_ip_prefix ON SP_ServiceType(ip_prefix)") or die $dbh->errstr;

  # 准备插入语句
  my $sth = $dbh->prepare("INSERT OR REPLACE INTO SP_ServiceType (ip, ip_prefix, service_type, avg_speed, count, updated_at) VALUES (?, ?, ?, ?, ?, ?)");

  # 插入数据
  foreach my $ip (keys %$data) {
    my $data = $data->{$ip};
    $sth->execute(
      $ip,
      get_ip_prefix($ip),
      $data->{serviceType},
      $data->{avgSpeed},
      $data->{count},
      $data->{updatedAt}
    ) or die $sth->errstr;
  }

  # 提交事务并关闭连接
  $dbh->commit;
  db_close($dbh);
}

sub store_sp_dns {
  my ( $data ) = @_;

  my $dbh = db_open(CDN_DB_FILE);

  # 创建表（如果不存在）
  $dbh->do("CREATE TABLE IF NOT EXISTS SP_DNS (
      domain TEXT,
      cdn_domain TEXT,
      ip TEXT,
      ip_prefix TEXT,
      record_type TEXT,
      provider_id TEXT,
      updated_at DATETIME DEFAULT (datetime('now', 'localtime')),
      PRIMARY KEY (domain, ip),
      FOREIGN KEY (ip_prefix) REFERENCES SP_GeoIP (ip_prefix),
      FOREIGN KEY (ip) REFERENCES SP_ServiceType (ip),
      FOREIGN KEY (provider_id) REFERENCES CDN_Providers (id)
  )") or die $dbh->errstr;

  # 准备插入语句
  my $sth = $dbh->prepare("REPLACE INTO SP_DNS (domain, cdn_domain, ip, ip_prefix, record_type, provider_id, updated_at) VALUES (?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))");

  for my $domain (keys %$data) {
    for my $cdn_domain (keys %{ $data->{$domain} }) {
      for my $rr (@{ $data->{$domain}->{$cdn_domain} }) {
        my ($ip, $ip_version, $provider_id) = ($rr->{"data"}, $rr->{"type"}, $rr->{"providerID"});
        $sth->execute($domain, $cdn_domain, $ip, get_ip_prefix($ip), $rr->{"type"}, $provider_id);
      }
    }
  }

  # 提交更改并关闭连接
  $dbh->commit;
  db_close($dbh);
}

sub store_sp_URLs {
  my ( $data ) = @_;

  my $dbh = db_open(CDN_DB_FILE);

  # 创建表（如果不存在）
  $dbh->do("CREATE TABLE IF NOT EXISTS SP_URLs (
      category TEXT,
      domain TEXT,
      url TEXT,
      referer TEXT,
      tag TEXT,
      weight INTEGER,
      size INTEGER,
      updated_at DATETIME DEFAULT (datetime('now', 'localtime')),
      PRIMARY KEY (category, domain, url)
  )") or die $dbh->errstr;

  # 准备插入语句
  my $sth = $dbh->prepare("INSERT OR REPLACE INTO SP_URLs (category, domain, url, referer, tag, weight, size, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))");

  for my $entry (@$data) {
    $sth->execute($entry->{category}, $entry->{domain}, $entry->{url}, $entry->{referer}, $entry->{tag}, $entry->{weight}, $entry->{size});
  }

  # 删除同一 domain 下 update_at 在一天之前的所有记录
  $sth = $dbh->prepare("DELETE FROM SP_URLs WHERE domain = ? AND updated_at < date('now', '-1 day')");
  
  for my $entry (@$data) {
    $sth->execute($entry->{domain});
  }

  # 提交更改并关闭连接
  $dbh->commit; 
  db_close($dbh);
}

sub load_cdn_providers {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare(qq{
    SELECT 
      p.id AS id, 
      p.provider AS provider, 
      d.domain AS domain 
    FROM 
      CDN_Providers p
    JOIN 
      CDN_Domains d ON p.id = d.provider_id;
  });
  $sth->execute();
  my $data = {};
  while (my ($id, $provider, $domain) = $sth->fetchrow_array()) {
    push @{$data->{$id}->{"domains"}}, $domain;
    $data->{$id}->{"provider"} = $provider;
  }

  # 提交更改并关闭连接
  db_close($dbh);

  return $data;
}

sub load_cdn_mappings {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare("SELECT origin_domain, cdn_domain FROM CDN_Mappings");
  $sth->execute();
  my $data = {};
  while (my ($origin_domain, $cdn_domain) = $sth->fetchrow_array()) {
    push @{ $data->{$origin_domain} }, $cdn_domain;
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}

sub load_cdn_domains {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare(qq{
    SELECT 
      d.domain AS domain, 
      d.provider_id AS provider_id, 
      p.provider AS provider 
    FROM 
      CDN_Domains d
    JOIN 
      CDN_Providers p ON d.provider_id = p.id;
  });
  $sth->execute();
  my $data = {};
  while (my ($domain, $provider_id, $provider) = $sth->fetchrow_array()) {
    $data->{$domain}->{"provider_id"} = $provider_id;
    $data->{$domain}->{"provider"} = $provider;
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}

sub load_cdn_networks {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare(qq{
    SELECT 
      n.cdn_domain, 
      n.ip_version, 
      n.network, 
      n.ip_prefix, 
      n.provider_id,
      p.provider
    FROM 
      CDN_Networks n
    JOIN
      CDN_Providers p ON n.provider_id = p.id
  });
  $sth->execute();
  my $data = {};
  while (my ($cdn_domain, $ip_version, $network, $ip_prefix, $provider_id, $provider) = $sth->fetchrow_array()) {
    push @{ $data->{$cdn_domain}->{"networks"}->{$ip_version} }, $network;
    $data->{$cdn_domain}->{"providerID"} = $provider_id;
    $data->{$cdn_domain}->{"provider"} = $provider;
    $data->{$cdn_domain}->{"refers"} = [];
    my $lookup_origin_domain = $dbh->prepare("SELECT origin_domain FROM CDN_Mappings WHERE cdn_domain = ?");
    $lookup_origin_domain->execute($cdn_domain);
    while (my ($origin_domain) = $lookup_origin_domain->fetchrow_array()) {
      push @{ $data->{$cdn_domain}->{"refers"} }, $origin_domain;
    }
  }
  # 去重
  for my $cdn_domain (keys %$data) {
    $data->{$cdn_domain}->{"refers"} = [sort(uniq(@{$data->{$cdn_domain}->{"refers"}}))];
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}

sub load_dns_statistics {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

    my $sth = $dbh->prepare(qq{
      SELECT 
        domain,
        cdn_domain,
        COUNT(DISTINCT ip) as ip_count,
        ip_prefix,
        provider_id
      FROM SP_DNS
      WHERE updated_at >= date((SELECT MAX(updated_at) FROM SP_DNS))
      GROUP BY ip_prefix
      ORDER BY `updated_at` DESC;
  });
  $sth->execute();
  my $data = {};
  while (my ($domain, $cdn_domain, $ip_count, $ip_prefix, $provider_id) = $sth->fetchrow_array()) {
    $data->{$ip_prefix} = {
      "domain" => $domain, "cdn_domain" => $cdn_domain, "ip_count" => $ip_count, "provider_id" => $provider_id
    };
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}

sub load_sp_geoip {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare("SELECT ip_prefix, country, province, city, isp FROM SP_GeoIP");
  $sth->execute();
  my $data = {};
  while (my ($ip_prefix, $country, $province, $city, $isp) = $sth->fetchrow_array()) {
    $data->{$ip_prefix} = {
      "country" => $country, "province" => $province, "city" => $city, "isp" => $isp
    };
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}

sub load_sp_service_type {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare("SELECT ip, service_type, avg_speed, count FROM SP_ServiceType");
  $sth->execute();
  my $data = {};
  while (my ($ip, $service_type, $avg_speed, $count) = $sth->fetchrow_array()) {
    $data->{$ip} = {
      "serviceType" => $service_type,
      "avgSpeed" => $avg_speed,
      "count" => $count
    };
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}

sub load_sp_service_type_brief {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare("SELECT ip, service_type FROM SP_ServiceType");
  $sth->execute();
  my $data = {};
  while (my ($ip, $service_type) = $sth->fetchrow_array()) {
    $data->{$ip} = $service_type;
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}

sub load_sp_dns {
  # 打开数据库连接
  my $dbh = db_open(CDN_DB_FILE);

  my $sth = $dbh->prepare("SELECT domain, cdn_domain, ip, ip_prefix, record_type, provider_id FROM SP_DNS");
  $sth->execute();
  my $data = {};
  while (my ($domain, $cdn_domain, $ip, $ip_prefix, $record_type, $provider_id) = $sth->fetchrow_array()) {
    my $rr = {
      "data" => $ip,
      "type" => $record_type,
      "providerID" => $provider_id
    };

    push @{ $data->{$domain}->{$cdn_domain} }, $rr;
  }

  # 关闭连接
  db_close($dbh);

  return $data;
}


1;