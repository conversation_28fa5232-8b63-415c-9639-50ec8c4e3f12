package SP::YAML;

use strict;
use warnings;

use utf8;
use open qw(:std :encoding(UTF-8));
binmode(STDOUT, ":utf8");

use YAML::XS;
use Encode qw(encode_utf8);

# 导出符号
use Exporter 'import';
our @EXPORT = qw(
  load_yaml_file
  save_yaml_file
);

# 模块版本
our $VERSION = '1.00';

# 读取 YAML 文件, 返回 HASH REF
sub load_yaml_file {
  my $yaml_file = shift;

  open my $fh, '<:encoding(UTF-8)', $yaml_file or die "Can't open $yaml_file: $!\n";

  # 读取文件到一个字符串变量
  my $yaml_text = do {
    local $/;
    <$fh>;
  };

  # 解析 YAML 字符串
  my $data = Load( encode_utf8( $yaml_text ) );

  # 关闭文件句柄
  close $fh;

  return $data;
}

# 将 YAML REF 写入 YAML 文件
sub save_yaml_file {
  my $data      = shift;
  my $yaml_file = shift;
  # 打开文件用于写入，如果失败则报错退出
  open my $fh, ">", $yaml_file or die "Can't write to file $yaml_file: $!\n";

  # 设置文件句柄为 UTF-8 编码
  binmode($fh, ":utf8");

  # 将数据转换为 YAML 格式并写入文件
  print $fh Dump($data), "\n";

  # 关闭文件句柄
  close $fh;
}

1;