#!/usr/bin/env perl
BEGIN { $ENV{PERL_LWP_SSL_VERIFY_HOSTNAME} = 0 }
use strict;
use warnings;
use Data::Dumper;
use JSON::XS;
use Getopt::Long;
use Pod::Usage;
use File::Basename;
use File::Temp qw(tempfile tempdir);
use File::Spec;
use Spreadsheet::ParseXLSX;
use Net::IP;
use NetAddr::IP qw(:lower);
use Hash::Ordered;
use Encode;

use utf8;
use open qw( :std :encoding(utf8) );
binmode(STDOUT, ":utf8");
binmode(STDERR, ":utf8");

use FindBin;
use lib "$FindBin::Bin/modules";

use SP::Common;
use SP::CDN;
use SP::DNS;
use SP::Domain;
use SP::GeoIP;
use SP::IPType;
use SP::NetAddr;
use SP::JSON;
use SP::YAML;
use SP::SQLite;
use SP::URL;
use SP::Task;

# 读取从 boce.aliyun.com 导出的 CSV 文件, 并提取域名、IP 等信息
#
# 说明：
#    - 参数1: CSV 文件的保存路径
# 依赖:
#    - 数据库 CDN_Providers 表, 记录下 CDN 厂商及所拥有域名信息
#    - 数据库 CDN_Domains 表, 记录下 CDN 域名信息
#    - 数据库 CDN_Networks 表, 记录下 CDN 域名及 IP 地址段信息
#    - public_suffix_list.dat, 保存了 gTLD 和 ccTLD 域名，用于计算二级域名和三级域名
#    - cdn_manifest.json, 保存了 预定义的资源网站信息, 包括类别、域名、URL 等
# 输出:
#    - 数据库 CDN_Networks 表, 记录下 CDN 域名及 IP 地址段信息
#    - 数据库 CDN_Mappings 表, 记录下 源站域名和 CDN 域名之间的映射关系
# 调用示例：
#    extract "~/Downloads/swcdn.apple.com-dns-result.csv"
sub extract {

  # 打开 CSV 文件
  my ($file) = @_;

  my $path;

  # 记录解析结果
  my %data;

  # 加载域名 -> CDN 域名映射
  my $cdn_mappings = load_cdn_mappings();

  # CNAME 映射, 用于临时存储 CNAME 映射关系（用于处理同一资源网站的多个域名）
  my $cname_mapping = {};

  # 加载预定义的资源网站信息
  my $cdn_manifest = load_json_file(CDN_MANIFEST_FILE);

  print "==> Loading CDN Manifest ...\n";

  # 遍历所有预定义的资源网站
  foreach my $category ( keys %$cdn_manifest ) {
    print "  -> Category: $category \n";

    # 在 manifest 中查找源站域名
    my @domain = @{$cdn_manifest->{$category}->{"domain"}};
    foreach my $domain (@domain) {
      print "    > $domain \n";
      
      # 创建 HASH 数组，将当前域名映射到第一个域名
      $cname_mapping->{$domain} = $domain[0];
    }
  }

  for my $filename ( glob $file ) {
    print "==> Reading file: $filename ...\n";

    # 从文件名读取原资源域名
    my $domain;
    if ( $filename =~ /([A-Za-z0-9\.-]+)-dns-result.*\.xlsx$/ ) {
      $domain = lc($1);
    }
    else {
      $domain = "NA";
    }

    # 根据 CNAME 映射关系, 将当前域名映射到第一个域名
    $domain = $cname_mapping->{$domain} if defined $cname_mapping->{$domain};

    print "  -> $domain \n";

    $path = dirname($filename);

    # 创建解析器对象
    my $parser = Spreadsheet::ParseXLSX->new();

    # 解析 Excel 文件
    my $workbook = $parser->parse($filename);

    # 假设数据在第一个工作表
    my $worksheet = $workbook->worksheet(0);

    # Load suffix list
    my $suffixes = load_suffix_list();

    # 获取工作表的行列范围
    my ($row_min, $row_max) = $worksheet->row_range();
    my ($col_min, $col_max) = $worksheet->col_range();

    # 遍历每一行
    for my $row ($row_min + 1 .. $row_max) {
      my $row_data = $worksheet->get_cell($row, 4);
      my @Resolved_v4;
      my @Resolved_v6;
      my $cdn_domain;
      if ( defined $row_data ) {
        my $cell_value = $row_data->value();

        # 按照 \n 分割多个 IP 地址和域名
        my @entries = split( /\n/, $cell_value );
        foreach my $entry (@entries) {
          # 去除前后空白字符
          $entry =~ s/^\s+|\s+$//g;

          # 删除无效字符
          $entry =~ s/_x000D_//gi;

          my $ip = new Net::IP($entry);

          # 判断是否为合法的 IP 地址
          if ( defined $ip ) {
            if ( $ip->version() == 4 ) {
              push @Resolved_v4, $ip->ip();
            }
            elsif ( $ip->version() == 6 ) {
              push @Resolved_v6, $ip->ip();
            }
          }
          else {
            # 这里使用 三级域名 来存储原始域名和 CDN 域名之间的映射关系
            $cdn_domain = get_third_level_domain( lc($entry), $suffixes );
            # 去除前后空白字符
            $cdn_domain =~ s/^\s+|\s+$//g;
          }
        }

        # 将解析出来的 IP 地址排序去重后, 存入对应的域名下
        if ( scalar @Resolved_v4 > 0 ) {
          @Resolved_v4 =
            sort {ip_to_packed($a) cmp ip_to_packed($b)} uniq( @Resolved_v4,
            @{ $data{$cdn_domain}->{"IPv4"} } );

          if ( $cdn_domain ne "" ) {
            $data{$cdn_domain}->{"IPv4"} = \@Resolved_v4;
            push @{ $cdn_mappings->{$domain} }, $cdn_domain;
          }
        }
        if ( scalar @Resolved_v6 > 0 ) {
          @Resolved_v6 =
            sort {ip_to_packed($a) cmp ip_to_packed($b)} uniq( @Resolved_v6,
            @{ $data{$cdn_domain}->{"IPv6"} } );

          if ( $cdn_domain ne "" ) {
            $data{$cdn_domain}->{"IPv6"} = \@Resolved_v6;
            push @{ $cdn_mappings->{$domain} }, $cdn_domain;
          }
        }
      }
    }

    # 排序, 去重
    my @cdn_domains = sort( uniq( @{ $cdn_mappings->{$domain} } ) );

    for my $cdn_domain (@cdn_domains) {
      print "    > $cdn_domain \n";
    }

    # 去重后的结果重新写回
    $cdn_mappings->{$domain} = \@cdn_domains;
  }

  # 将域名 -> CDN 映射关系写回数据库
  store_cdn_mappings($cdn_mappings);

  # Load suffix list
  my $suffixes = load_suffix_list();

  # Load CDN networks
  my $cdn_networks = load_cdn_networks();

  # 加载 CDN 域名
  my $cdn_domains = load_cdn_domains();

  print "==> CDN Providers:\n";
  printf "%32s  %-8s  %s\n", "DOMAIN", "ID", "PROVIDER";
  printf "%32s  %-8s  %s\n", "-" x 16, "-" x 8,    "-" x 22;

  for my $domain ( sort keys %data ) {
    next if $domain eq "";

    my $second_level_domain = get_second_level_domain( lc($domain), $suffixes );

    my $provider;
    my $provider_id;

    # 这里使用 二级域名 来判别域名所属 CDN 厂商
    $provider_id = $cdn_domains->{$second_level_domain}->{"provider_id"};
    $provider = $cdn_domains->{$second_level_domain}->{"provider"};
    if ( not defined $provider_id ) {
      $provider_id = "N/A";
      $provider = "N/A";
    }

    # 输出解析结果
    printf "%32s  %-8s  %s\n", $domain, $provider_id, $provider;

    my $ipv4_networks;
    my $ipv6_networks;

    if ( defined $cdn_networks->{$domain} ) {
      if ( defined $data{$domain}->{"IPv4"}
        and scalar @{ $data{$domain}->{"IPv4"} } > 0 )
      {
        $ipv4_networks = aggregate_ipv4_blocks(
          (
            @{
              $cdn_networks->{$domain}->{"networks"}->{"IPv4"}
            },
            @{ $data{$domain}->{"IPv4"} }
          )
        );
      }
      else {
        $ipv4_networks =
          $cdn_networks->{$domain}->{"networks"}->{"IPv4"};
      }

      if ( defined $data{$domain}->{"IPv6"}
        and scalar @{ $data{$domain}->{"IPv6"} } > 0 )
      {
        $ipv6_networks = aggregate_ipv6_blocks(
          (
            @{
              $cdn_networks->{$domain}->{"networks"}->{"IPv6"}
            },
            @{ $data{$domain}->{"IPv6"} }
          )
        );
      }
      else {
        $ipv6_networks =
          $cdn_networks->{$domain}->{"networks"}->{"IPv6"};
      }
    }
    else {
      $ipv4_networks = aggregate_ipv4_blocks( @{ $data{$domain}->{"IPv4"} } );
      $ipv6_networks = aggregate_ipv6_blocks( @{ $data{$domain}->{"IPv6"} } );
    }

    $cdn_networks->{$domain}->{"networks"}->{"IPv4"} = $ipv4_networks;
    $cdn_networks->{$domain}->{"networks"}->{"IPv6"} = $ipv6_networks;
  }

  # 将 CDN 域名 -> IP 映射关系写回数据库
  store_cdn_networks($cdn_networks);
}

# 根据预定义的资源网站类别, 生成探测脚本
#
# 说明：
#    - 参数1: 预定义的类别名称
#    - 参数2: 是否 dryrun (仅打印 probe 命令, 并不实际运行)
# 依赖:
#    - cdn_manifest.json, 保存了 预定义的资源网站信息, 包括类别、域名、URL 等
#    - cdn_mapping.json, 保存了 源站域名和 CDN 域名之间的映射关系
#    - cdn_domains.json, 保存了 CDN 域名和 IP 地址段及源站域名 之间的映射关系
#    - cdn_filterlist.json, 保存了 CDN IP 地址段筛选规则
#    - providers 目录下 CDN 域名及 IP 具体信息
#    - configs/sp_iptype.json, 保存了 IP 和 服务类型 service type 之间的映射关系
#    - configs/sp_geoip.json, 保存了 IP 和 地理位置以及所属运营商 之间的映射关系
# 输出:
#    - <STDOUT>: 源站域名、CDN 域名、CDN 提供商、IP 网段、服务类型、地理位置、所属运营商 等报表
#    - <STDOUT>: 待补全 GeoIP 信息的 IP 地址段 (如果有)
#    - <STDOUT>: 运行 httpx 探测所需的命令行参数及筛选探测结果所需运行的命令
# 调用示例：
#    probe "apple_swcdn" {"dryrun"=>1}
sub probe {
  my $category     = shift;
  my $options      = shift;
  my $cdn_manifest = load_json_file(CDN_MANIFEST_FILE);

  unless ( defined $category ) {
    print "Please specify category name for cdn domains.\n";
    return;
  }

  unless ( $category eq 'all' or defined $cdn_manifest->{$category} ) {
    print "Unknown URL category: $category\n";
    return;
  }

  unless ( defined $options ) {
    $options->{"dryrun"} = 0;
  }

  # 加载域名 -> CDN 域名映射
  my $cdn_mappings = load_cdn_mappings();

  # 分类别依次加载 CDN 域名和 IP 数据
  my $provider_data = load_cdn_providers();

  # 加载 Service Type
  my $servicetype_mapping = load_sp_service_type_brief();

  # 加载 GeoIP Mapping
  my $geoip_mapping = load_sp_geoip();

  # 加载 CDN 域名 -> IP 映射
  my $cdn_networks = load_cdn_networks();

  # 加载 CDN filter list
  my $cdn_filter = load_json_file(CDN_FILTER_FILE);

  # 需要丰富 GeoIP 信息的 IP
  my @ip_unknown = ();

  # 需要提供给 httpx 进行探测的 IP
  my @ip_probe = ();

  # 如果 $category 为 'all'，遍历所有类别，否则只处理指定类别
  my @categories = ($category eq 'all') ? keys %$cdn_manifest : ($category);

  foreach my $current_category (@categories) {
    print "-" x 32, "\n";
    print " Category: $current_category \n";
    print "-" x 32, "\n";

    # 在 manifest 中查找源站域名
    my $domain = $cdn_manifest->{$current_category}->{"domain"}->[0];
    print "==> $domain \n";

    # 在 cdn 域名 HASH 数组中查找每个 CDN 域名
    for my $cdn_domain ( @{ $cdn_mappings->{$domain} } ) {
      # 从 cdn_networks 中获取 CDN 提供商信息
      my $provider = $cdn_networks->{$cdn_domain}->{"provider"};
      # 如果提供商信息未定义，则设置为 "N/A"
      $provider = "N/A" unless defined($provider);

      # 打印 CDN 域名和提供商信息
      print "  -> $cdn_domain [$provider] \n";

      # 查找 CDN 每个 IPv4 地址段对应的属性
      if (!defined($options->{"dns_type"}) || $options->{"dns_type"} eq "A") {
        for my $ip_block (
          @{ $cdn_networks->{$cdn_domain}->{"networks"}->{"IPv4"} } )
        {
          my $ip = NetAddr::IP->new($ip_block);
          next unless ( defined $ip );
          my $netaddr      = get_network_addr( $ip->addr() . "/24" );
          my $geoip        = get_geoip_info( $geoip_mapping, $netaddr );
          my $service_type = get_service_type( $servicetype_mapping, $ip->first()->canon() );
          my $result       = check_cdn_filter(
            $cdn_filter,
            {
              "ip_block"    => $ip_block,
              "domain"      => $cdn_domain,
              "serviceType" => $service_type,
              "geoip"       => $geoip,
            }
          );

          if ($result == 1) {
            print "    > ";    # 需要探测
          } elsif ($result == 2) {
            print "    N ";    # 信息不全, 需补全信息
          } elsif ($result == 0) {
            print "    * ";    # 黑名单, 不使用不探测
          } else {
            print "    N ";
          }
          my $location = get_geoip_location($geoip_mapping, $netaddr);
          printf "%-45s\t%-10s\t(%-1d)\t%-s\n", $ip_block, $service_type, $result,
            $location;

          push @ip_unknown, $ip_block if $location eq 'N/A';
          push @ip_probe,   $ip_block if $result == 1;
        }
      }

      # 查找 CDN 每个 IPv6 地址段对应的属性
      if (!defined($options->{"dns_type"}) || $options->{"dns_type"} eq "AAAA") {
        for my $ip_block (
          @{ $cdn_networks->{$cdn_domain}->{"networks"}->{"IPv6"} } )
        {
          my $ip = NetAddr::IP->new($ip_block);
          next unless ( defined $ip );
          my $netaddr      = get_network_addr( $ip->addr . "/64" );
          my $geoip        = get_geoip_info( $geoip_mapping, $netaddr );
          my $service_type = get_service_type( $servicetype_mapping, $ip->first()->canon() );
          my $result       = check_cdn_filter(
            $cdn_filter,
            {
              "ip_block"    => $ip_block,
              "domain"      => $cdn_domain,
              "serviceType" => $service_type,
              "geoip"       => $geoip,
            }
          );

          if ($result == 1) {
            print "    > ";    # 需要探测
          } elsif ($result == 2) {
            print "    N ";    # 信息不全, 需补全信息
          } elsif ($result == 0) {
            print "    * ";    # 黑名单, 不使用不探测
          } else {
            print "    N ";
          }
          my $location = get_geoip_location($geoip_mapping, $netaddr);
          printf "%-45s\t%-10s\t(%-1d)\t%-s\n", $ip_block, $service_type, $result,
            $location;

          push @ip_unknown, $ip_block if $location eq 'N/A';
          if ( $result == 1 ) {
            # httpx 不支持 IPv6 网段探测, 这里需要展开每个具体 IPv6 地址
            my $p;
            for ( $p = $ip->first() ; $p <= $ip->last() ; $p++ ) {
              push @ip_probe, "[" . $p->addr() . "]";
            }
          }
        }
      }
    }

    # 打印 httpx 运行参数
    my $probe_url = $cdn_manifest->{$current_category}->{"probe"};    
    my $referer = $cdn_manifest->{$current_category}->{"referer"};
    my $tag = $cdn_manifest->{$current_category}->{"tag"};
    my $user_agent = UA_CONF_MAP->{$tag}->[ rand @{UA_CONF_MAP->{$tag}} ];
    if ( $probe_url =~ m{^(?:https?://)?([^/]+)/(.*)?$} ) {
      my $host   = $1;
      my $uri    = $2;
      my $scheme = $probe_url =~ m{^(https?://)} ? $1 : 'http://';

      # 创建一个临时文件
      my ( $fh, $filename ) =
        tempfile( 'probe_XXXXX', DIR => "/tmp", SUFFIX => ".txt" );

      print $fh "cd \$(mktemp -p /tmp -d ip_dns_XXXXX)\n\n";
      print $fh "cat > ip_cidr.txt <<EOF\n";
      map { print $fh $_, "\n" } @ip_probe;
      print $fh "EOF", "\n\n";
      my $cmd = qq{cat "ip_cidr.txt" | httpx \\
            -tech-detect \\
            -status-code \\
            -server \\
            -content-length \\
            -probe \\
            -threads 96 \\
            -x "HEAD" \\
            -H "Host: $host" \\
            -H "User-Agent: $user_agent"};
      $cmd .= qq{ \\\n            -p http:80} if ($scheme eq 'http://');
      $cmd .= qq{ \\\n            -p https:443} if ($scheme eq 'https://');
      $cmd .= qq{ \\\n            -sni "$host"} if ($scheme eq 'https://');
      $cmd .= qq{ \\\n            -H "Referer: $referer"} if $referer;
      $cmd .= qq{ \\\n            -path "$uri" | \\
            tee "ip_probe_result.txt"\n};
      print $fh $cmd;
      print $fh q{cat "ip_probe_result.txt" | \\
            sed -r "s/\x1B\[[0-9;]*[mGKH]//g" | \\
            grep '\[SUCCESS\] \[200\]' | \\
            awk '{print$1}' | \\
            sed -e 's#^https\?://##' -e 's#/.*$##' | \\
            sort -V > "ip_avail.txt"
        }, "\n\n";
      print $fh qq{yes | \\
            cp  "ip_avail.txt" "/tmp/ip_avail_"$current_category".txt"
        }, "\n\n";

      # 关闭文件句柄
      close $fh;

      # 清空 @ip_probe 数组，为下一次循环做准备
      @ip_probe = ();

      print "-" x 32, "\n";
      print " Probe Instructions for $current_category: \n";
      print "     $filename \n";
      print "-" x 32, "\n";

      if ( not $options->{"dryrun"} ) {
        # 运行脚本内容
        system("/bin/sh", "$filename") == 0
          or die "Failed to execute script: $!";

        print "-" x 32, "\n";
        print "Probe Result for $current_category: \n";
        print "     /tmp/ip_avail_$current_category.txt\n";
        print "-" x 32, "\n";
      }
    }
    else {
      print "Malformed probe URL for $current_category: $probe_url\n";
    }
  }

  # 打印需要丰富填充 GeoIP 信息的 IP 地址段
  if (@ip_unknown) {
    print "-" x 32, "\n";
    print " Missing GeoIP Infomation \n";
    print "-" x 32, "\n";
    open my $fh, '>>', '/tmp/missing_geoip.txt' or die "Could not open file '/tmp/missing_geoip.txt' $!";
    map { print $fh $_, "\n"; print $_, "\n"; } sort {ip_to_packed($a) cmp ip_to_packed($b)} uniq(@ip_unknown);
    close $fh;
  }

  print "\n";
}

# 主函数，解析子命令和参数
sub main {
  my $help = 0;

  # 获取子命令
  my $subcommand = shift @ARGV;

  if ( !$subcommand ) {
    pod2usage(1);
  }

  # 解析子命令参数
  if ( $subcommand eq 'db' ) {
    my ($dump, $restore);

    GetOptions(
      'd|dump'    => \$dump,
      'r|restore' => \$restore,
      'h|help|?'  => \$help,
    ) or pod2usage(2);

    pod2usage(1) if $help;

    if ($dump || $restore) {
      dump_to_json() if $dump;
      restore_from_json() if $restore;
    } else {
      pod2usage(1);
    }
  }
  elsif ( $subcommand eq 'extract' ) {
    my $csvfile;

    GetOptions(
      'f|csvfile=s' => \$csvfile,
      'h|help|?'    => \$help,
    ) or pod2usage(2);

    pod2usage(1) if $help;
    extract($csvfile);
  }
  elsif ( $subcommand eq 'probe' ) {
    my ( $category, $dns_type, $dryrun);

    GetOptions(
      'c|category=s'  => \$category,
      't|type=s'      => \$dns_type,
      'd|dryrun'      => \$dryrun,
      'h|help|?'      => \$help,
    ) or pod2usage(2);

    pod2usage(1) if $help;
    probe( $category, { "dryrun" => $dryrun, "dns_type" => $dns_type } );
  }
  elsif ( $subcommand eq 'dns' ) {
    my ( $generate, $update, $evaluate, $category, $file );

    GetOptions(
      'u|update'         => \$update,
      'e|evaluate'       => \$evaluate,
      'c|category=s'     => \$category,
      'f|file=s'         => \$file,
      'h|help|?'         => \$help,
    ) or pod2usage(2);

    pod2usage(1) if $help;
    update_dns_data( $category, $file ) if $update;
    evaluate_dns_data( $category, $file ) if $evaluate;
  }
  elsif ( $subcommand eq 'geoip' ) {
    my ( $generate, $refresh );

    GetOptions(
      'g|generate'    => \$generate,
      'r|refresh'     => \$refresh,
      'h|help|?'      => \$help,
    ) or pod2usage(2);

    pod2usage(1)      if $help;
    gen_geoip_cfg()   if $generate;
    refresh_geoip_cfg if $refresh;
  }
  elsif ( $subcommand eq 'iptype' ) {
    my ( $generate, $update, $file );

    GetOptions(
      'g|generate'    => \$generate,
      'u|update'      => \$update,
      'f|file=s'      => \$file,
      'h|help|?'      => \$help,
    ) or pod2usage(2);

    pod2usage(1) if $help;
    gen_service_type_cfg($file) if $generate;
    update_ip_speed($file) if $update;
  }
  elsif ( $subcommand eq 'task' ) {
    my ( $config, $generate, $deploy, $evaluate, $task_name, $days_ago );

    GetOptions(
      'c|config'      => \$config,
      'g|generate'    => \$generate,
      'd|deploy'      => \$deploy,
      'e|evaluate'    => \$evaluate,
      't|task=s'      => \$task_name,
      'a|days-ago=i'  => \$days_ago,
      'h|help|?'      => \$help,
    ) or pod2usage(2);

    pod2usage(1) if $help;
    if ($config) {
      generate_task_config();
      deploy_task_config(REMOTE_CONF_SERVER);
    }
    if ($generate) {
      my $conditions = { days_ago => $days_ago // 30 }; # 默认访问 30 天内的数据
      generate_task_bundle($task_name, $conditions);
    }
    if ($deploy) {
      deploy_task_bundle($task_name, REMOTE_CONF_SERVER);
    }
    if ($evaluate) {
      my $conditions = { days_ago => $days_ago // 30 }; # 默认访问 30 天内的数据
      evaluate_task_bundle($task_name, $conditions);
    }
  }
  elsif ( $subcommand eq 'url' ) {
    my ( $category );

    GetOptions(
      'c|category=s'  => \$category,
      'h|help|?'      => \$help,
    ) or pod2usage(2);

    pod2usage(1) if $help;
    probe_urls( $category );
  }
  else {
    print "Unknown subcommand: $subcommand\n";
    pod2usage(1);
  }
}

main();

__END__

=head1 NAME

sp_cdnHandler.pl - 展示如何使用子命令和参数

=head1 SYNOPSIS

sp_cdnHandler.pl [command] [options]

=head1 OPTIONS

=over 4

=item B<db>

-d <dump>
-r <restore>n

=item B<extract>

-f "*dns-result.csv"

=item B<probe>

-c "apple_swcdn"
-t <dns type: A or AAAA>
-d <dryrun>

=item B<dns>

-u <update dns data>
-e <evaluate dns data>
-c "apple_swcdn"
-f "~/Downloads/ip_avail.txt"

=item B<geoip>

-g <generate config>
-r <refresh config>

=item B<iptype>

-g <generate config>
-u <update ip speed>
-f "assets/report/iptypes/*.csv"

=item B<task>

-c <generate task config>
-g <generate task bundle>
-d <deploy task bundle>
-e <evaluate task bundle>
-a <days ago>
-t <task name>

=item B<url>

-c "apple_swcdn"

=back

=head1 DESCRIPTION

B<This program> 演示了如何使用子命令和参数来调用脚本。

=cut
