#!/usr/bin/perl
use strict;
use warnings;
use JSON;
use DBI;
use Data::Dumper;
use Encode;

use FindBin;
use lib "$FindBin::Bin/modules";

use SP::Common;
use SP::JSON;
use SP::Domain;
use SP::GeoIP;
use SP::NetAddr;
use SP::SQLite;

my $dbh = db_open(CDN_DB_FILE);


# Command line argument handling
my $command = shift @ARGV || '';

if ($command eq 'restore') {
    restore_from_json($dbh);
} elsif ($command eq 'dump') {
    dump_to_json($dbh);
} elsif ($command eq 'backup') {
    db_backup($dbh);
} else {
    print "Usage: $0 [restore|dump|backup]\n";
    exit 1;
}

# my $data = load_cdn_providers( $dbh );
# my $json = JSON::XS->new->pretty->canonical->space_before(0)->space_after(0);
# print $json->encode($data), "\n";

# my $data = load_cdn_mappings( $dbh );
# print Dumper( $data ) , "\n";

# my $data = load_json_file(q{/Users/<USER>/Projects/Shell/stream_puller/configs/sp_file.json});
# store_sp_URLs( $data );

db_close($dbh);
