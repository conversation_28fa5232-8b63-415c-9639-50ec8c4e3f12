#!/usr/bin/env bash

# 确认可执行文件所在目录存在于 $PATH 环境变量中
export PATH="/usr/local/bin:$PATH"

# 设置目标URL。请务必将此占位符替换为您的实际目标URL。
TARGET_URL="https://www.iflyrec.com/download/tjzs/win/iflyrec_meeting_v3.0.1250.exe"

# 定义测试结果数组
declare -A test_results

# 格式化打印表格头部
print_table_header() {
  printf "\n%-20s %-15s %-15s %-15s %s\n" "配置ID" "CURL状态" "HTTP状态码" "响应时间(s)" "测试结果"
  printf "%s\n" "--------------------------------------------------------------------------------"
}

# 格式化打印表格行
print_table_row() {
  local id=$1
  local curl_status=$2
  local http_code=$3
  local time_total=$4
  local result=$5
  printf "%-20s %-15s %-15s %-15.3f %s\n" "$id" "$curl_status" "$http_code" "$time_total" "$result"
}

# 从 browser_profiles.json 文件中读取 JSON 数据
json_data=$(cat ./configs/browser_profiles.json)

# 打印表格头部
print_table_header

# 使用 jq 解析 JSON 数据并遍历每个配置文件
echo "$json_data" | jq -c '.[]' | while read -r profile; do
  id=$(echo "$profile" | jq -r '.id')
  command=$(echo "$profile" | jq -r '.command')
  ciphers=$(echo "$profile" | jq -r '.ciphers')

  # 构建 curl 命令的基础部分
  curl_args=()
  curl_args+=("$command")
  curl_args+=(--ciphers "$ciphers")

  # 添加 headers
  while IFS= read -r header_line; do
    curl_args+=(-H "$header_line")
  done < <(echo "$profile" | jq -r '.headers | to_entries[] | "\(.key): \(.value)"')

  # 添加 options
  while IFS= read -r option_line; do
    for option in $option_line; do
      [[ -n "$option" ]] && curl_args+=("$option")
    done
  done < <(echo "$profile" | jq -r '.options[]')

  # 添加通用 curl 选项
  curl_args+=(
    "--resolve" "www.iflyrec.com:443:[2409:8c5c:b00:201::2:2a]"
    "--insecure"
    "--retry" "0"
    "--connect-timeout" "3"
    "--max-time" "5"
    "--limit-rate" "980K"
    "--referer" "https://www.iflyrec.com/software/"
    "-w" "%{http_code}|%{time_total}"
    "-o" "/dev/null"
    "-s"
    "$TARGET_URL"
  )

  # 执行 curl 命令并获取结果
  result=$("${curl_args[@]}")
  curl_status=$?
  
  # 解析结果
  IFS='|' read -r http_code time_total <<< "$result"
  
  # 判断测试结果
  if [[ ($curl_status -eq 0 || $curl_status -eq 28) && ($http_code == "200" || $http_code == "206") ]]; then
    test_result="✅ 成功"
  else
    test_result="❌ 失败"
  fi

  # 打印结果行
  print_table_row "$id" "$curl_status" "$http_code" "$time_total" "$test_result"
  
  # 存储测试结果
  test_results["$id"]="$curl_status|$http_code|$time_total|$test_result"
done

# 打印汇总信息
total_tests=${#test_results[@]}
success_tests=$(printf '%s\n' "${test_results[@]}" | grep -c "✅ 成功")
fail_tests=$((total_tests - success_tests))

printf "\n测试汇总:\n"
printf "总测试数: %d\n" "$total_tests"
printf "成功数量: %d\n" "$success_tests"
printf "失败数量: %d\n" "$fail_tests"
printf "成功率: %.1f%%\n" "$(( success_tests * 100 / total_tests ))"
