#!/usr/bin/env bash

# shellcheck source=/dev/null

# 公共变量及函数定义
source sp_common.sh

source utils/sp_sync.sh

testing_sp_archiveConf() {
  sp_archiveConf "$REMOTE_CONF_SERVER" "http://192.168.16.69:8500/"
}

testing_sp_pullTaskConfig() {
  while true; do
    sp_pullTaskConfig "$REMOTE_CONF_SERVER"
    sleep 10
  done
}

testing_sp_pullAllTasks() {
  while true; do
    sp_syncAllTasks "$REMOTE_CONF_SERVER" "http://192.168.16.88:8500/"
    sleep 10
  done
} 

testing_sp_pullAllTasks