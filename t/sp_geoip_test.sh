#!/usr/bin/env bash

# shellcheck source=/dev/null

source utils/sp_geoip.sh

testing_load_db() {
  echo -n "==> Loading GeoIP DB... "
  load_cache
  echo "done"
  dump_cache
}

testing_lookup_v4() {
  load_cache
  # 生成临时文件
  tmpFile=$(mktemp -p "$TMPDIR")
  # 将待查询的内容写入临时文件
  cat >"${tmpFile}" <<EOF
*************
************
*******
EOF

  # 调用查询函数
  lookup_geoip_v4 "${tmpFile}" true

  # 删除临时文件
  rm -rf "${tmpFile}" >/dev/null
}

testing_lookup_v6() {
  load_cache
  # 生成临时文件
  tmpFile=$(mktemp -p "$TMPDIR")

  # 将待查询的内容写入临时文件
  cat >"${tmpFile}" <<EOF
2409:8c04:1001:000c:0001:0000:0000:0015
2409:8087:c00:12::1:f
2409:8087:7c00:10::303
2409:8087:74f1:16::24:16
2401:8da0:0000:0000:
2409:8c3c:1300:506::74
EOF

  # 调用查询函数
  lookup_geoip_v6 "${tmpFile}" true

  # 删除临时文件
  rm -rf "${tmpFile}" >/dev/null
}

testing_load_from_file() {
  # 从 dns 配置文件中读取 GeoIP 数据
  load_geoip_from_file "./configs/sp_dns.json"
}

# testing_load_db
# testing_lookup_v4
testing_lookup_v6
# testing_load_from_file