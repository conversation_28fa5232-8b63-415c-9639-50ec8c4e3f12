#!/usr/bin/env bash

# shellcheck source=/dev/null

source utils/sp_dns.sh

testing_sp_lookup() {
  # 测试 sp_lookup() 函数
  # 测试已经收录, 且满足筛选条件的域名
  sp_lookup swcdn.apple.com A
  sp_lookup download.imgo.tv AAAA
  sp_lookup d1.music.126.net
  # 测试已经收录, 但不满足筛选条件的域名
  sp_lookup b22-obs-ykj-01.obs.cidc-rp-2006.joint.cmecloud.cn A
  # 测试没有收录的域名
  sp_lookup download999.caiyun.feixin.10086.cn A
}

testing_sp_lookup