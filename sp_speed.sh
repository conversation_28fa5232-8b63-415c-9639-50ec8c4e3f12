#!/usr/bin/env bash

# shellcheck source=/dev/null

# 公共变量及函数定义
source sp_common.sh

srvConfFile=${CONFIG_FDR}/sp_speed.json
filterdRecords=${RESPONSE_FDR}/11_filtered_record.json
srvInfoFile=${RESPONSE_FDR}/12_selected_server.txt
srvResponseFile=${RESPONSE_FDR}/13_srv_reponse.txt

# 初始化运行环境
sp_init() {
  echo -n "==> Initializing speedtest mode ..."
  # 登录之前，先删除之前运行产生的应答文件
  rm -rf "$RESPONSE_FDR" > /dev/null 2>&1
  # 重新创建应答文件目录
  mkdir "$RESPONSE_FDR" > /dev/null 2>&1
  echo " done"  
}

# 读取配置文件, 随机返回服务器 IP
sp_cttl_getIP() {
  jq -rM '.cttl | map(select(.location | test(" 移动") ) | {ip: .ip, url: .url, host: .host, location: .location} )' "$srvConfFile" > "$filterdRecords"
  
  # 从服务器列表中，随机挑选一个 IP
  totalServers=$(jq 'length' "$filterdRecords")
  random_number=$((RANDOM % totalServers))
  record=$(jq -crM ".[${random_number}]" "$filterdRecords")
  
  # 提取出 IP  等字段
  ip=$(echo "$record" | jq -rM '.ip')
  host=$(echo "$record" | jq -rM '.host')
  url=$(echo "$record" | jq -rM '.url')
  
  # 提取出地址字段和运营商字段
  read -r loc isp <<< "$(echo "${record}" | jq -rM '.location')"
  
  echo "==> ${host} (ip: ${ip} loc: ${loc} isp: ${isp})"
  
  echo '{"ip": "'"$ip"'", "host": "'"$host"'", "url": "'"$url"'"}' > "${srvInfoFile}"
}

# 向服务器发送 PING 请求, 检测可用性
sp_cttl_ping() {
  local record
  record=$(cat "$srvInfoFile")
  local retries=0
  
  ip=$(echo "$record" | jq -rM '.ip')
  host=$(echo "$record" | jq -rM '.host')
  url=$(echo "$record" | jq -rM '.url')
  
  while [ $retries -lt "$MAX_RETRIES" ]; do
    # 发送 ping 请求，等待服务器回应
    echo -n "  -> Trying to ping ${ip}..."
    curl "$url" \
    --connect-timeout 2 \
    --silent \
    -X GET \
    -H "Accept: */*" \
    -H "User-Agent: UXTest/1.3.3 (cn.cttl.UXTest; build:1; iOS 17.4.1) Alamofire/5.8.0" \
    -H "Accept-Language: en-US;q=1.0, zh-Hans-US;q=0.9" \
    -H "Accept-Encoding: br;q=1.0, gzip;q=0.9, deflate;q=0.8" \
    -H "Connection: keep-alive" > "${srvResponseFile}"
    
    # 如果 curl 命令执行失败，则重试
    if [ $? -ne 0 ]; then
      echo ' failed, server unreachable, retrying...'
    else
      local resp
      resp=$(cat "${srvResponseFile}")
      # 如果应答报文不为 pong, 则判断执行失败, 重试
      if [ "$resp" == "pong" ]; then
        echo " done"
        
        return 0
      else
        echo " failed, invalid response: ${resp}, retrying..."
      fi
    fi
    
    retries=$((retries + 1))
    if [ $retries -eq "$MAX_RETRIES" ]; then
      exit 1 # Exit with error
    fi
    
    sleep "$RETRY_INTERVAL"
  done
}

# 向服务器发送下载请求
sp_cttl_download() {
  local record
  record=$(cat "${srvInfoFile}")
  
  ip=$(echo "$record" | jq -rM '.ip')
  host=$(echo "$record" | jq -rM '.host')
  url=$(echo "$record" | jq -rM '.url')
  port=$(echo "$url" | sed -E 's#http://[^:/]+:([0-9]+)/.*#\1#')
  
  for ((t=1; t<=$DOWNLOAD_TIMES; t++)); do
    echo "  -> Downloading ..."
    url="http://${host}:${port}/speedtest/download"
    timeout "${MAX_TIME}" \
    wget -q "$url" \
      --no-check-certificate \
      --show-progress \
      --progress=dot:giga \
      --tries="$MAX_RETRIES" \
      --wait="$RETRY_INTERVAL" \
      --random-wait \
      --dns-timeout="$DNS_TIMEOUT" \
      --connect-timeout="$CONNECT_TIMEOUT" \
      --read-timeout="$READ_TIMEOUT" \
      --user-agent="UXTest/1.3.3 (cn.cttl.UXTest; build:1; iOS 17.4.0) Alamofire/5.8.0" \
      --header="Accept-Language: en-US;q=1.0, zh-Hans-US;q=0.9" \
      --header="Accept-Encoding: br;q=1.0, gzip;q=0.9, deflate;q=0.8" \
      --header="Connection: keep-alive" \
      -O /dev/null
  done
}

# 初始化
sp_init

# 获取 IP
sp_cttl_getIP 

# 测试可用性
sp_cttl_ping

# 下载拉流
sp_cttl_download

# 完成
sp_done


