{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": [], "__requires": [{"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.3.7"}, {"type": "panel", "id": "graph", "name": "Graph (old)", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table-old", "name": "Table (old)", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Dashboard for OpenWRT routers with installed lua scripts.", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 11147, "graphTooltip": 0, "id": null, "iteration": 1721975268391, "links": [], "liveNow": false, "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 308, "panels": [{"columns": [], "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fontSize": "100%", "gridPos": {"h": 2, "w": 24, "x": 0, "y": 1}, "id": 314, "links": [], "pluginVersion": "6.3.6", "scroll": false, "showHeader": true, "sort": {"desc": false}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "node_openwrt_info{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "", "refId": "A"}], "transform": "timeseries_aggregations", "type": "table-old"}, {"columns": [], "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fontSize": "100%", "gridPos": {"h": 2, "w": 24, "x": 0, "y": 3}, "id": 315, "links": [], "pluginVersion": "6.3.6", "scroll": false, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "node_uname_info{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "", "refId": "A"}], "transform": "timeseries_aggregations", "type": "table-old"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Busy state of all CPU cores together", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 85}, {"color": "rgba(245, 54, 54, 0.9)", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 5}, "id": 20, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "(((count(count(node_cpu_seconds_total{instance=~\"$node:$port\",job=~\"$job\"}) by (cpu))) - avg(sum by (mode)(irate(node_cpu_seconds_total{mode='idle',instance=~\"$node:$port\",job=~\"$job\"}[5m])))) * 100) / count(count(node_cpu_seconds_total{instance=~\"$node:$port\",job=~\"$job\"}) by (cpu))", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 900}], "title": "CPU Busy", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Non available RAM memory", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 5}, "hideTimeOverride": false, "id": 16, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "((node_memory_MemTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_MemFree_bytes{instance=~\"$node:$port\",job=~\"$job\"}) / (node_memory_MemTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} )) * 100", "format": "time_series", "hide": true, "intervalFactor": 1, "refId": "A", "step": 900}, {"expr": "100 - ((node_memory_MemAvailable_bytes{instance=~\"$node:$port\",job=~\"$job\"} * 100) / node_memory_MemTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "B", "step": 900}], "title": "Used RAM Memory", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "average Wifi quality", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(50, 172, 45, 0.97)", "value": 30}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 8, "y": 5}, "hideTimeOverride": false, "id": 310, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "avg(wifi_network_quality{instance=~\"$node:$port\",job=~\"$job\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "B", "step": 900}], "title": "Wifi Quality", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Busy state of all CPU cores together (1 min average)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 85}, {"color": "rgba(245, 54, 54, 0.9)", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 5}, "id": 19, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "avg(node_load1{instance=~\"$node:$port\",job=~\"$job\"}) /  count(count(node_cpu_seconds_total{instance=~\"$node:$port\",job=~\"$job\"}) by (cpu)) * 100", "hide": false, "intervalFactor": 1, "refId": "A", "step": 900}], "title": "CPU System Load (1m avg)", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Busy state of all CPU cores together (5 min average)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 85}, {"color": "rgba(245, 54, 54, 0.9)", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 15, "y": 5}, "id": 155, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "avg(node_load5{instance=~\"$node:$port\",job=~\"$job\"}) /  count(count(node_cpu_seconds_total{instance=~\"$node:$port\",job=~\"$job\"}) by (cpu)) * 100", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "A", "step": 900}], "title": "CPU System Load (5m avg)", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Used Swap", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(245, 54, 54, 0.9)", "value": 25}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 18, "y": 5}, "id": 21, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "((node_memory_SwapTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_SwapFree_bytes{instance=~\"$node:$port\",job=~\"$job\"}) / (node_memory_SwapTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} )) * 100", "intervalFactor": 1, "refId": "A", "step": 900}], "title": "Used SWAP", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Used Root FS", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 21, "y": 5}, "id": 154, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "100 - ((node_filesystem_avail_bytes{instance=~\"$node:$port\",job=~\"$job\",mountpoint=\"/\",fstype!=\"rootfs\"} * 100) / node_filesystem_size_bytes{instance=~\"$node:$port\",job=~\"$job\",mountpoint=\"/\",fstype!=\"rootfs\"})", "format": "time_series", "intervalFactor": 1, "refId": "A", "step": 900}], "title": "Used Root FS", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total number of CPU cores", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 9}, "id": 14, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "count(count(node_cpu_seconds_total{instance=~\"$node:$port\",job=~\"$job\"}) by (cpu))", "intervalFactor": 1, "refId": "A", "step": 900}], "title": "CPU Cores", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "all connected Wifi clients", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 3, "y": 9}, "id": 306, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "sum(wifi_stations)", "legendFormat": "", "refId": "A"}], "title": "Wifi Clients", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "System uptime", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 6, "y": 9}, "hideTimeOverride": true, "id": 15, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "node_time_seconds{instance=~\"$node:$port\",job=~\"$job\"} - node_boot_time_seconds{instance=~\"$node:$port\",job=~\"$job\"}", "intervalFactor": 2, "refId": "A", "step": 1800}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total RAM", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 9, "y": 9}, "id": 75, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "node_memory_MemTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "intervalFactor": 1, "refId": "A", "step": 900}], "title": "Total RAM", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total network traffic", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 12, "y": 9}, "id": 312, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "(sum(node_network_transmit_bytes_total{instance=~\"$node:$port\",job=~\"$job\"}) + sum(node_network_receive_bytes_total{instance=~\"$node:$port\",job=~\"$job\"}))", "intervalFactor": 1, "refId": "A", "step": 900}], "title": "Total Network Traffic", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total NAT traffic (WAN)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bits"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 15, "y": 9}, "id": 311, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "sum(node_nat_traffic{instance=~\"$node:$port\",job=~\"$job\"})", "intervalFactor": 1, "refId": "A", "step": 900}], "title": "Total NAT Traffic", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total SWAP", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 18, "y": 9}, "id": 18, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "node_memory_SwapTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "intervalFactor": 1, "refId": "A", "step": 900}], "title": "Total SWAP", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total RootFS", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 21, "y": 9}, "id": 23, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "node_filesystem_size_bytes{instance=~\"$node:$port\",job=~\"$job\",mountpoint=\"/\",fstype!=\"rootfs\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "A", "step": 900}], "title": "Total RootFS", "type": "stat"}], "title": "Overview", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 265, "panels": [], "title": "CPU Memory", "type": "row"}, {"aliasColors": {"Busy": "#EAB839", "Busy Iowait": "#890F02", "Busy other": "#1F78C1", "Idle": "#052B51", "Idle - Waiting for something to happen": "#052B51", "guest": "#9AC48A", "idle": "#052B51", "iowait": "#EAB839", "irq": "#BF1B00", "nice": "#C15C17", "softirq": "#E24D42", "steal": "#FCE2DE", "system": "#508642", "user": "#5195CE"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "description": "Basic CPU info", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 2}, "hiddenSeries": false, "id": 77, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": 250, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "<PERSON><PERSON>", "color": "#890F02"}, {"alias": "Idle", "color": "#7EB26D"}, {"alias": "Busy System", "color": "#EAB839"}, {"alias": "Busy User", "color": "#0A437C"}, {"alias": "Busy Other", "color": "#6D1F62"}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum by (instance)(irate(node_cpu_seconds_total{mode=\"system\",instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Busy System", "refId": "B", "step": 240}, {"expr": "sum by (instance)(irate(node_cpu_seconds_total{mode='user',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Busy User", "refId": "D", "step": 240}, {"expr": "sum by (instance)(irate(node_cpu_seconds_total{mode='iowait',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "<PERSON><PERSON>", "refId": "E", "step": 240}, {"expr": "sum by (instance)(irate(node_cpu_seconds_total{mode=~\".*irq\",instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Busy IRQs", "refId": "F", "step": 240}, {"expr": "sum (irate(node_cpu_seconds_total{mode!='idle',mode!='user',mode!='system',mode!='iowait',mode!='irq',mode!='softirq',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Busy Other", "refId": "A", "step": 240}, {"expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='idle',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Idle", "refId": "C", "step": 240}], "thresholds": [], "timeRegions": [], "title": "CPU Basic", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "SWAP Used": "#BF1B00", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap Used": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "description": "Basic memory usage", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 2}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "RAM Total", "color": "#E0F9D7", "fill": 0, "stack": false}, {"alias": "RAM Cache + Buffer", "color": "#052B51"}, {"alias": "RAM Free", "color": "#7EB26D"}, {"alias": "Avaliable", "color": "#DEDAF7", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "node_memory_MemTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "RAM Total", "refId": "A", "step": 240}, {"expr": "node_memory_MemTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_MemFree_bytes{instance=~\"$node:$port\",job=~\"$job\"} - (node_memory_Cached_bytes{instance=~\"$node:$port\",job=~\"$job\"} + node_memory_Buffers_bytes{instance=~\"$node:$port\",job=~\"$job\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "RAM Used", "refId": "D", "step": 240}, {"expr": "node_memory_Cached_bytes{instance=~\"$node:$port\",job=~\"$job\"} + node_memory_Buffers_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "RAM Cache + Buffer", "refId": "B", "step": 240}, {"expr": "node_memory_MemFree_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "RAM Free", "refId": "F", "step": 240}, {"expr": "(node_memory_SwapTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_SwapFree_bytes{instance=~\"$node:$port\",job=~\"$job\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "SWAP Used", "refId": "G", "step": 240}], "thresholds": [], "timeRegions": [], "title": "Memory Basic", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Idle - Waiting for something to happen": "#052B51", "guest": "#9AC48A", "idle": "#052B51", "iowait": "#EAB839", "irq": "#BF1B00", "nice": "#C15C17", "softirq": "#E24D42", "steal": "#FCE2DE", "system": "#508642", "user": "#5195CE"}, "bars": false, "dashLength": 10, "dashes": false, "decimals": 2, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode=\"system\",instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "interval": "10s", "intervalFactor": 2, "legendFormat": "System - Processes executing in kernel mode", "refId": "A", "step": 20}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='user',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "User - Normal processes executing in user mode", "refId": "B", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='nice',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Nice - Niced processes executing in user mode", "refId": "C", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='idle',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Idle - Waiting for something to happen", "refId": "F", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='iowait',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Iowait - Waiting for <PERSON><PERSON><PERSON> to complete", "refId": "D", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='irq',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Irq - Servicing interrupts", "refId": "G", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='softirq',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Softirq - Servicing softirqs", "refId": "H", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='steal',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Steal - Time spent in other operating systems when running in a virtualized environment", "refId": "E", "step": 240}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "sum by (mode)(irate(node_cpu_seconds_total{mode='guest',instance=~\"$node:$port\",job=~\"$job\"}[5m])) * 100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Guest - Time spent running a virtual CPU for a guest operating system", "refId": "I", "step": 240}], "thresholds": [], "timeRegions": [], "title": "CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Percentage", "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap - Swap memory usage": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839", "Unused - Free memory unasigned": "#052B51"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Harware Corrupted - *./", "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "node_memory_MemTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_MemFree_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_Buffers_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_Cached_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_Slab_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_PageTables_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_SwapCached_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Apps - Memory used by user-space applications", "refId": "Q", "step": 240}, {"expr": "node_memory_PageTables_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "PageTables - Memory used to map between virtual and physical memory addresses", "refId": "G", "step": 240}, {"expr": "node_memory_SwapCached_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "SwapCache - Memory that keeps track of pages that have been fetched from swap but not yet been modified", "refId": "F", "step": 240}, {"expr": "node_memory_Slab_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Slab - Memory used by the kernel to cache data structures for its own use (caches like inode, dentry, etc)", "refId": "E", "step": 240}, {"expr": "node_memory_Cached_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Cache - Parked file data (file content) cache", "refId": "C", "step": 240}, {"expr": "node_memory_Buffers_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Buffers - Block device (e.g. harddisk) cache", "refId": "B", "step": 240}, {"expr": "node_memory_MemFree_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Unused - Free memory unasigned", "refId": "D", "step": 240}, {"expr": "(node_memory_SwapTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"} - node_memory_SwapFree_bytes{instance=~\"$node:$port\",job=~\"$job\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Swap - Swap space used", "refId": "I", "step": 240}, {"expr": "node_memory_HardwareCorrupted_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working", "refId": "O", "step": 240}], "thresholds": [], "timeRegions": [], "title": "Memory Stack", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 266, "panels": [{"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 44}, "id": 136, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 2, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "node_memory_Inactive_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Inactive - Memory which has been less recently used.  It is more eligible to be reclaimed for other purposes", "refId": "K", "step": 4}, {"expr": "node_memory_Active_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Active - Memory that has been used more recently and usually not reclaimed unless absolutely necessary", "refId": "J", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Active / Inactive", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 44}, "id": 135, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Committed_AS - *./"}, {"alias": "/.*CommitLimit - *./", "color": "#BF1B00", "fill": 0}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_Committed_AS_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Committed_AS - Amount of memory presently allocated on the system", "refId": "A", "step": 4}, {"expr": "node_memory_CommitLimit_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "CommitLimit - Amount of  memory currently available to be allocated on the system", "refId": "M", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Commited", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 54}, "id": 191, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "node_memory_Inactive_file_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Inactive_file - File-backed memory on inactive LRU list", "refId": "A", "step": 4}, {"expr": "node_memory_Inactive_anon_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Inactive_anon - Anonymous and swap cache on inactive LRU list, including tmpfs (shmem)", "refId": "D", "step": 4}, {"expr": "node_memory_Active_file_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Active_file - File-backed memory on active LRU list", "refId": "B", "step": 4}, {"expr": "node_memory_Active_anon_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Active_anon - Anonymous and swap cache on active least-recently-used (LRU) list, including tmpfs", "refId": "C", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Active / Inactive Detail", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "bytes", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"Active": "#99440A", "Buffers": "#58140C", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Dirty": "#6ED0E0", "Free": "#B7DBAB", "Inactive": "#EA6460", "Mapped": "#052B51", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "Slab_Cache": "#EAB839", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Total": "#511749", "Total RAM": "#052B51", "Total RAM + Swap": "#052B51", "Total Swap": "#614D93", "VmallocUsed": "#EA6460"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 54}, "id": 130, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 2, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_Writeback_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Writeback - Memory which is actively being written back to disk", "refId": "J", "step": 4}, {"expr": "node_memory_WritebackTmp_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "WritebackTmp - Memory used by FUSE for temporary writeback buffers", "refId": "K", "step": 4}, {"expr": "node_memory_Dirty_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Dirty - Memory which is waiting to get written back to the disk", "refId": "A", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Writeback and Dirty", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 64}, "id": 138, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_Mapped_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Mapped - Used memory in mapped pages files which have been mmaped, such as libraries", "refId": "A", "step": 4}, {"expr": "node_memory_Shmem_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Shmem - Used shared memory (shared between several processes, thus including RAM disks)", "refId": "B", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Shared and Mapped", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Active": "#99440A", "Buffers": "#58140C", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Dirty": "#6ED0E0", "Free": "#B7DBAB", "Inactive": "#EA6460", "Mapped": "#052B51", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "Slab_Cache": "#EAB839", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Total": "#511749", "Total RAM": "#052B51", "Total RAM + Swap": "#052B51", "Total Swap": "#614D93", "VmallocUsed": "#EA6460"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 64}, "id": 131, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 2, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "node_memory_SUnreclaim_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "SUnreclaim - Part of Slab, that cannot be reclaimed on memory pressure", "refId": "O", "step": 4}, {"expr": "node_memory_SReclaimable_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "SReclaimable - Part of Slab, that might be reclaimed, such as caches", "refId": "N", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Slab", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Active": "#99440A", "Buffers": "#58140C", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Dirty": "#6ED0E0", "Free": "#B7DBAB", "Inactive": "#EA6460", "Mapped": "#052B51", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "Slab_Cache": "#EAB839", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Total": "#511749", "Total RAM": "#052B51", "Total RAM + Swap": "#052B51", "VmallocUsed": "#EA6460"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 74}, "id": 70, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_VmallocChunk_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "VmallocChunk - Largest contigious block of vmalloc area which is free", "refId": "H", "step": 4}, {"expr": "node_memory_VmallocTotal_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "VmallocTotal - Total size of vmalloc memory area", "refId": "I", "step": 4}, {"expr": "node_memory_VmallocUsed_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "VmallocUsed - Amount of vmalloc area which is used", "refId": "O", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Vmalloc", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 74}, "id": 159, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_Bounce_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Bounce - Memory used for block device bounce buffers", "refId": "N", "step": 4}], "thresholds": [], "timeRegions": [], "title": "<PERSON> Bo<PERSON>ce", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Active": "#99440A", "Buffers": "#58140C", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Dirty": "#6ED0E0", "Free": "#B7DBAB", "Inactive": "#EA6460", "Mapped": "#052B51", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "Slab_Cache": "#EAB839", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Total": "#511749", "Total RAM": "#052B51", "Total RAM + Swap": "#052B51", "VmallocUsed": "#EA6460"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 84}, "id": 129, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Inactive *./", "transform": "negative-Y"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_AnonHugePages_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "AnonHugePages - Memory in anonymous huge pages", "refId": "D", "step": 4}, {"expr": "node_memory_AnonPages_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "AnonPages - Memory in user pages not backed by files", "refId": "G", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Anonymous", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 84}, "id": 160, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 2, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_KernelStack_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "KernelStack - Kernel memory stack. This is not reclaimable", "refId": "N", "step": 4}], "thresholds": [], "timeRegions": [], "title": "<PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Active": "#99440A", "Buffers": "#58140C", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Dirty": "#6ED0E0", "Free": "#B7DBAB", "Inactive": "#EA6460", "Mapped": "#052B51", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "Slab_Cache": "#EAB839", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Total": "#511749", "Total RAM": "#052B51", "Total RAM + Swap": "#052B51", "Total Swap": "#614D93", "VmallocUsed": "#EA6460"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 94}, "id": 132, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_NFS_Unstable_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "NFS Unstable - Memory in NFS pages sent to the server, but not yet commited to the storage", "refId": "L", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory NFS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Harware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "decimals": 2, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 94}, "id": 137, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 350, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_Unevictable_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Unevictable - Amount of unevictable memory that can't be swapped out for a variety of reasons", "refId": "P", "step": 4}, {"expr": "node_memory_Mlocked_bytes{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "MLocked - Size of pages locked to memory using the mlock() system call", "refId": "C", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Memory Unevictable and MLocked", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "Bytes", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}], "title": "Memory Detail Meminfo", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 269, "panels": [], "title": "System Detail", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 23}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_context_switches_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Context switches", "refId": "A", "step": 240}, {"expr": "irate(node_intr_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Interrupts", "refId": "B", "step": 240}], "thresholds": [], "timeRegions": [], "title": "Context Switches / Interrupts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Counter", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 23}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_load1{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 4, "legendFormat": "Load 1m", "refId": "A", "step": 480}, {"expr": "node_load5{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 4, "legendFormat": "Load 5m", "refId": "B", "step": 480}, {"expr": "node_load15{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 4, "legendFormat": "Load 15m", "refId": "C", "step": 480}], "thresholds": [], "timeRegions": [], "title": "System Load", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Load", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 33}, "hiddenSeries": false, "id": 148, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_forks_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Processes forks second", "refId": "C", "step": 240}], "thresholds": [], "timeRegions": [], "title": "Processes  Forks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Forks / sec", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_filefd_maximum{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 4, "legendFormat": "Max open files", "refId": "A", "step": 8}, {"expr": "node_filefd_allocated{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Open files", "refId": "B", "step": 4}], "thresholds": [], "timeRegions": [], "title": "File Descriptor", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Files", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 43}, "id": 285, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 15}, "id": 309, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"expr": "sum(wifi_stations)", "legendFormat": "", "refId": "A"}], "title": "Wifi Stations", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Non available RAM memory", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(50, 172, 45, 0.97)", "value": 30}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 3, "x": 3, "y": 15}, "hideTimeOverride": false, "id": 286, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "wifi_network_quality{instance=~\"$node:$port\",job=~\"$job\",ifname=\"wlan0\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "B", "step": 900}], "title": "wlan0 quality", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Non available RAM memory", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(50, 172, 45, 0.97)", "value": 30}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 3, "x": 6, "y": 15}, "hideTimeOverride": false, "id": 287, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "wifi_network_quality{instance=~\"$node:$port\",job=~\"$job\",ifname=\"wlan0-1\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "B", "step": 900}], "title": "wlan0-1 quality", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Non available RAM memory", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(50, 172, 45, 0.97)", "value": 30}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 3, "x": 9, "y": 15}, "hideTimeOverride": false, "id": 288, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.3.7", "targets": [{"expr": "wifi_network_quality{instance=~\"$node:$port\",job=~\"$job\",ifname=\"wlan1\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "B", "step": 900}], "title": "wlan1 quality", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "id": 291, "interval": "5m", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wifi_network_noise_dbm{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WiFi Network Noise", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "dBm", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 3, "x": 0, "y": 17}, "id": 290, "interval": "5m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wifi_stations{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WiFi Stations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 22}, "id": 292, "interval": "5m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wifi_network_bitrate{instance=~\"$node:$port\",job=~\"$job\"}/1024", "legendFormat": "{{ifname}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WiFi Bitrate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Mbits", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 22}, "id": 299, "interval": "5m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wifi_network_quality{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "{{ifname}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WiFi Network Quality", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "percent", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 28}, "id": 297, "interval": "5m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wifi_station_expected_throughput_kilobits_per_second{instance=~\"$node:$port\",job=~\"$job\"}/1024", "legendFormat": "{{mac}} # {{ifname}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WiFi Station Expected Throughput", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Mbits", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 28}, "id": 298, "interval": "5m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wifi_station_signal_dbm{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "{{mac}} # {{ifname}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WiFi Station Signal", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "dBm", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 35}, "id": 316, "interval": "5m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wifi_station_inactive_milliseconds{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "{{mac}} # {{ifname}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WiFi Station inactive", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "ms", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "WiFi", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 44}, "id": 294, "panels": [], "title": "Network Traffic", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 45}, "hiddenSeries": false, "id": 295, "interval": "5m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_network_transmit_bytes_total{instance=~\"$node:$port\",job=~\"$job\"}/1024/1024", "legendFormat": "{{device}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Transmit Bytes Total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "decmbytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 45}, "hiddenSeries": false, "id": 300, "interval": "5m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_network_receive_bytes_total{instance=~\"$node:$port\",job=~\"$job\"}/1024/1024", "legendFormat": "{{device}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Receive Bytes Total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "decmbytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"receive_packets_eth0": "#7EB26D", "receive_packets_lo": "#E24D42", "transmit_packets_eth0": "#7EB26D", "transmit_packets_lo": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "fieldConfig": {"defaults": {"links": [], "unit": "Kbits"}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 53}, "hiddenSeries": false, "id": 302, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "irate(node_network_receive_bytes_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])/1024*8", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{device}} - Receive", "refId": "O", "step": 4}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "irate(node_network_transmit_bytes_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])/1024*8", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{device}} - Transmit", "refId": "P", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "Kbits", "label": "Bytes out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {"receive_packets_eth0": "#7EB26D", "receive_packets_lo": "#E24D42", "transmit_packets_eth0": "#7EB26D", "transmit_packets_lo": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 53}, "hiddenSeries": false, "id": 60, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_transmit_packets_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Transmit", "refId": "O", "step": 4}, {"expr": "irate(node_network_receive_packets_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Receive", "refId": "P", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic by Packets", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "pps", "label": "Packets out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 63}, "hiddenSeries": false, "id": 296, "interval": "5m", "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_network_transmit_packets_total{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "{{device}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Transmit Packets Total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 63}, "hiddenSeries": false, "id": 301, "interval": "5m", "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_network_receive_packets_total{instance=~\"$node:$port\",job=~\"$job\"}", "legendFormat": "{{device}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Receive Packets Total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 71}, "hiddenSeries": false, "id": 281, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "topk(15, node_nat_traffic{instance=~\"$node:$port\",job=~\"$job\"}/1024/1024)", "hide": false, "instant": false, "intervalFactor": 4, "legendFormat": "Dest= {{dest}} # Source= {{src}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Top NAT Traffic", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "decmbytes", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 79}, "id": 272, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 143, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.drop.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.drop.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.drop.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.drop.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*.drop.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*.drop.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*.drop.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_drop_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Receive drop", "refId": "G", "step": 4}, {"expr": "irate(node_network_transmit_drop_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Transmit drop", "refId": "H", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Drop", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "pps", "label": "Packets out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 7}, "hiddenSeries": false, "id": 142, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.errors.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.errors.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.errors.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.errors.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*.errors.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*.errors.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*.errors.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_errs_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Receive errors", "refId": "E", "step": 4}, {"expr": "irate(node_network_transmit_errs_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Rransmit errors", "refId": "F", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Errors", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "pps", "label": "Packets out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 17}, "hiddenSeries": false, "id": 146, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.multicast.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.multicast.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.multicast.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.multicast.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*.multicast.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*.multicast.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*.multicast.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_multicast_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Receive multicast", "refId": "M", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Multicast", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "pps", "label": "Packets out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 17}, "hiddenSeries": false, "id": 141, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.compressed.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.compressed.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.compressed.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.compressed.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*.compressed.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*.compressed.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*.compressed.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_compressed_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Receive compressed", "refId": "C", "step": 4}, {"expr": "irate(node_network_transmit_compressed_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Transmit compressed", "refId": "D", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Compressed", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "pps", "label": "Packets out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 145, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.frame.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.frame.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.frame.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.frame.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*.frame.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*.frame.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*.frame.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_frame_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{device}} - Receive frame", "refId": "K", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Frame", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "pps", "label": "Packets out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 144, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.fifo.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.fifo.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.fifo.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.fifo.*/", "color": "#EF843C"}, {"alias": "/.*eth3.*.fifo.*/", "color": "#E24D42"}, {"alias": "/.*eth4.*.fifo.*/", "color": "#1F78C1"}, {"alias": "/.*eth5.*.fifo.*/", "color": "#BA43A9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_receive_fifo_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Receive fifo", "refId": "I", "step": 4}, {"expr": "irate(node_network_transmit_fifo_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Transmit fifo", "refId": "J", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Fifo", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "pps", "label": "Packets out (-) / in (+)", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 37}, "hiddenSeries": false, "id": 232, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.carrier.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.carrier.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.carrier.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.carrier.*/", "color": "#EF843C"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_transmit_colls_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Transmit colls", "refId": "C", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Colls", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Counter", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 37}, "hiddenSeries": false, "id": 231, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sideWidth": 300, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Trans.*/", "transform": "negative-Y"}, {"alias": "/.*lo.*.carrier.*/", "color": "#7EB26D"}, {"alias": "/.*eth0.*.carrier.*/", "color": "#EAB839"}, {"alias": "/.*eth1.*.carrier.*/", "color": "#6ED0E0"}, {"alias": "/.*eth2.*.carrier.*/", "color": "#EF843C"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_network_transmit_carrier_total{instance=~\"$node:$port\",job=~\"$job\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{device}} - Statistic transmit_carrier", "refId": "C", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Network Traffic Carrier", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Counter", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 47}, "hiddenSeries": false, "id": 61, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "NF conntrack limit", "color": "#890F02", "fill": 0}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_nf_conntrack_entries{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "NF conntrack entries", "refId": "O", "step": 4}, {"expr": "node_nf_conntrack_entries_limit{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "NF conntrack limit", "refId": "P", "step": 4}], "thresholds": [], "timeRegions": [], "title": "NF Contrack", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Entries", "logBase": 1, "min": "0", "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}], "title": "Network Traffic Detail", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 80}, "id": 279, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "node_scrape_collector_duration_seconds{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{collector}} - Scrape duration", "refId": "A", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Node Exporter Scrape Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "label": "Seconds", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 157, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_scrape_collector_success{instance=~\"$node:$port\",job=~\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{collector}} - Scrape success", "refId": "A", "step": 4}], "thresholds": [], "timeRegions": [], "title": "Node Exporter Scrape Success", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "Counter", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}], "title": "Node Exporter", "type": "row"}], "refresh": "30s", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": [{"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "", "hide": 2, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(node_uname_info, job)", "refId": "Prometheus-job-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(node_uname_info,instance)", "hide": 0, "includeAll": false, "label": "Host:", "multi": false, "name": "node", "options": [], "query": {"query": "label_values(node_uname_info,instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/([^:]+):.*/", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "", "hide": 2, "includeAll": false, "label": "Port", "multi": false, "name": "port", "options": [], "query": {"query": "label_values(node_uname_info{instance=~\"$node:(.*)\"}, instance)", "refId": "Prometheus-port-Variable-Query"}, "refresh": 1, "regex": "/[^:]+:(.*)/", "skipUrlSync": false, "sort": 3, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "节点监控", "uid": "fLi0yXAWk", "version": 7, "weekStart": ""}