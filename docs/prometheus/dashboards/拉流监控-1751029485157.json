{"__inputs": [{"name": "DS_PROMETHEUS", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.3.7"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "拉流节点流量监控", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "iteration": 1751029243855, "links": [], "liveNow": true, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "title": "汇总", "type": "row"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 0, "y": 1}, "id": 13, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(irate(node_network_receive_bytes_total{device=\"pppoe-wan\",city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"}[1m]))*8 / (1000 * 1000 * 1000)", "interval": "", "legendFormat": "", "refId": "A"}], "title": "下行流量（Gbps）", "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 4, "y": 1}, "id": 15, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(irate(node_network_transmit_bytes_total{device=\"pppoe-wan\",city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"}[1m]))*8 / (1000 * 1000 * 1000)", "interval": "", "legendFormat": "", "refId": "A"}], "title": "上行流量（Gbps）", "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 8, "y": 1}, "id": 12, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(node_uname_info{city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "节点总数", "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 1}, "id": 16, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(node_network_info{collector=\"textfile\",public_ipv4!=\"\",city=\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "在线节点", "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 16, "y": 1}, "id": 17, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(node_docker_containers_running_total{city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "并发总数", "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 20, "y": 1}, "id": 14, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.3.7", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(node_nf_conntrack_entries{city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "会话总数", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 4}, "id": 2, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(irate(node_network_receive_bytes_total{device=\"pppoe-wan\",city=\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"}[1m]))*8", "format": "time_series", "instant": false, "interval": "15s", "intervalFactor": 1, "legendFormat": "下行流量", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(irate(node_network_transmit_bytes_total{device=\"pppoe-wan\",city=\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"}[1m]))*8", "format": "time_series", "hide": false, "instant": false, "interval": "15s", "intervalFactor": 1, "legendFormat": "上行流量", "refId": "B"}], "title": "总流量", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 19, "panels": [{"description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "filterable": true}, "mappings": [{"options": {"down": {"color": "red", "index": 1, "text": "down"}, "up": {"color": "green", "index": 0, "text": "up"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "当前状态"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "LAN IP"}, "properties": [{"id": "custom.width", "value": 145}, {"id": "mappings", "value": [{"options": {"pattern": "(\\d+\\.\\d+\\.\\d+\\.\\d+):.*", "result": {"index": 0, "text": "$1"}}, "type": "regex"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "组别"}, "properties": [{"id": "custom.width", "value": 67}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "机房"}, "properties": [{"id": "custom.width", "value": 111}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "网关"}, "properties": [{"id": "custom.width", "value": 124}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "IPv6前缀长度"}, "properties": [{"id": "custom.width", "value": 116}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "IPv6前缀委派"}, "properties": [{"id": "custom.width", "value": 121}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "公网IPv6"}, "properties": [{"id": "custom.width", "value": 159}]}]}, "gridPos": {"h": 22, "w": 24, "x": 0, "y": 14}, "id": 21, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "当前状态"}]}, "pluginVersion": "8.3.7", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "node_network_info{collector=\"textfile\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "节点信息", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "app": true, "city": true, "collector": true, "job": true, "province": true}, "indexByName": {"Time": 0, "Value": 19, "__name__": 1, "app": 2, "branch": 7, "city": 6, "collector": 3, "device": 11, "gateway": 13, "group": 8, "instance": 9, "ip": 10, "ipv6_prefix_delegation": 15, "ipv6_prefix_length": 16, "job": 4, "num_of_ipv6_addr": 17, "operstate": 18, "province": 5, "public_ipv4": 12, "public_ipv6": 14}, "renameByName": {"Value": "", "app": "", "branch": "机房", "city": "城市", "device": "WAN接口名称", "gateway": "IPv4网关", "group": "组别", "instance": "LAN IP", "ip": "WAN IP", "ipv6_prefix_delegation": "IPv6前缀委派", "ipv6_prefix_length": "IPv6前缀长度", "job": "", "num_of_ipv6_addr": "IPv6地址总数", "operstate": "当前状态", "province": "省份", "public_ipv4": "公网IPv4", "public_ipv6": "公网IPv6"}}}], "type": "table"}], "title": "节点信息", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 6, "panels": [{"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["***************:9100"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 15}, "id": 7, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "sortBy": "Mean", "sortDesc": true}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "irate(node_network_receive_bytes_total{device=\"pppoe-wan\",city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"}[1m])*8", "format": "time_series", "instant": false, "interval": "15s", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "sum(irate(node_network_receive_bytes_total{device=\"pppoe-wan\",city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"}[1m])*8)", "hide": false, "interval": "", "legendFormat": "Sum", "refId": "B"}], "title": "Network Traffic  / $branch / $group / $node", "transformations": [], "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Percentage", "axisPlacement": "auto", "axisSoftMax": 1, "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 15}, "id": 9, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "sortBy": "Mean", "sortDesc": true}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": true, "expr": "1-avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\",city=~\"$city\",branch=~\"$branch\",group=~\"$group\",instance=~\"$node:$port\"}[1m]))", "format": "time_series", "instant": false, "interval": "15s", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "title": "CPU Utilization / $branch / $group / $node", "transformations": [], "type": "timeseries"}], "title": "详细信息", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 25, "panels": [{"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 16}, "id": 23, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(rate(ifHCInOctets{instance=\"***************\",ifName=\"Eth-Trunk33\"}[300s]))*8 + sum(rate(ifHCInOctets{instance=\"***************\",ifName=\"Po10\"}[300s]))*8", "format": "time_series", "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "汇总 - In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(rate(ifHCOutOctets{instance=\"***************\",ifName=\"Eth-Trunk33\"}[300s]))*8 + sum(rate(ifHCOutOctets{instance=\"***************\",ifName=\"Po10\"}[300s]))*8", "format": "time_series", "hide": false, "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "汇总 - Out", "refId": "B"}], "title": "盒子流量 - 汇总", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 26, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(rate(ifHCInOctets{instance=\"***************\",ifName=\"Eth-Trunk33\"}[300s]))*8", "format": "time_series", "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "sum(rate(ifHCOutOctets{instance=\"***************\",ifName=\"Eth-Trunk33\"}[300s]))*8", "format": "time_series", "hide": false, "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "Out", "refId": "B"}], "title": "塔园路 - 接口流量 - to ME60 via Eth-Trunk33", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "id": 27, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCInOctets{instance=\"***************\",ifName=~\"25GE1/0/33|25GE1/0/34|25GE1/0/35|25GE1/0/36|25GE1/0/37|25GE1/0/38|25GE1/0/39|25GE1/0/40\"}[300s])*-8", "format": "time_series", "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCOutOctets{instance=\"***************\",ifName=~\"25GE1/0/33|25GE1/0/34|25GE1/0/35|25GE1/0/36|25GE1/0/37|25GE1/0/38|25GE1/0/39|25GE1/0/40\"}[300s])*8", "format": "time_series", "hide": false, "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - Out", "refId": "B"}], "title": "塔园路 - 接口流量 - to 服务器网口", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 28, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCInOctets{instance=\"***************\",ifName=~\"Te1/0/41|Te1/0/42|Te1/0/43|Te1/0/44|Te1/0/45|Te1/0/46|Te1/0/47|Te1/0/48|Po10\"}[300s])*8", "format": "time_series", "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCOutOctets{instance=\"***************\",ifName=~\"Te1/0/41|Te1/0/42|Te1/0/43|Te1/0/44|Te1/0/45|Te1/0/46|Te1/0/47|Te1/0/48|Po10\"}[300s])*8", "format": "time_series", "hide": false, "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - Out", "refId": "B"}], "title": "铁通 - 接口流量 - to ME60", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 29, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCInOctets{instance=\"***************\",ifName=~\"Te1/0/17|Te1/0/18|Te1/0/19|Te1/0/20|Te1/0/21|Te1/0/22|Te1/0/23|Te1/0/24|Te1/0/25|Te1/0/26|Te1/0/27|Te1/0/28|Te1/0/29|Te1/0/30|Te1/0/31|Te1/0/32\"}[300s])*-8", "format": "time_series", "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCOutOctets{instance=\"***************\",ifName=~\"Te1/0/17|Te1/0/18|Te1/0/19|Te1/0/20|Te1/0/21|Te1/0/22|Te1/0/23|Te1/0/24|Te1/0/25|Te1/0/26|Te1/0/27|Te1/0/28|Te1/0/29|Te1/0/30|Te1/0/31|Te1/0/32\"}[300s])*8", "format": "time_series", "hide": false, "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - Out", "refId": "B"}], "title": "铁通 - 接口流量 - to 服务器网口", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "id": 30, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCInOctets{instance=\"***************\",ifName=~\"10GE1/0/1|10GE1/0/2|10GE1/0/3|10GE1/0/4|10GE1/0/5|10GE1/0/6|10GE1/0/7|10GE1/0/8|10GE1/0/9|10GE1/0/10|10GE1/0/11|10GE1/0/12\"}[300s])*8", "format": "time_series", "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCOutOctets{instance=\"***************\",ifName=~\"10GE1/0/1|10GE1/0/2|10GE1/0/3|10GE1/0/4|10GE1/0/5|10GE1/0/6|10GE1/0/7|10GE1/0/8|10GE1/0/9|10GE1/0/10|10GE1/0/11|10GE1/0/12\"}[300s])*8", "format": "time_series", "hide": false, "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - Out", "refId": "B"}], "title": "铁通 - 接口流量 - 优化服务器 to ME60 ", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Bits out (-) / in (+)", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "id": 31, "options": {"legend": {"calcs": ["mean", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCInOctets{instance=\"***************\",ifName=~\"Eth-Trunk31\"}[300s])*8", "format": "time_series", "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - In", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "exemplar": false, "expr": "rate(ifHCOutOctets{instance=\"***************\",ifName=~\"Eth-Trunk31\"}[300s])*8", "format": "time_series", "hide": false, "instant": false, "interval": "60s", "intervalFactor": 1, "legendFormat": "{{ifName}} - Out", "refId": "B"}], "title": "铁通 - 接口流量 - to ME60 via Eth-Trunk31", "type": "timeseries"}], "title": "其他信息", "type": "row"}], "refresh": "1m", "schemaVersion": 34, "style": "dark", "tags": [], "templating": {"list": [{"current": {}, "definition": "label_values(node_uname_info,city)", "hide": 2, "includeAll": true, "multi": true, "name": "city", "options": [], "query": {"query": "label_values(node_uname_info,city)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query", "datasource": "${DS_PROMETHEUS}"}, {"allValue": "", "current": {}, "definition": "label_values(node_uname_info{city=~\"$city\"},branch)", "hide": 0, "includeAll": true, "label": "机房：", "multi": true, "name": "branch", "options": [], "query": {"query": "label_values(node_uname_info{city=~\"$city\"},branch)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query", "datasource": "${DS_PROMETHEUS}"}, {"current": {}, "definition": "label_values(node_uname_info{branch=~\"$branch\"},group)", "hide": 0, "includeAll": true, "label": "组别：", "multi": true, "name": "group", "options": [], "query": {"query": "label_values(node_uname_info{branch=~\"$branch\"},group)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query", "datasource": "${DS_PROMETHEUS}"}, {"current": {}, "definition": "label_values(node_uname_info{branch=~\"$branch\",group=~\"$group\"}, instance)", "hide": 0, "includeAll": true, "label": "节点：", "multi": true, "name": "node", "options": [], "query": {"query": "label_values(node_uname_info{branch=~\"$branch\",group=~\"$group\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/([^:]+):.*/", "skipUrlSync": false, "sort": 1, "type": "query", "datasource": "${DS_PROMETHEUS}"}, {"current": {}, "definition": "label_values(node_uname_info{branch=~\"$branch\",group=~\"$group\"}, instance)", "hide": 2, "includeAll": false, "label": "", "multi": false, "name": "port", "options": [], "query": {"query": "label_values(node_uname_info{branch=~\"$branch\",group=~\"$group\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/[^:]+:(.*)/", "skipUrlSync": false, "sort": 1, "type": "query", "datasource": "${DS_PROMETHEUS}"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "Asia/Shanghai", "title": "拉流监控", "uid": "H8qQXrQIk", "version": 71, "weekStart": "sunday"}