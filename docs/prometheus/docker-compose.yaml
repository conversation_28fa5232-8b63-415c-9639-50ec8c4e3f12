version: '3'

networks:
  prometheus:

services:
  snmp-exporter:
    image: ak.gforce.cn:5543/common/snmp-exporter:v0.20.0
    container_name: snmp_exporter
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./snmp.yml:/etc/snmp_exporter/snmp.yml
    ports:
      - "9116:9116"
    networks:
      - prometheus
    logging:
      driver: "json-file"  # 使用 json-file 日志驱动
      options:
        max-size: "10m"    # 每个日志文件的最大大小为 10MB
        max-file: "3"      # 保留最多 3 个日志文件

  prometheus:
    image: ak.gforce.cn:5543/common/prometheus:v2.34.0
    container_name: prometheus
    restart: always
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=1y'
      - '--storage.tsdb.retention.size=32GB'
    environment:
      - STORAGE_TSDB_RETENTION_TIME=10d  # 设置数据保留时间为10天
      - TZ=Asia/Shanghai
      - PUID=65534
      - PGID=65534
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus:/prometheus:z
    networks:
      - prometheus
    ports:
      - "9090:9090"
    logging:
      driver: "json-file"  # 使用 json-file 日志驱动
      options:
        max-size: "10m"    # 每个日志文件的最大大小为 10MB
        max-file: "3"      # 保留最多 3 个日志文件
    
  grafana:
    image: ak.gforce.cn:5543/common/grafana:8.3.7
    container_name: grafana
    restart: always
    depends_on:
      - prometheus
    environment:
      - TZ=Asia/Shanghai
      - PUID=472
      - PGID=472
    volumes:
      - ./grafana:/var/lib/grafana:z
    networks:
      - prometheus
    ports:
      - "3000:3000"
    logging:
      driver: "json-file"  # 使用 json-file 日志驱动
      options:
        max-size: "10m"    # 每个日志文件的最大大小为 10MB
        max-file: "3"      # 保留最多 3 个日志文件
