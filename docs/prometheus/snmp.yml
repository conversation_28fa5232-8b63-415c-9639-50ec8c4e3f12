# WARNING: This file was auto-generated using snmp_exporter generator, manual changes will be lost.
switch:
  walk:
  - *******.2.1.1
  - *******.*******.1.2
  - *******.********.1
  metrics:
  - name: sysDescr
    oid: *******.*******
    type: DisplayString
    help: A textual description of the entity - *******.*******
  - name: sysObjectID
    oid: *******.*******
    type: OctetString
    help: The vendor's authoritative identification of the network management subsystem
      contained in the entity - *******.*******
  - name: sysUpTime
    oid: *******.*******
    type: gauge
    help: The time (in hundredths of a second) since the network management portion
      of the system was last re-initialized. - *******.*******
  - name: sysContact
    oid: *******.*******
    type: DisplayString
    help: The textual identification of the contact person for this managed node,
      together with information on how to contact this person - *******.*******
  - name: sysName
    oid: *******.*******
    type: DisplayString
    help: An administratively-assigned name for this managed node - *******.*******
  - name: sysLocation
    oid: *******.*******
    type: DisplayString
    help: The physical location of this node (e.g., 'telephone closet, 3rd floor')
      - *******.*******
  - name: sysServices
    oid: *******.2.1.1.7
    type: gauge
    help: A value which indicates the set of services that this entity may potentially
      offer - *******.2.1.1.7
  - name: sysORLastChange
    oid: *******.2.1.1.8
    type: gauge
    help: The value of sysUpTime at the time of the most recent change in state or
      value of any instance of sysORID. - *******.2.1.1.8
  - name: sysORIndex
    oid: *******.*******.1.1
    type: gauge
    help: The auxiliary variable used for identifying instances of the columnar objects
      in the sysORTable. - *******.*******.1.1
    indexes:
    - labelname: sysORIndex
      type: gauge
  - name: sysORID
    oid: *******.*******.1.2
    type: OctetString
    help: An authoritative identification of a capabilities statement with respect
      to various MIB modules supported by the local SNMP application acting as a command
      responder. - *******.*******.1.2
    indexes:
    - labelname: sysORIndex
      type: gauge
  - name: sysORDescr
    oid: *******.*******.1.3
    type: DisplayString
    help: A textual description of the capabilities identified by the corresponding
      instance of sysORID. - *******.*******.1.3
    indexes:
    - labelname: sysORIndex
      type: gauge
  - name: sysORUpTime
    oid: *******.*******.1.4
    type: gauge
    help: The value of sysUpTime at the time this conceptual row was last instantiated.
      - *******.*******.1.4
    indexes:
    - labelname: sysORIndex
      type: gauge
  - name: ifInMulticastPkts
    oid: *******.********.1.1.2
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a multicast address at this sub-layer - *******.********.1.1.2
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifInBroadcastPkts
    oid: *******.********.1.1.3
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a broadcast address at this sub-layer - *******.********.1.1.3
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifOutMulticastPkts
    oid: *******.********.1.1.4
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a multicast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.4
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifOutBroadcastPkts
    oid: *******.********.1.1.5
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a broadcast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.5
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCInOctets
    oid: *******.********.1.1.6
    type: counter
    help: The total number of octets received on the interface, including framing
      characters - *******.********.1.1.6
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCInUcastPkts
    oid: *******.********.1.1.7
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were not addressed to a multicast or broadcast address at this sub-layer
      - *******.********.1.1.7
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCInMulticastPkts
    oid: *******.********.1.1.8
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a multicast address at this sub-layer - *******.********.1.1.8
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCInBroadcastPkts
    oid: *******.********.1.1.9
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a broadcast address at this sub-layer - *******.********.1.1.9
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCOutOctets
    oid: *******.********.1.1.10
    type: counter
    help: The total number of octets transmitted out of the interface, including framing
      characters - *******.********.1.1.10
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCOutUcastPkts
    oid: *******.********.1.1.11
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were not addressed to a multicast or broadcast address at this sub-layer,
      including those that were discarded or not sent - *******.********.1.1.11
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCOutMulticastPkts
    oid: *******.********.1.1.12
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a multicast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.12
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifHCOutBroadcastPkts
    oid: *******.********.1.1.13
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a broadcast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.13
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifLinkUpDownTrapEnable
    oid: *******.********.1.1.14
    type: gauge
    help: Indicates whether linkUp/linkDown traps should be generated for this interface
      - *******.********.1.1.14
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    enum_values:
      1: enabled
      2: disabled
  - name: ifHighSpeed
    oid: *******.********.1.1.15
    type: gauge
    help: An estimate of the interface's current bandwidth in units of 1,000,000 bits
      per second - *******.********.1.1.15
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  - name: ifPromiscuousMode
    oid: *******.********.1.1.16
    type: gauge
    help: This object has a value of false(2) if this interface only accepts packets/frames
      that are addressed to this station - *******.********.1.1.16
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    enum_values:
      1: "true"
      2: "false"
  - name: ifConnectorPresent
    oid: *******.********.1.1.17
    type: gauge
    help: This object has the value 'true(1)' if the interface sublayer has a physical
      connector and the value 'false(2)' otherwise. - *******.********.1.1.17
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    enum_values:
      1: "true"
      2: "false"
  - name: ifCounterDiscontinuityTime
    oid: *******.********.1.1.19
    type: gauge
    help: The value of sysUpTime on the most recent occasion at which any one or more
      of this interface's counters suffered a discontinuity - *******.********.1.1.19
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifAlias
      oid: *******.********.1.1.18
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifDescr
      oid: *******.*******.1.2
      type: DisplayString
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
  version: 2
  max_repetitions: 25
  retries: 3
  timeout: 15s
  auth:
    community: youhua2024
