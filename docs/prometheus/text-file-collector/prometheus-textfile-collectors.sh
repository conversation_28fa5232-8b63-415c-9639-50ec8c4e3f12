#!/bin/sh

INTERVAL=60

prom_metric_file_wan_ip="/var/prometheus/wan_ip_addr.prom"
prom_metric_file_docker="/var/prometheus/docker.prom"

is_valid_ipv4() {
  ip="$1"

  if echo "$ip" | grep -Eq '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$'; then
    return 0
  else
    return 1
  fi
}

while true; do
  # Get Public IP address
  public_ip=$(wget -q -T 10 -4 -O - http://ak.gforce.cn:9080/ip)
  public_ipv6=$(ifconfig pppoe-wan | grep -Eo "(2408:|2409:|240e:)([0-9a-fA-F]{1,4}:){0,7}([0-9a-fA-F]{1,4}|:)")

  # Extract gateway, dev and interface ip
  default_route=$(ip route  | grep default | tail -n 1)
  gateway=$(echo "$default_route" | awk '{for(i=1;i<=NF;i++){if($i=="via"){print $(i+1)}}}')
  dev=$(echo "$default_route" | awk '{for(i=1;i<=NF;i++){if($i=="dev"){print $(i+1)}}}')
  ip=$(ip addr show dev "${dev}" | grep 'inet ' | awk '{print$2}')

  # Use command substitution instead of pipeline to preserve variable scope
  json_str=$(/sbin/ifstatus wan_6 | jsonfilter -e '@["ipv6-prefix"][*]')
  if [ -n "$json_str" ]; then
    # Extract prefix delegation and length
    prefix_delegation=$(echo "$json_str" | jsonfilter -e '@.address')
    prefix_length=$(echo "$json_str" | jsonfilter -e '@.mask')
  fi

  # Number of running docker containers
  num_of_containers=$(/usr/bin/docker ps -q | wc -l)

  # Number of IPv6 addresses
  num_of_ipv6_addr=$(ifconfig | grep -Eo "(2408:|2409:|240e:)([0-9a-fA-F]{1,4}:){0,7}([0-9a-fA-F]{1,4}|:)" | wc -l)

  # create destination directory
  mkdir -p "$(dirname $prom_metric_file_wan_ip)" >/dev/null 2>&1

  # save ip address as Prometheus metrics
  echo "# TYPE node_network_info gauge"  > "$prom_metric_file_wan_ip"
  if is_valid_ipv4 "$public_ip" || [ "$num_of_ipv6_addr" -gt 0 ]; then
    printf "%s" "node_network_info{collector=\"textfile\",public_ipv4=\"${public_ip}\",public_ipv6=\"${public_ipv6}\",ip=\"${ip}\",gateway=\"${gateway}\",ipv6_prefix_delegation=\"${prefix_delegation}\",ipv6_prefix_length=\"${prefix_length}\",num_of_ipv6_addr=\"${num_of_ipv6_addr}\",device=\"${dev}\",operstate=\"up\"} 1" >> "$prom_metric_file_wan_ip"
  else 
    printf "%s" "node_network_info{collector=\"textfile\",public_ipv4=\"\",ip=\"${ip}\",gateway=\"${gateway}\",device=\"${dev}\",operstate=\"down\"} 1" >> "$prom_metric_file_wan_ip"
  fi

  # save docker containers as Prometheus metrics
  echo "# TYPE node_docker_containers_running_total gauge"  > "$prom_metric_file_docker"
  printf "%s %s" "node_docker_containers_running_total{collector=\"textfile\"}" "${num_of_containers}" >> "$prom_metric_file_docker"

  # sleep INTERVAL seconds
  sleep "${INTERVAL}"
done