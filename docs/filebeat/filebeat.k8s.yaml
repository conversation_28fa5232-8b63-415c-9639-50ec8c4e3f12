apiVersion: v1
kind: Service
metadata:
  name: ${NAMESPACE}-filebeat
  namespace: ${NAMESPACE}
  labels:
    component: filebeat
spec:
  selector:
    component: filebeat
    role: filebeat
  type: LoadBalancer
  loadBalancerIP: ${FILEBEAT_LBS_IP}
  ports:
  - name: udp-log
    port: 9001
    targetPort: 9001
    protocol: UDP
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: filebeat
  namespace: ${NAMESPACE}
  labels:
    component: filebeat
    role: filebeat
spec:
  replicas: ${NUM_OF_FILEBEAT_NODES}
  podManagementPolicy: Parallel
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      component: filebeat
      role: filebeat
  serviceName: filebeat
  template:
    metadata:
      labels:
        component: filebeat
        role: filebeat
    spec:
      containers:
      - name: filebeat
        image: docker.elastic.co/beats/filebeat:${ELK_VER}
        imagePullPolicy: IfNotPresent
        args: [
          "-e",
          "--strict.perms=false"
        ]
        securityContext:
          runAsUser: 0
        env:
        - name: ELASTIC_USER
          value: "${ELASTICSEARCH_USERNAME}"
        - name: ELASTIC_PASSWORD
          value: "${ELASTIC<PERSON>ARCH_PASSWORD}"
        - name: ELASTIC_HOSTS
          value: "https://${CLUSTER_NAME}-es-ingest:9200"
        resources:
          requests:
            cpu: 1
            memory: 1Gi
          limits:
            cpu: 12
            memory: 8Gi
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - ps -ef | grep filebeat | grep -v grep
          initialDelaySeconds: 20
          periodSeconds: 10
          failureThreshold: 3
        volumeMounts:
        - name: host-time
          mountPath: /etc/localtime
          readOnly: true
        - name: config
          mountPath: /usr/share/filebeat/filebeat.yml
          subPath: filebeat.yml
          readOnly: true
      - name: metricbeat-sidecar
        image: docker.elastic.co/beats/metricbeat:${ELK_VER}
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: metricbeat-config-volume
          mountPath: /usr/share/metricbeat/metricbeat.yml
          readOnly: true
          subPath: metricbeat.yml
        - name: metricbeat-modules-volume
          mountPath: /usr/share/metricbeat/modules.d
          readOnly: true   
      volumes:
      - name: host-time
        hostPath:
          path: /etc/localtime
      - name: config
        configMap:
          name: filebeat-config
          items:
            - key: filebeat.yml
              path: filebeat.yml
      - name: metricbeat-config-volume
        configMap:
          defaultMode: 0640
          name: metricbeat-config
      - name: metricbeat-modules-volume
        configMap:
          defaultMode: 0640
          name: metricbeat-modules
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: metricbeat-config
  namespace: ${NAMESPACE}
data:
  metricbeat.yml: |-
    metricbeat.config.modules:
      path: \${path.config}/modules.d/*.yml
      reload.enabled: true
      reload.period: 10s
    
    metricbeat.modules:
    - module: system
      enabled: false  # Disable system metrics

    output.elasticsearch:
      hosts: ["https://${CLUSTER_NAME}-es-ingest:9200"] # You probably need to modify elasticsearch URL
      protocol: "https"
      username: "${ELASTICSEARCH_USERNAME}"
      password: "${ELASTICSEARCH_PASSWORD}"
      ssl.verification_mode: "none"
    
    setup.ilm.enabled: true

    monitoring:
      enabled: true
      cluster_uuid: "${ELASTICSEARCH_CLUSTER_UUID}"
      elasticsearch:
        hosts: ["https://${CLUSTER_NAME}-es-ingest:9200"] 
        protocol: "https"
        username: "${ELASTICSEARCH_USERNAME}"
        password: "${ELASTICSEARCH_PASSWORD}"
        ssl.verification_mode: "none"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: metricbeat-modules
  namespace: ${NAMESPACE}
data:
  filebeat-xpack.yml: |-
    - module: beat
      metricsets:
        - stats
        - state
      xpack.enabled: true # This is what turns on xpack monitoring metric export
      period: 10s
      hosts: ["http://localhost:5066"] # Connect to filebeat metrics api using localhost inside pod
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-config
  namespace: ${NAMESPACE}
data:
  filebeat.yml: |
    filebeat.inputs:
      - type: udp
        max_message_size: 16KiB
        host: "0.0.0.0:9001"
        
    processors:
      - dissect:
          tokenizer: "%{flow.export.region},%{flow.export.city},%{flow.export.branch},%{flow.export.server},%{flow.export.instance},%{flow.src.ip.addr},%{flow.src.l4.port.id},%{flow.dst.ip.addr},%{flow.dst.l4.port.id},%{ip.version.name},%{flow.dst.ip.service.type},%{flow.dst.ip.service.provider},%{flow.dst.geo.country.name},%{flow.dst.geo.region.name},%{flow.dst.geo.city.name},%{flow.dst.geo.isp},%{http.response.status_code},%{flow.duration.seconds},%{flow.byte_rate},%{flow.bytes},%{task.name},%{http.request.host},%{http.request.url}"
          field: "message"
          target_prefix: ""
          ignore_failure: true
          overwrite_keys: true
    
      - convert:
          fields:
            - {from: "flow.bytes", to: "flow.bytes", type: "long"}
            - {from: "flow.byte_rate", to: "flow.byte_rate", type: "long"}
            - {from: "flow.dst.ip.addr", to: "flow.dst.ip.addr.ip", type: "ip"}
            - {from: "flow.src.ip.addr", to: "flow.src.ip.addr.ip", type: "ip"}
            - {from: "flow.dst.l4.port.id", to: "flow.dst.l4.port.id", type: "integer"}
            - {from: "flow.src.l4.port.id", to: "flow.src.l4.port.id", type: "integer"}
            - {from: "flow.duration.seconds", to: "flow.duration.seconds", type: "float"}
            - {from: "http.response.status_code", to: "http.response.status_code", type: "integer"}
          ignore_missing: true
          fail_on_error: false
    
      - drop_fields:
          fields: ["message", "log", "input", "ecs", "agent", "host", "stream"]
    
    http:
      enabled: true
      port: 5066
    
    monitoring.enabled: false
    
    output.console:
      enabled: false
      pretty: true
    
    output.elasticsearch:
      enabled: true
      hosts: ["https://${CLUSTER_NAME}-es-ingest:9200"]
      username: "${ELASTICSEARCH_USERNAME}"
      password: "${ELASTICSEARCH_PASSWORD}"
      ssl.verification_mode: "none"
      index: "streamflow-1.2.0"
    
    setup:
      template:
        enabled: true
        name: "streamflow-1.2.0"
        pattern: "streamflow-*"
