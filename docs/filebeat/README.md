# Filebeat 发送 docker 日志到 Elasticsearch 配置说明

## 目录

- [Filebeat 发送 docker 日志到 Elasticsearch 配置说明](#filebeat-发送-docker-日志到-elasticsearch-配置说明)
  - [目录](#目录)
  - [前言](#前言)
    - [默认的 Filebeat 数据流](#默认的-filebeat-数据流)
    - [查看默认的 Filebeat 数据流](#查看默认的-filebeat-数据流)
  - [配置 Filebeat 发送 docker 日志到指定的 Data Stream](#配置-filebeat-发送-docker-日志到指定的-data-stream)
    - [删除已存在的策略、模板和数据流](#删除已存在的策略模板和数据流)
    - [创建 Index Lifecycle Management 策略](#创建-index-lifecycle-management-策略)
    - [创建 Index Template](#创建-index-template)
    - [创建 Data Stream](#创建-data-stream)
    - [修改 Filebeat 配置](#修改-filebeat-配置)
    - [运行 Filebeat](#运行-filebeat)
  - [测试](#测试)

## 前言

Filebeat 是 Elastic 公司的一款轻量级日志采集工具，可以用来采集和发送日志到 Elasticsearch 中。

### 默认的 Filebeat 数据流

默认情况下，Filebeat 8 使用 Elasticsearch 8 上的一个新功能，称为数据流（Data Stream）。数据流是使用索引模板创建的索引的逻辑分组。
它们用于跨多个支持索引存储仅追加的时间序列数据。数据流支持索引通常默认是隐藏的。

> 数据流设计用于很少甚至从不更新现有数据的用例。您不能直接向数据流发送更新或删除现有文档的请求。相反，请使用按查询更新和按查询删除 API。
> 如果需要，您可以通过直接向文档的支持索引提交请求来更新或删除文档。
> 如果您经常更新或删除现有的时间序列数据，请使用带有写索引的索引别名，而不是数据流。请参阅 [Manage time series data without data streams](https://www.elastic.co/guide/en/elasticsearch/reference/8.8/getting-started-index-lifecycle-management.html#manage-time-series-data-without-data-streams)。

### 查看默认的 Filebeat 数据流

默认情况下，如果没有对 Filebeat 将写入的数据流进行任何自定义更改，它会将收集到的任何事件数据写入默认的数据流，如 `filebeat-8.8.1`。

在 Kibana 界面中，可以通过 Stack Management > Data > Index Management > Data Streams 查看默认的 Filebeat 数据流。

![Data Streams](https://kifarunix.com/wp-content/uploads/2023/06/data-streams.png?v=1687897152&ezimgfmt=ng:webp/ngcb3)

如果您想查看数据流索引，请在 Index Management 下点击 Indices，并打开 Include hidden indices 选项：

![Index Management](https://kifarunix.com/wp-content/uploads/2023/06/data-stream-indices.png?v=1687900388&ezimgfmt=ng:webp/ngcb3)

正如前面提到的，数据流是通过索引模板创建的。索引模板定义了 Elasticsearch 在创建索引时如何配置索引。例如，`filebeat-8.8.1` 索引是由索引模板 `Filebeat-8.8.1` 创建的。您可以在索引模板部分找到索引模板。

可以在 Kibana 界面中，打开 Management > DevTools > Console，然后运行如下命令查看默认的 Filebeat 索引模板：

```bash
GET _index_template/filebeat-8.8.1
```

## 配置 Filebeat 发送 docker 日志到指定的 Data Stream

### 删除已存在的策略、模板和数据流

```bash
DELETE _data_stream/streamflow-*
DELETE _index_template/streamflow-*
DELETE _ilm/policy/streamflow-ilm-policy
```

### 创建 Index Lifecycle Management 策略

此步骤是可选的，但如果您想控制索引的生命周期任务，例如创建、删除、滚动到新阶段等，ILM 策略非常有用。您可以在 Kibana 的 Stack Management > Data > Index Lifecycle Policies 下管理 ILM 策略。

在 Kibana 界面中，打开 Management > DevTools > Console，然后运行如下 API 命令创建一个 ILM 策略：

```json
PUT _ilm/policy/streamflow-ilm-policy
{
  "policy": {
    "phases": {
      "hot": {
        "min_age": "0ms",
        "actions": {
          "rollover": {
            "max_age": "1d",
            "max_size": "20gb"
          }
        }
      },
      "cold": {
        "min_age": "7d",
        "actions": {
          "allocate": {
            "number_of_replicas": 0
          },
          "set_priority": {
            "priority": 0
          }
        }
      },
      "delete": {
        "min_age": "15d",
        "actions": {
          "delete": {
            "delete_searchable_snapshot": true
          }
        }
      }
    }
  }
}
```

这个 ILM 策略定义了以下生命周期：

1. **热阶段 (Hot)**：
   - 索引创建后立即进入此阶段
   - 当索引达到 20GB 或存在时间超过 1 天时，会自动滚动创建新索引
   - 此阶段索引可以频繁写入和查询

2. **冷阶段 (Cold)**：
   - 索引创建 7 天后进入此阶段
   - 移除所有副本以节省存储空间
   - 降低索引优先级
   - 适用于查询频率较低的旧数据

3. **删除阶段 (Delete)**：
   - 索引创建 15 天后进入此阶段
   - 自动删除索引及其快照
   - 用于清理过期数据，避免存储空间持续增长

### 创建 Index Template

索引模板（Index Template）是用于为特定索引定义特定设置的模板。索引模板可以包含在组件模板中定义的设置和映射，以及特定于索引的设置和映射。

在 Kibana 界面中，打开 Management > DevTools > Console，然后运行如下 API 命令创建一个索引模板：

```json
PUT _index_template/streamflow-1.2.0
{
  "index_patterns": ["streamflow-*"],
  "data_stream": {}, 
  "template": {
    "settings": {
      "index": {
        "lifecycle": {
          "name": "streamflow-ilm-policy",
          "rollover_alias": "streamflow-1.2.0-rollover"
        },
        "codec": "best_compression",
        "mapping": {
          "total_fields": {
            "limit": "12500"
          }
        },
        "refresh_interval": "10s",
        "number_of_shards": "5",
        "final_pipeline": "_none",
        "default_pipeline": "_none",
        "number_of_replicas": "1"
      }
    },
    "mappings": {
      "_source": {
        "enabled": true
      },
      "dynamic_templates": [
        {
          "timestamps": {
            "path_match": "@timestamp",
            "mapping": {
              "type": "date"
            }
          }
        },
        {
          "ip_addresses": {
            "path_match": "*.ip.addr",
            "mapping": {
              "type": "ip"
            }
          }
        },
        {
          "ports": {
            "path_match": "*.l4.port.id",
            "mapping": {
              "type": "integer"
            }
          }
        },
        {
          "flow_metrics": {
            "path_match": "flow.{bytes,byte_rate}",
            "mapping": {
              "type": "long"
            }
          }
        },
        {
          "durations": {
            "path_match": "flow.duration.seconds",
            "mapping": {
              "type": "float"
            }
          }
        },
        {
          "task_name": {
            "path_match": "task.name",
            "mapping": {
              "type": "keyword"
            }
          }
        },
        {
          "http_host": {
            "path_match": "http.request.host",
            "mapping": {
              "type": "keyword"
            }
          }
        },
        {
          "http_urls": {
            "path_match": "http.request.url",
            "mapping": {
              "type": "keyword",
              "index": false
            }
          }
        },
        {
          "http_status_code": {
            "path_match": "http.response.status_code",
            "mapping": {
              "type": "integer"
            }
          }
        },
        {
          "geo_fields": {
            "path_match": "*.geo.{country,region,city}.name",
            "mapping": {
              "type": "keyword"
            }
          }
        },
        {
          "service_fields": {
            "path_match": "*.ip.service.*",
            "mapping": {
              "type": "keyword"
            }
          }
        },
        {
          "export_fields": {
            "path_match": "flow.export.*",
            "mapping": {
              "type": "keyword"
            }
          }
        },
        {
          "metadata_fields": {
            "path_match": "@metadata.*",
            "mapping": {
              "type": "keyword"
            }
          }
        },
        {
          "ip_version": {
            "path_match": "ip.version.name",
            "mapping": {
              "type": "keyword"
            }
          }
        }
      ],
      "properties": {
        "stream": { "type": "keyword" }
      }
    }
  }
}
```

这个索引模板的主要配置说明如下：

1. **基本设置**:
   - `index_patterns`: 匹配 "streamflow-*" 的索引都会使用此模板
   - `data_stream`: 启用数据流功能

2. **索引设置**:
   - `lifecycle`: 使用名为 "streamflow-ilm-policy" 的 ILM 策略管理索引生命周期
   - `codec`: 使用最佳压缩以节省存储空间
   - `refresh_interval`: 每 10 秒刷新一次，平衡实时性和性能
   - `number_of_shards`: 5 个主分片，用于数据分布
   - `number_of_replicas`: 1 个副本，提供数据冗余

3. **字段映射**:
   - `timestamps`: 时间戳字段映射为 date 类型
   - `ip_addresses`: IP 地址字段映射为 ip 类型
   - `ports`: 端口字段映射为 integer 类型
   - `flow_metrics`: 流量指标映射为 long 类型
   - `durations`: 持续时间映射为 float 类型
   - `http_*`: HTTP 相关字段的特定映射
   - `geo_fields`: 地理位置信息映射为 keyword 类型
   - `service_fields`: 服务相关字段映射为 keyword 类型
   - `metadata_fields`: 元数据字段映射为 keyword 类型

4. **性能优化**:
   - 设置了字段总数限制为 12500
   - 禁用了默认和最终管道处理
   - 对于 URL 等大文本字段禁用了索引以节省空间

### 创建 Data Stream

Kibana 控制台执行以下 API 命令（Kibana > Management > DevTools > Console）：

```bash
PUT _data_stream/streamflow-1.2.0
```

**⚠️注意⚠️**：数据流的名称必须能够被刚刚创建的索引模板中的 index_patterns 匹配。

例如，刚才创建的索引模板中，index_patterns 为 `["streamflow-*"]`，所以数据流的名称必须为 `streamflow-*` 的形式；如果是 `streamflow`，则无法匹配。

### 修改 Filebeat 配置

Filebeat 的配置文件中，需要指定数据流的名称。

```yaml
output.elasticsearch:
  enabled: true
  hosts: ["https://***************:9200"]
  username: "elastic"
  password: "7eG7yR67m1rSR24VBW6X87oX"
  ssl.verification_mode: "none"
  index: "streamflow-1.2.0"

setup:
  template:
    enabled: true
    name: "streamflow-1.2.0"
    pattern: "streamflow-*"
```

### 运行 Filebeat

docker 运行

```bash
docker run -it --rm \
  --name=filebeat \
  --user=root \
  --network=host \
  --volume="$(pwd)/filebeat.docker.yaml:/usr/share/filebeat/filebeat.yml:ro" \
  --volume="/var/lib/docker/containers:/var/lib/docker/containers:ro" \
  --volume="/var/run/docker.sock:/var/run/docker.sock:ro" \
  --volume="registry:/usr/share/filebeat/data:rw" \
  docker.elastic.co/beats/filebeat:8.17.0 \
  filebeat -e --strict.perms=false
```

docker compose 运行

详见 `docker-compose.yaml` 文件。

Kubernetes 运行

获取 elasticsearch 的地址和用户名密码

```bash
ELASTICSEARCH_USERNAME="elastic"
ELASTICSEARCH_PASSWORD=$(kubectl -n ${NAMESPACE} get secret ${CLUSTER_NAME}-es-elastic-user -o go-template='{{.data.elastic | base64decode}}')

ELASTICSEARCH_CLUSTER_IP=$(kubectl -n ${NAMESPACE} get service ${CLUSTER_NAME}-es-http -o go-template='{{.spec.clusterIP}}')

printf "=======================================\nELASTICSEARCH_USERNAME:\t%s\nELASTICSEARCH_PASSWORD:\t%s\n=======================================\n" ${ELASTICSEARCH_USERNAME} ${ELASTICSEARCH_PASSWORD}
```

获取 `cluster_uuid`

```bash
ELASTICSEARCH_CLUSTER_UUID=$(curl -s -u "${ELASTICSEARCH_USERNAME}:${ELASTICSEARCH_PASSWORD}" -k "https://${ELASTICSEARCH_CLUSTER_IP}:9200/_cluster/state/blok?pretty" | jq -r '.cluster_uuid')
echo cluster_uuid: ${ELASTICSEARCH_CLUSTER_UUID}
```

准备 filebeat.yaml 配置文件

详见 `filebeat.kubernetes.yaml` 文件。

部署

```bash
kubectl apply -f filebeat.yaml
```

## 测试

```bash
message="-,-,-,-,-,240e:36f:9:a820:1d8a:db4a:6c4c:3bb7,53525,2409:871e:820c:300::ed,443,IPv6,互联网专线,ljcdn,中国,上海,,移动,200,10.002455,1500329,15007818,task-Pull-ST-ILL-RT-AAAA-Ex-Prov-js-01,fga1.market.xiaomi.com,https://fga1.market.xiaomi.com/download/AppStore/0c0b85cbf8e014f13b11de3c0b0925d4be9ddf953/com.xiaomi.youpin.apk"
echo -n "$message" | nc -4u -w1 *************** 9001
```
