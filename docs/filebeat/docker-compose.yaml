services:
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.17.0
    container_name: filebeat
    user: root
    restart: unless-stopped
    network_mode: host
    command: filebeat -e --strict.perms=false
    volumes:
      - ./filebeat.docker.yaml:/usr/share/filebeat/filebeat.yml:ro
      # - ./filebeat-data:/usr/share/filebeat/data:rw
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTIC_USER=elastic
      - ELASTIC_PASSWORD=7eG7yR67m1rSR24VBW6X87oX
      - ELASTIC_HOSTS=https://***************:9200
      - KIBANA_HOSTS=https://***************:8081
