filebeat.inputs:
# - type: container
#   stream: stdout
#   paths:
#     - '/tmp/docker/containers/*/*.log'
#   include_lines: ['^\033\[0;92m\[INF\]']
#   harvester_buffer_size: 32768
#   ignore_older: 5m
- type: udp
  max_message_size: 16KiB
  host: "0.0.0.0:9001"
  
processors:
  - dissect:
      tokenizer: "%{flow.export.region},%{flow.export.city},%{flow.export.branch},%{flow.export.server},%{flow.export.instance},%{flow.src.ip.addr},%{flow.src.l4.port.id},%{flow.dst.ip.addr},%{flow.dst.l4.port.id},%{ip.version.name},%{flow.dst.ip.service.type},%{flow.dst.ip.service.provider},%{flow.dst.geo.country.name},%{flow.dst.geo.region.name},%{flow.dst.geo.city.name},%{flow.dst.geo.isp},%{http.response.status_code},%{flow.duration.seconds},%{flow.byte_rate},%{flow.bytes_data},%{task.name},%{http.request.host},%{http.request.url}"
      field: "message"
      target_prefix: ""
      ignore_failure: true
      overwrite_keys: true

  - convert:
      fields:
        - {from: "flow.bytes_data", to: "flow.bytes_data", type: "long"}
        - {from: "flow.byte_rate", to: "flow.byte_rate", type: "long"}
        - {from: "flow.dst.ip.addr", to: "flow.dst.ip.addr.ip", type: "ip"}
        - {from: "flow.src.ip.addr", to: "flow.src.ip.addr.ip", type: "ip"}
        - {from: "flow.dst.l4.port.id", to: "flow.dst.l4.port.id", type: "integer"}
        - {from: "flow.src.l4.port.id", to: "flow.src.l4.port.id", type: "integer"}
        - {from: "flow.duration.seconds", to: "flow.duration.seconds", type: "float"}
        - {from: "http.response.status_code", to: "http.response.status_code", type: "integer"}
      ignore_missing: true
      fail_on_error: false

  - drop_fields:
      fields: ["message", "log", "input", "ecs", "agent", "host", "stream"]

output.console:
  enabled: false
  pretty: true

output.elasticsearch:
  enabled: true
  hosts: ["https://***************:9200"]
  username: "elastic"
  password: "7eG7yR67m1rSR24VBW6X87oX"
  ssl.verification_mode: "none"
  index: "streamflow-1.2.0"

setup:
  template:
    enabled: true
    name: "streamflow-1.2.0"
    pattern: "streamflow-*"
