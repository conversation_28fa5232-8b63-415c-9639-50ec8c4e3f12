# AList 网盘下载平台搭建

## 使用步骤

1. docker-compose 启动

   ```shell
   docker-compose up -d
   ```

2. 修改管理员密码

   ```shell
   docker exec -it alist ./alist admin set admin
   ```

3. 访问

   ```shell
   http://localhost:5244/
   ```

## 添加存储

### 中国移动云盘

> 云盘地址 <https://yun.139.com/，官方文档：https://alist.nn.ci/zh/guide/drivers/139.html>

1. admin 账号登录后，点击页面最下方的“管理”链接
2. 点击左方列表中的“`存储`”
3. 在右边，点击“`添加`”按钮
4. 驱动下拉菜单，选择“`中国移动云盘`”
5. “挂载路径”，填写“`/移动云盘`”
6. 获取鉴权信息以及根文件夹 ID
   1. 用 Safari 或者 Chrome 浏览器登录移动云盘，开启Debug跟踪模式
   2. 查找以下URL：`https://yun.139.com/orchestration/personalCloud/catalog/v1.0/getDisk`
   3. 在 Request Header 中，查找 `Authorization` 字段，复制 Basic 之后的内容，填入 `Authorization` 栏
   4. 在 Request Body 中，查找 catalogID 字段，复制内容，填入 `根文件夹 ID` 栏
7. 点击“`保存`”按钮
