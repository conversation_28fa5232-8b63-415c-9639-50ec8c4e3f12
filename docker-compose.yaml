services:
  x_downloader_template: &downloader-template
    platform: linux/amd64
    image: ak.gforce.cn:5543/library/stream-puller:1.2.6
    environment: &downloader-template-environment
      SP_REMOTE_CONFIG: true
      SP_MODE: file
      SP_DEBUG: true
      LOG_SERVER: ***************:9001
      LOCAL_CONF_SERVER: http://***************:8500,http://***************:8500,http://***************:8500
      REMOTE_CONF_SERVER: http://**************:8500
    command: /opt/sp_main.sh
    stdin_open: true
    tty: true
    restart: unless-stopped
    network_mode: host
    volumes:
      - /tmp/sp_state:/sp_state:rw
    cap_add:
      - NET_ADMIN
    deploy:
      replicas: 0

  downloader_v4:
    <<: *downloader-template
    environment:
      <<: *downloader-template-environment
      DNS_TYPE: A
    build:
      context: .
      dockerfile: Dockerfile
    deploy:
      replicas: 0

  downloader_v6:
    <<: *downloader-template
    environment:
      <<: *downloader-template-environment 
      DNS_TYPE: AAAA
    deploy:
      replicas: 1
