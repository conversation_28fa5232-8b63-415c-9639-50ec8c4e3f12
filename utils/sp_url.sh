#!/usr/bin/env bash

# shellcheck source=/dev/null

# TODO: 生成的 GeoIP 库中添加 category 字段
# TODO: CDN 类别待更新
# 定义关联数组用来存放当前启用的 CDN 类别
declare -A allowed_categories=(
  ["cmcdn"]="true"    # 移动自建 CDN
  ["alicdn"]="true"   # 阿里云 CDN
  ["applecdn"]="true" # Apple CDN
  ["bytecdn"]="true"  # 字节跳动 火山引擎 CDN
  ["ctcdn"]="true"    # 天翼云 CDN
  ["ksyun"]="true"    # 金山云 CDN
  ["tencent"]="true"  # 腾讯云 CDN
  ["hwcdn"]="true"    # 华为云 CDN
)

# 根据输入的 URLs 来获取 hosts, type, size 等信息
#
# 说明：
#    - 参数1: 输入文件路径, 格式为 JSON, 保存待处理 URL 以及 category 等信息，支持通配符 *.json
#    - 参数2: 输出文件路径, 格式为 JSON
# 调用示例：
#    sp_parseURLs "assets/*.json" "./configs/sp_url_new.json"
sp_parseURLs() {
  # Input JSON file
  intput_file=$1
  # Output JSON file
  output_file=$2

  # 声明空数组 category_array
  category_array=()

  # 遍历哈希数组
  for key in "${!allowed_categories[@]}"; do
    if [ "${allowed_categories[$key]}" == "true" ]; then
      category_array+=("$key")
    fi
  done

  # TODO: 类别筛选机制待更新, 需要从 URL 筛选切换为 DNS 筛选
  # 将 category_array 数组转换为 jq 可用的格式
  jq_filter="select("
  # 枚举 category_array 数组，构建 jq 筛选条件
  for category in "${category_array[@]}"; do
    jq_filter+=$(printf '.category==\"%s\" or ' "${category}")
  done
  jq_filter="${jq_filter% or }" # 移除最后的 " or "
  jq_filter="${jq_filter})"     # 添加反括号 " ) "

  # 使用 jq 进行筛选
  # shellcheck disable=SC2086
  filtered_results=$(jq -s -crM '.[] | '"${jq_filter}"' | {name: .name, category: .category, URLs: .URLs}' ${intput_file})

  # Initialize JSON array
  echo "[" >"${output_file}"

  first_record="true"

  # Read URLs from stdin
  while IFS= read -r record; do
    category=$(jq -crM '.category' <<<"$record")

    while IFS= read -r url; do
      # 判断字符串是否匹配 URL
      if [[ $url =~ (https?|ftp)://[-A-Za-z0-9+\&@#/%?=~_|!:,.\;]+[-A-Za-z0-9+\&@#/%=~_|] ]]; then
        echo -n "==> Parsing URL: ${url} ..."
      else
        echo -n "==> Parsing URL: ${url} ..."
        echo "[IGNORE]"
        continue
      fi

      # Extract host using awk
      host=$(echo "$url" | awk -F[/:] '{print $4}')

      # Extract file extension using awk
      extension=$(echo "$url" | awk -F. '{print $NF}' | awk -F[/?] '{print $1}')

      # Choose a random User-Agent, because some CDN server bans 'curl'
      ua="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

      # Get the file size and HTTP code using curl
      response=$(curl -sIL -A "$ua" "$url")
      size=$(echo "$response" | grep -i '^Content-Length' | tail -n 1 | awk '{print $2}' | tr -d '\r')
      http_code=$(echo "$response" | grep -i '^HTTP/' | tail -n 1 | awk '{print $2}')
      echo "[${http_code}]"

      # Write the result to the JSON file
      # If HTTP code is 200, write the result to the JSON file
      if [ "$http_code" == "200" ] && [ "$size" -gt 10485760 ]; then
        if [ "$first_record" = "false" ]; then
          echo "," >>"${output_file}"
        fi
        first_record="false"

        json_string=$(printf '  {\n')
        json_string+=$(printf '    "category": "%s",\n' "$category")
        json_string+=$(printf '    "host": "%s",\n' "$host")
        json_string+=$(printf '    "type": "%s",\n' "$extension")
        json_string+=$(printf '    "size": "%s",\n' "$size")
        json_string+=$(printf '    "url": "%s"\n' "$url")
        json_string+=$(printf '  }')

        printf "  %s" "$(jq -crM <<<"$json_string")" >>"${output_file}"
      fi
    done <<<"$(jq -crM '.URLs.[]' <<<"$record")"
  done <<<"${filtered_results}"

  # Close JSON array
  echo "" >>"${output_file}"
  echo "]" >>"${output_file}"

  # 转换并规范格式
  # jq -crM "." "${output_file}" >"${output_file}.new"
  # mv "${output_file}.new" "${output_file}"

  # 完成并打印提示
  echo "==> Parsing complete. Results saved to ${output_file}."
}

# 提取输入文件中的 RRs 记录，筛选合并后写入到输出文件
#
# 说明：
#    - 参数1: 输入文件路径, 格式为 JSON, 保存待处理 RRs 以及 category 等信息，支持通配符 *.json
#    - 参数2: 输出文件路径, 格式为 JSON
# 调用示例：
#    sp_parseDNS "assets/*.json" "./configs/sp_dns_new.json"
# TODO: 由于 assets 文件更新, 此代码已失效, 待删除
sp_parseDNS() {
  # Input JSON file
  intput_file=$1
  # Output JSON file
  output_file=$2

  dns_rr=""
  for asset_file in ${intput_file}; do
    # 读取 name 字段作为 A 以及 AAAA 记录的 key, 并作为 CNAME 记录解析结果
    name=$(jq -crM '.name' "${asset_file}")
    # 读取 category 字段, 备用
    category=$(jq -crM '.category' "${asset_file}")
    # 逐条读取 domains 字段, 全部设置为 CNAME 记录，指向 name
    cname_rr=""
    for domain in $(jq -crM '.domains.[]' "${asset_file}"); do
      cname_rr+=$(jq -crnM "{\"${domain}\": [ {type: \"CNAME\", data: \"${name}\"}]}")
    done
    # 合并 CNAME 记录为 JSON, 等同于以下 add 方法, 但是不用将所有内容加载到内存, 更为高效
    # dns_rr+=$(jq -s 'add' <<<"${cname_rr}")
    dns_rr+=$(jq -n 'reduce inputs as $line ({}; .+$line)' <<<"${cname_rr}")
    # 读取 RRs 记录
    rr=$(jq -crnM "{\"${name}\": $(jq -crM '.RRs' "${asset_file}")}")
    dns_rr+=$(jq -n 'reduce inputs as $line ({}; .+$line)' <<<"${rr}")
  done
  jq -nM 'reduce inputs as $line ({}; .+$line)' <<<"${dns_rr}" >"${output_file}"
  return
}

# sp_parseURLs "assets/ctcdn_huya.json" "configs/sp_url_new.json"
# sp_parseDNS "assets/*.json" "configs/sp_dns_new.json"
# sp_parseDNS "assets/ctcdn_*.json" "configs/sp_dns_new.json"
