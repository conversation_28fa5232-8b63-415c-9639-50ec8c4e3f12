#!/usr/bin/env bash

# 检查参数
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <input IP file path> <URL>"
    exit 1
fi

INPUT_IP_FILE="$1"
URL="$2"
MAX_TIME=10

# 检查输入文件是否存在
if [ ! -f "$INPUT_IP_FILE" ]; then
    echo "Error: Input file '$INPUT_IP_FILE' does not exist"
    exit 1
fi

# 创建临时文件
TEMP_FILE=$(mktemp)

# 创建 curl 格式文件
cat > "$TEMP_FILE" <<EOF
    time_namelookup:  %{time_namelookup}\n
    time_connect:  %{time_connect}\n
    time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
    time_redirect:  %{time_redirect}\n
    time_starttransfer:  %{time_starttransfer}\n
    time_total:  %{time_total}\n
    speed_download:  %{speed_download}\n
    size_request:  %{size_request}\n
    size_download:  %{size_download}\n
    size_header:  %{size_header}\n
    http_code:  %{http_code}\n
EOF

# 输出 CSV 表头
echo "Server IP,HTTP Code,Connection Time (s),Start Transfer Time (s),Total Time (s),Download Speed (bytes/s),Received Bytes,Domain,URL"

# 读取输入 IP 文件并处理每一行
while IFS= read -r SERVER_IP || [ -n "$SERVER_IP" ]; do
    # 跳过空行
    [ -z "$SERVER_IP" ] && continue

    # 获取URL的协议和端口
    SCHEME=$(echo "$URL" | awk -F[/:] '{print $1}')
    PORT=$(echo "$URL" | awk -F: '{print $3}' | awk -F/ '{print $1}')
    if [ -z "$PORT" ]; then
        if [ "$SCHEME" = "https" ]; then
            PORT=443
        else
            PORT=80
        fi
    fi
    DOMAIN=$(echo "$URL" | awk -F[/:] '{print $4}')

    curl -w "@$TEMP_FILE" -o /dev/null -s "$URL" --resolve "$DOMAIN:$PORT:$SERVER_IP" -H "Referer: $SCHEME://$DOMAIN" --max-time $MAX_TIME  > "$TEMP_FILE.out"

    # 提取结果
    TIME_TOTAL=$(grep "time_total:" "$TEMP_FILE.out" | awk '{print $2}')
    SPEED_DOWNLOAD=$(grep "speed_download:" "$TEMP_FILE.out" | awk '{print $2}')
    TIME_CONNECT=$(grep "time_connect:" "$TEMP_FILE.out" | awk '{print $2}')
    TIME_STARTTRANSFER=$(grep "time_starttransfer:" "$TEMP_FILE.out" | awk '{print $2}')
    SIZE_HEADER=$(grep "size_header:" "$TEMP_FILE.out" | awk '{print $2}')
    SIZE_DOWNLOAD=$(grep "size_download:" "$TEMP_FILE.out" | awk '{print $2}')
    TOTAL_DOWNLOAD=$((SIZE_DOWNLOAD + SIZE_HEADER))
    HTTP_CODE=$(grep "http_code:" "$TEMP_FILE.out" | awk '{print $2}')

    # 输出结果
    echo "$SERVER_IP,$HTTP_CODE,$TIME_CONNECT,$TIME_STARTTRANSFER,$TIME_TOTAL,$SPEED_DOWNLOAD,$TOTAL_DOWNLOAD,$DOMAIN,$URL"

done < "$INPUT_IP_FILE"

# 删除临时文件
rm "$TEMP_FILE" "$TEMP_FILE.out"
