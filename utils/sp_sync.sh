#!/usr/bin/env bash

# shellcheck source=/dev/null

# 公共变量及函数定义
source sp_common.sh

# 将远程服务器上的任务文件下载到本地
#
# 说明：
#    - 参数1: 任务名称
#    - 参数2: 本地任务文件路径
# 调用示例：
#    sp_pullTask "http://**************:8500" "task1" "./tasks" "./configs"
sp_pullTask() {
  local remote_servers="$1"
  local task_name="$2"
  local conf_dir="$3"
  local etag_file="/tmp/.${task_name}_etag"

  IFS=',' read -ra server_array <<< "$remote_servers"
  
  # 判断是否是 docker 环境，如果不是 docker 环境，则直接返回, 避免覆盖源码
  # if [ ! -e /.dockerenv ]; then
  #   sp_log "DEBUG" "  -> Abort: can only run in a docker container environment."
  #   return 1
  # fi

  # 创建临时目录
  TEMP_DIR=$(mktemp -d)

  # 检查文件是否有更新
  local if_none_match=""
  if [ -f "${etag_file}" ]; then
    if_none_match=$(cat "${etag_file}")
  fi

  # 遍历所有服务器直到成功
  for remote_server in "${server_array[@]}"; do
    sp_log "DEBUG" "==> Attempting to download task: ${task_name} from server: ${remote_server}..."

    # 构建远程文件 URL
    URL="${remote_server}/tasks/${task_name}.zip"
    
    # 先检查文件是否有更新
    sp_log "DEBUG" "  -> Checking if task needs update..."
    local response
    local http_code
    local etag
    
    if [ -n "${if_none_match}" ]; then
      response=$(curl -sI --connect-timeout 5 -H "If-None-Match: ${if_none_match}" "${URL}")
      http_code=$(echo "$response" | grep -i '^HTTP/' | awk '{print $2}')
      
      if [ "${http_code}" = "304" ]; then
        sp_log "DEBUG" "  -> Task is up to date, skipping download"
        rm -rf "$TEMP_DIR"
        return 0
      fi
    fi

    # 如果文件已更新或首次下载，则获取新文件
    sp_log "DEBUG" "  -> Downloading from ${URL}... "
    response=$(curl -s -D - --connect-timeout 5 -o "${TEMP_DIR}/${task_name}.zip" "${URL}")
    http_code=$(echo "$response" | grep -i '^HTTP/' | awk '{print $2}')
    
    # 如果连接超时或失败，尝试下一个服务器
    if [ -z "$http_code" ] || [ "$http_code" = "000" ]; then
      sp_log "DEBUG" "      > Connection timeout, trying next server..."
      continue
    fi
    
    # 如果其他错误，也尝试下一个服务器
    if [ "${http_code}" -ne 200 ]; then
      sp_log "DEBUG" "      > Failed with code: ${http_code}, trying next server..."
      continue
    fi

    sp_log "DEBUG" "      > Download completed successfully"
    
    # 获取新的 ETag
    etag=$(echo "$response" | grep -i '^ETag:' | awk '{print $2}' | tr -d '\r')

    # 保存新的 ETag
    if [ -n "${etag}" ]; then
      echo "${etag}" > "${etag_file}"
    fi

    export UNZIP_DISABLE_ZIPBOMB_DETECTION=TRUE
    # 解压缩文件并通过管道将输出传递给 sp_log
    sp_log "DEBUG" "  -> Extracting files..."
    unzip "${TEMP_DIR}/${task_name}.zip" -d "$TEMP_DIR" 2>&1 | while IFS= read -r line; do
      sp_log "DEBUG" "      > $line"
    done

    \rm -rf "${TEMP_DIR}/${task_name}.zip" >/dev/null

    # 打印临时目录位置
    sp_log "DEBUG" "  -> Files have been downloaded and extracted to: $TEMP_DIR"
    i=0
    find "${TEMP_DIR}" -type f | while IFS= read -r file; do
      ((i++))
      sp_log "DEBUG" "$(printf "      %02d  %9s" $i "$(basename "${file}")")"
    done

    # 检查文件校验码
    sp_log "DEBUG" "  -> Verifying file integrity..."
    sp_verify_local "${TEMP_DIR}"
    status=$?

    if [ ${status} -ne 0 ]; then
      sp_log "DEBUG" "  -> Verifying file integrity failed, trying next server..."
      continue
    fi

    # 覆盖配置目录中的文件, \mv 是避免别名干扰的方式
    sp_log "DEBUG" "  -> Moving files to ${conf_dir}... "
    \mv -f "$TEMP_DIR/"* "${conf_dir}"
    sp_log "DEBUG" "      > Files moved successfully"
    return 0
  done

  # 如果所有服务器都失败了
  sp_log "DEBUG" "  -> All servers failed"
  rm -rf "$TEMP_DIR"
  return 1
}

# 将远程服务器上的配置文件打包压缩后同步到本地集群 dufs (提前压缩是为了减轻 dufs 实时压缩的 CPU 压力)
#
# 说明：
#    - 参数1: 远程服务器地址
#    - 参数2: 本地集群服务器地址
# 调用示例：
#    sp_archiveConf "http://**************:8500" "http://***************:8500"
sp_archiveConf() {
  local remote_server="$1"
  local local_server="$2"

  # 获取当前目录的完整路径, 便于工作完成后返回
  current_dir=$(pwd)

  archive_file="configs.zip"

  # 下载远程服务器中的文件到临时目录
  sp_log "DEBUG" "==> Syncing config files from server: ${remote_server}..."

  # 先将压缩包保存至临时文件，上传完成后再更名
  tmp_archive=$(mktemp -u "${archive_file}".XXXXXX)

  # 下载打包后的文件
  TEMP_DIR=$(mktemp -d)
  URL="${remote_server}/configs?zip"
  sp_log "DEBUG" "  -> Downloading from ${URL} ... "
  http_code=$(curl -s -w "%{http_code}" -o "${TEMP_DIR}/${tmp_archive}" "${URL}")

  if [ "${http_code}" -ne 200 ]; then
    sp_log "DEBUG" "    > Download FAILED, HTTP CODE: ${http_code}"
    \rm -rf "${TEMP_DIR}" > /dev/null
    return 1
  else
    sp_log "DEBUG" "    > Download completed successfully"
  fi

  # 上传压缩包
  sp_log "DEBUG" "==> Uploading file ${archive_file} to ${local_server}... "
  sp_log "DEBUG" "  -> Uploading to temp path: ${tmp_archive}: "
  curl -s -T "${TEMP_DIR}/${tmp_archive}" "${local_server}/${tmp_archive}"
  # 如果 curl 命令执行失败，则给出错误代码
  status=$?
  if [ ! ${status} ]; then
    sp_log "DEBUG" '    > Upload failed, error code: '"${status}"
    \rm -rf "${TEMP_DIR}" > /dev/null
    return
  else
    sp_log "DEBUG" "    > Upload completed successfully"
  fi

  # 上传完毕后更名覆盖原文件
  sp_log "DEBUG" "  -> Moving file ${tmp_archive} to ${archive_file}: "
  curl -s -X MOVE "${local_server}/${tmp_archive}" -H "Destination: ${local_server}/${archive_file}"
  # 如果 curl 命令执行失败，则给出错误代码
  status=$?
  if [ ! ${status} ]; then
    sp_log "DEBUG" '    > Move failed, error code: '"${status}"
    \rm -rf "${TEMP_DIR}" > /dev/null
    return
  else
    sp_log "DEBUG" "    > Move completed successfully"
  fi

  # 返回之前的目录
  cd "${current_dir}" || return

  \rm -rf "${TEMP_DIR}" >/dev/null
  
  # 完成
  sp_log "DEBUG" "$(sp_done)"
}

# 从远程服务器下载所有任务包并同步到本地集群 dufs
#
# 说明：
#    - 参数1: 远程服务器地址
#    - 参数2: 本地集群服务器地址
# 调用示例：
#    sp_syncAllTasks "http://**************:8500" "http://***************:8500"
sp_syncAllTasks() {
  local remote_server="$1"
  local local_server_str="$2"

  # 将本地服务器地址字符串转换为数组
  IFS=',' read -ra local_server_array <<< "$local_server_str"

  # 遍历所有本地服务器
  for local_server in "${local_server_array[@]}"; do
    sp_log "INFO" "==> Syncing tasks to local server: ${local_server}"

    # 创建临时目录
    TEMP_DIR=$(mktemp -d)

    # 获取远程和本地任务列表的详细信息
    remote_tasks=$(curl -s "${remote_server}/tasks/?json" | jq '.paths[] | select(.name | endswith(".zip")) | {name: .name, mtime: .mtime}')
    local_tasks=$(curl -s "${local_server}/tasks/?json" | jq '.paths[] | select(.name | endswith(".zip")) | {name: .name, mtime: .mtime}')

    if [ -z "$remote_tasks" ]; then
      sp_log "INFO" "  -> No task files found on remote server"
      \rm -rf "${TEMP_DIR}" > /dev/null
      return 0
    fi

    # 将任务列表转换为关联数组
    declare -A remote_mtimes local_mtimes
    while IFS= read -r task; do
      name=$(echo "$task" | jq -r '.name')
      mtime=$(echo "$task" | jq -r '.mtime')
      remote_mtimes["$name"]=$mtime
    done < <(echo "$remote_tasks" | jq -c '.')

    while IFS= read -r task; do
      name=$(echo "$task" | jq -r '.name')
      mtime=$(echo "$task" | jq -r '.mtime')
      local_mtimes["$name"]=$mtime
    done < <(echo "$local_tasks" | jq -c '.')

    # 遍历远程任务，检查更新和新增
    for task_name in "${!remote_mtimes[@]}"; do
      remote_mtime="${remote_mtimes[$task_name]}"
      local_mtime="${local_mtimes[$task_name]:-0}"

      if [ "$remote_mtime" -gt "$local_mtime" ] || [ "$local_mtime" -eq 0 ]; then
        sp_log "INFO" "  -> Syncing updated task: ${task_name}"

        # 下载任务包
        URL="${remote_server}/tasks/${task_name}"
        sp_log "INFO" "    > Downloading from ${URL}... "
        http_code=$(curl -s -w "%{http_code}" -o "${TEMP_DIR}/${task_name}" "${URL}")

        if [ "${http_code}" -ne 200 ]; then
          sp_log "INFO" "    > Download FAILED, HTTP CODE: ${http_code}"
          continue
        else
          sp_log "INFO" "    > Download OK"
        fi

        # 上传到临时文件
        tmp_file="${task_name%.zip}_$(date +%s).zip"
        sp_log "INFO" "    > Uploading to ${local_server}/tasks/${task_name}... "
        http_code=$(curl -s -w "%{http_code}" -T "${TEMP_DIR}/${task_name}" "${local_server}/tasks/${tmp_file}")

        if [ "${http_code}" -lt 200 ] || [ "${http_code}" -ge 300 ]; then
          sp_log "INFO" "    > Upload FAILED, HTTP CODE: ${http_code}"
          continue
        fi

        # 移动到最终位置
        move_code=$(curl -s -w "%{http_code}" -X MOVE "${local_server}/tasks/${tmp_file}" \
                    -H "Destination: ${local_server}/tasks/${task_name}")

        if [ "${move_code}" -lt 200 ] || [ "${move_code}" -ge 300 ]; then
          sp_log "INFO" "    > Upload FAILED, MOVE HTTP CODE: ${move_code}"
          continue
        else
          sp_log "INFO" "    > Upload OK"
        fi

        sp_log "INFO" "    > Task ${task_name} synced successfully"
      else
        sp_log "INFO" "  -> Task ${task_name} is up to date, skipping"
      fi
    done

    # 删除远程不存在的任务
    for task_name in "${!local_mtimes[@]}"; do
      if [ -z "${remote_mtimes[$task_name]}" ]; then
        sp_log "INFO" "  -> Deleting task not present on remote server: ${task_name}"
        delete_code=$(curl -s -w "%{http_code}" -X DELETE "${local_server}/tasks/${task_name}")
        if [ "${delete_code}" -lt 200 ] || [ "${delete_code}" -ge 300 ]; then
          sp_log "INFO" "    > FAILED to delete ${task_name}, HTTP CODE: ${delete_code}"
        else
          sp_log "INFO" "    > Successfully deleted ${task_name}"
        fi
      fi
    done

    # 清理临时目录
    \rm -rf "${TEMP_DIR}" >/dev/null
  done

  sp_log "INFO" "==> All tasks synced successfully"
}


# 校验下载到本地的文件
#
# 说明：
#    - 参数1: 本地配置文件路径
# 调用示例：
#    sp_verify_local "./config"
sp_verify_local() {
  local conf_dir="$1"

  # 进入目录
  # 记录当前工作目录, 便于返回前切换回来
  current_dir=$(pwd)
  cd "${conf_dir}" || (
    sp_log "DEBUG" "Folder ${conf_dir} does not exist, quit"
    cd "${current_dir}" || true
    return 1
  )

  # 检查 checksum.md5 文件是否存在
  if [ ! -f "checksum.md5" ]; then
    sp_log "DEBUG" "File \"checksum.md5\" does not exist, quit"
    cd "${current_dir}" || true
    return 2
  fi

  code=0

  # 逐行读取文件
  while IFS= read -r line; do
    # 分别读取校验码和文件名
    checksum=$(echo "$line" | awk '{print $1}')
    filename=$(echo "$line" | awk '{print $2}')

    # 根据文件名判断文件是否存在
    if [ ! -f "${filename}" ]; then
      sp_log "DEBUG" "      > ${filename}: MISSING"
      code=3
      continue
    fi

    # 计算并比较 md5 校验码
    md5sum=$(md5sum "${filename}" | awk '{print $1}')
    if [ "${checksum}" != "${md5sum}" ]; then
      sp_log "DEBUG" "      > ${filename}: FAILED"
      code=4
    else
      sp_log "DEBUG" "      > ${filename}: OK"
    fi
  done <"checksum.md5"

  cd "${current_dir}" || true

  return "$code"
}

# 校验远程服务器上的文件
#
# 说明：
#    - 参数1: 远程服务器地址
# 调用示例：
#    sp_verify_remote "http://**************:8500"
sp_verify_remote() {
  local remote_server="$1"

  local url="${remote_server}/configs/checksum.md5"

  # Get HTTP code using curl
  response=$(curl -sI "$url")
  http_code=$(echo "$response" | grep -i '^HTTP/' | awk '{print $2}')
  # 检查 checksum.md5 文件是否存在
  if [ "$http_code" != "200" ]; then
    sp_log "DEBUG" "  -> File \"${url}\" does not exist, quit"
    return 1
  else
    sp_log "DEBUG" "  -> Checksum file found, checking... "
  fi

  checksum_content=$(curl -s "$url")

  code=0

  # 逐行读取文件
  while IFS= read -r line; do
    # 分别读取校验码和文件名
    checksum=$(echo "$line" | awk '{print $1}')
    filename=$(echo "$line" | awk '{print $2}')

    file_url="${remote_server}/configs/"$(basename "${filename}")

    # 根据文件名判断文件是否存在
    sp_log "DEBUG" "      > Checking ${filename} ... "
    response=$(curl -sI "$file_url")
    http_code=$(echo "$response" | grep -i '^HTTP/' | awk '{print $2}')
    # 检查文件是否存在
    if [ "$http_code" != "200" ]; then
      sp_log "DEBUG" "      > ${filename} is MISSING"
      code=3
      continue
    fi

    # 计算并比较 md5 校验码
    md5sum=$(curl -s "${file_url}" | md5sum | awk '{print $1}')
    if [ "${checksum}" != "${md5sum}" ]; then
      sp_log "DEBUG" "      > Checking MD5 of ${filename} has FAILED"
      code=4
    else
      sp_log "DEBUG" "      > Checking MD5 of ${filename} is OK"
    fi
  done <<<"$checksum_content"

  return "$code"
}

# 从远程服务器下载任务配置压缩包并解压到本地
#
# 说明：
#    - 参数1: 远程服务器地址
# 调用示例：
#    sp_pullTaskConfig "http://**************:8500" 
sp_pullTaskConfig() {
  local remote_servers="$1"
  local task_config_archive="task-Config.zip"
  local task_config_file="task_config.json"
  local etag_file="/tmp/.task_config_etag"

  IFS=',' read -ra server_array <<< "$remote_servers"
  
  # 创建临时目录
  local TEMP_DIR
  TEMP_DIR=$(mktemp -d)
  
  # 检查文件是否有更新
  local if_none_match=""
  if [ -f "${etag_file}" ]; then
    if_none_match=$(cat "${etag_file}")
  fi

  # 遍历所有服务器直到成功
  for remote_server in "${server_array[@]}"; do
    sp_log "DEBUG" "==> Checking task config from server: ${remote_server}..."

    # 构建远程文件 URL
    local URL="${remote_server}/tasks/${task_config_archive}"
    
    # 先检查文件是否有更新
    sp_log "DEBUG" "  -> Checking if config needs update..."
    local response
    local http_code
    local etag
    
    if [ -n "${if_none_match}" ]; then
      response=$(curl -s --connect-timeout 5 -I -H "If-None-Match: ${if_none_match}" "${URL}")
      http_code=$(echo "$response" | grep -i '^HTTP/' | awk '{print $2}')
      
      if [ "${http_code}" = "304" ]; then
        sp_log "DEBUG" "  -> Config is up to date, skipping download"
        rm -rf "$TEMP_DIR"
        return 0
      fi
    fi

    # 如果文件已更新或首次下载，则获取新文件
    sp_log "DEBUG" "  -> Downloading task config..."
    response=$(curl -s --connect-timeout 5 -D - -o "${TEMP_DIR}/${task_config_archive}" "${URL}")
    http_code=$(echo "$response" | grep -i '^HTTP/' | awk '{print $2}')
    
    # 如果连接超时或失败，尝试下一个服务器
    if [ -z "$http_code" ] || [ "$http_code" = "000" ]; then
      sp_log "DEBUG" "      > Connection timeout, trying next server..."
      continue
    fi
    
    # 如果其他错误，也尝试下一个服务器
    if [ "${http_code}" -ne 200 ]; then
      sp_log "DEBUG" "      > Failed with code: ${http_code}, trying next server..."
      continue
    fi
    
    # 获取新的 ETag
    etag=$(echo "$response" | grep -i '^ETag:' | awk '{print $2}' | tr -d '\r')

    # 保存新的 ETag
    if [ -n "${etag}" ]; then
      echo "${etag}" > "${etag_file}"
    fi

    # 解压缩文件并通过管道将输出传递给 sp_log
    sp_log "DEBUG" "  -> Extracting task config..."
    export UNZIP_DISABLE_ZIPBOMB_DETECTION=TRUE
    unzip "${TEMP_DIR}/${task_config_archive}" -d "$TEMP_DIR" 2>&1 | while IFS= read -r line; do
      sp_log "DEBUG" "      > $line"
    done

    \rm -rf "${TEMP_DIR:?}/${task_config_archive}" >/dev/null

    # 检查文件校验码
    sp_log "DEBUG" "  -> Verifying file integrity..."
    sp_verify_local "${TEMP_DIR}"
    status=$?

    if [ ${status} -ne 0 ]; then
      sp_log "DEBUG" "  -> Verifying file integrity failed, trying next server..."
      continue
    fi

    # 成功获取配置，继续处理
    break
  done

  # 如果所有服务器都失败了
  if [ ! -f "${TEMP_DIR}/${task_config_file}" ]; then
    sp_log "DEBUG" "  -> All servers failed"
    rm -rf "$TEMP_DIR"
    return 1
  fi

  # 读取 task_config.json 文件
  task_config_file="${TEMP_DIR}/${task_config_file}"
  # 获取本地局域网 IP, 从配置文件中提取出需要的配置
  lan_ip=$(sp_get_local_lan_ip)
  # 检查 lan_ip 是否有效
  if [ -z "${lan_ip// }" ]; then
    sp_log "DEBUG" "  -> Failed to get local LAN IP"
    rm -rf "$TEMP_DIR"
    return 2
  fi

  # 定义需要读取的参数数组
  declare -a params=(
    "task_name"
    "connect_timeout"
    "download_times" 
    "limit_rate"
    "max_retries"
    "max_time"
    "replica"
  )

  # 根据 DNS_TYPE 判断访问 v4 还是 v6 配置
  if [ "${DNS_TYPE}" = "AAAA" ]; then
    # 检查是否存在 v6 配置
    if ! jq -e ".[\"$lan_ip\"].v6" "$task_config_file" >/dev/null; then
      sp_log "DEBUG" "  -> No IPv6 config found for IP: $lan_ip"
      # 尝试获取默认配置
      if ! jq -e ".[\"_DEFAULT_\"].v6" "$task_config_file" >/dev/null; then
        sp_log "DEBUG" "  -> No default IPv6 config found"
        rm -rf "$TEMP_DIR" 
        return 3
      fi
      sp_log "DEBUG" "  -> Using default IPv6 config"
      lan_ip="_DEFAULT_"
    fi
    
    # 读取所有参数
    for param in "${params[@]}"; do
      declare "$param"="$(jq -r ".[\"$lan_ip\"].v6.$param" "$task_config_file")"
    done
  else
    # 检查是否存在 v4 配置
    if ! jq -e ".[\"$lan_ip\"].v4" "$task_config_file" >/dev/null; then
      sp_log "DEBUG" "  -> No IPv4 config found for IP: $lan_ip"
      # 尝试获取默认配置
      if ! jq -e ".[\"_DEFAULT_\"].v4" "$task_config_file" >/dev/null; then
        sp_log "DEBUG" "  -> No default IPv4 config found"
        rm -rf "$TEMP_DIR" 
        return 3
      fi
      sp_log "DEBUG" "  -> Using default IPv4 config"
      lan_ip="_DEFAULT_"
    fi

    # 读取所有参数
    for param in "${params[@]}"; do
      declare "$param"="$(jq -r ".[\"$lan_ip\"].v4.$param" "$task_config_file")"
    done
  fi

  # 检查所有参数是否为空或 null
  sp_log "DEBUG" "  -> Checking parameters..."
  for param in "${params[@]}"; do
    param_value="${!param}"
    if [ -z "${param_value}" ] || [ "${param_value}" = "null" ]; then
      sp_log "DEBUG" "  -> Failed to get $param for $lan_ip"
      rm -rf "$TEMP_DIR"
      return 4
    fi
    sp_log "DEBUG" "      > Got ${param^^}: ${param_value}"
    # 设置环境变量
    # 刷新环境变量到本地变量
    eval "${param^^}=${param_value}"
    export "${param^^}"
  done

  # 清理临时目录
  rm -rf "$TEMP_DIR"

  sp_log "DEBUG" "  -> Task config updated successfully"
  return 0
}
