#!/usr/bin/env bash

# shellcheck source=/dev/null

# 公共变量及函数定义
source sp_common.sh

dnsConfFile=${CONFIG_FDR}/sp_dns.json

get_domain_data() {
  local domain="$1"
  local file="$2"
  local result=""

  # 检查域名是否存在
  if ! grep -q "\"${domain}\":" "$file"; then
    echo ""
    return 1
  fi

  # 提取数据
  if grep -q "\"${domain}\":" "${file}"; then
    # 直接使用 jq 从原文件中提取，避免临时文件的复杂性
    result=$(jq -crM ".\"${domain}\"" "$file" 2>/dev/null || echo "")
  else
    result=""
  fi

  echo "$result"
  return 0
}

# 域名查找函数
#
# 说明：
#    - 返回结果为 JSON 格式，包括有 type, data, location 等字段
#    - 如果查找不到结果，则返回空字符串
# 参数说明：
#    - $1: 查询的域名（必选）
#    - $2: 查询的类型（可选）
#      - A: IPv4 记录
#      - AAAA: IPv6 记录
#      - 缺省: 不筛选类型（IPv4 或者 IPv6）
# 调用示例：
#    sp_lookup "d.musicapp.migu.cn" A
# 返回示例：
#    {"type":"A","data":"**************","location":"中国广东广州 移动"}
sp_lookup() {
  # 获取待查域名
  domain=$1
  type=$2

  # 避免加载整个文件, 替代
  #   r=$(jq -crM '."'"${domain}"'"' "${dnsConfFile}")
  # 使用临时文件方法，处理大文件
  r=$(get_domain_data "${domain}" "${dnsConfFile}")
  # if grep -q "\"${domain}\":" "${dnsConfFile}"; then
  #     # 创建一个只包含目标域名的临时 JSON
  #     temp_json=$(mktemp)
  #     echo "{" > "$temp_json"
  #     sed -n "/\"${domain}\":/,/^\s*\],*$/p" "${dnsConfFile}" >> "$temp_json"
  #     echo "}" >> "$temp_json"
      
  #     r=$(jq -crM ".\"${domain}\"" "$temp_json" 2>/dev/null || echo "")
  #     rm -f "$temp_json"
  # else
  #     r=""
  # fi

  # 域名没有收录
  if [ "$r" = "null" ]; then
    echo "{}"
    return
  fi

  # 从配置文件中查找域名
  if [ "$type" ]; then
    r=$(jq -crM '. | map(select(.type=="CNAME" or .type=="'"${type}"'"))' <<<"$r")
  else
    r=$(jq -crM '. | map(select(.type=="CNAME" or .type=="A" or .type=="AAAA"))' <<<"$r")
  fi

  if [ -n "$r" ] && [ "$r" != "null" ]; then
    t=$(jq -crM '.[0].type' <<<"$r")

    if [ "$t" = "CNAME" ]; then
      # 如果是 CNAME 记录，则递归查询
      # 随机返回一个结果
      n=$(jq -r 'length' <<<"$r")

      random_number=$((RANDOM % n))
      domain=$(jq -crM '.['${random_number}'].data' <<<"$r")
      sp_lookup "${domain}" "${type}"
    else
      # 如果是 A 或者 AAAA 记录

      # 随机返回一个结果
      n=$(jq -r 'length' <<<"$r")

      if [ "$n" -gt 0 ]; then
        random_number=$((RANDOM % n))
        jq -crM '.['${random_number}']' <<<"$r"
      else
        # 域名有收录, 但都不满足筛选条件
        echo "{\"\"}"
      fi
    fi
  else
    echo "{}"
  fi
}
