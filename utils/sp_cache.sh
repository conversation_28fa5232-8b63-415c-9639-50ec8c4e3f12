#!/usr/bin/env bash

# shellcheck source=/dev/null

# Initialize the cache as an associative array
declare -A cache

# Function to set a key-value pair in the cache
set_cache() {
  local key="$1"
  local value="$2"
  cache["$key"]="$value"
}

# Function to get the value for a key from the cache
get_cache() {
  local key="$1"
  if [[ -v cache["$key"] ]]; then
    echo "${cache["$key"]}"
  else
    echo "Key not found"
  fi
}

# Function to delete a key-value pair from the cache
del_cache() {
  local key="$1"
  if [[ -v cache["$key"] ]]; then
    unset 'cache['"$key"']'
    echo "Key deleted"
  else
    echo "Key not found"
  fi
}

# Function to list all keys in the cache
list_cache() {
  for key in "${!cache[@]}"; do
    echo "$key"
  done
}

# Function to flush all contents in the cache
flush_cache() {
  for key in "${!cache[@]}"; do
    unset 'cache['"$key"']'
  done
}

# Function to implement memory cache
sp_cache() {
  # Main script logic to handle command-line arguments
  if [[ $# -lt 1 ]]; then
    usage
  fi

  command="$1"
  key="$2"
  value="$3"

  case "$command" in
  set)
    if [[ -z "$key" || -z "$value" ]]; then
      usage
    fi
    set_cache "$key" "$value"
    ;;
  get)
    if [[ -z "$key" ]]; then
      usage
    fi
    get_cache "$key"
    ;;
  del)
    if [[ -z "$key" ]]; then
      usage
    fi
    del_cache "$key"
    ;;
  list)
    list_cache
    ;;
  flush)
    flush_cache
    ;;
  *)
    usage
    ;;
  esac

}

# function usage instructions
usage() {
  echo "Usage: $0 {set|get|del|list|flush} [key] [value]"
  return 1
}
