#!/usr/bin/env bash

# shellcheck source=/dev/null

# 公共变量及函数定义
source sp_common.sh
source utils/sp_dns.sh

# 目录结构说明: provider_fdr / account_fdr / REMOTE_FDR / files...
#        举例: 移动云盘/**********/转存合集/...

loginTokenFile=${RESPONSE_FDR}/01_token.json
providerListFile=${RESPONSE_FDR}/02_provider_fdr_list.json
accountListFile=${RESPONSE_FDR}/03_account_fdr_list.json
fileListFile=${RESPONSE_FDR}/04_file_list.json
fileInfoFile=${RESPONSE_FDR}/05_file_info.json

remote_folder=""

# 初始化运行环境
sp_init() {
  echo -n "==> Initializing pan mode ..."
  # 登录之前，先删除之前运行产生的应答文件
  rm -rf "$RESPONSE_FDR" >/dev/null 2>&1
  # 重新创建应答文件目录
  mkdir "$RESPONSE_FDR" >/dev/null 2>&1
  echo " done"
}

# 登录并写入 token 报文
sp_login() {
  local retries=0

  while [ $retries -lt "$MAX_RETRIES" ]; do
    # 登录，获取 token 等信息
    echo -n "==> Login ..."
    curl "${ALIST_SERVER}/api/auth/login?Username=${ALIST_USERNAME}&Password=${ALIST_PASSWORD}" \
      -s \
      -X POST \
      -H "content-type: application/x-www-form-urlencoded" \
      -H "Accept: */*" \
      -H "Content-Length: 0" \
      -H "Connection: keep-alive" >"${loginTokenFile}"

    # 如果 curl 命令执行失败，则重试
    if [ ! $? ]; then
      echo ' failed, server unreachable, retrying...'
    else
      local json_response
      json_response=$(cat "$loginTokenFile")
      # 如果应答报文内容不是标准 JSON 格式, 判断执行失败, 重试
      if validate_json "$json_response"; then
        local code
        code=$(jq -r .code <<<"$json_response")
        # 如果应答报文中的 code 字段不为 200, 则判断执行失败, 重试
        if [ "${code}" -eq 200 ]; then
          echo " done"
          # 输出获取结果
          #print_resp $loginTokenFile '.data.token'
          return 0
        else
          echo ' failed, code '"$code"', retrying...'
        fi
      else
        echo ' failed, invalid response, retrying...'
      fi
    fi

    retries=$((retries + 1))
    if [ $retries -eq "$MAX_RETRIES" ]; then
      echo "==> Maximum retries reached. Exiting..."
      exit 1 # Exit with error
    fi

    sleep "${RETRY_INTERVAL}"
  done

  echo "==> Login failed after $MAX_RETRIES attempts"
  exit 1
}

# 获取 provider 目录列表
sp_get_provider_folders() {
  token=$(jq -r '.data.token' "${loginTokenFile}")

  echo -n "==> Getting provider folder list ..."
  curl "${ALIST_SERVER}/api/fs/list" \
    -s \
    -X POST \
    -H "content-type: application/json" \
    -H "Accept: */*" \
    -H "Connection: keep-alive" \
    -H "Authorization: ${token}" \
    --data-raw '{"page":1,"per_page": 99,"refresh": false,"path": "/"}' >"${providerListFile}"

  # 如果 curl 命令执行失败，则退出
  if [ ! $? ]; then
    echo ' failed.'
    exit 1
  else
    echo " done"
  fi

  # 输出获取结果
  print_resp "${providerListFile}" '.data.content[].name'

  # 验证 provider 信息
  totalProviders=$(jq -r '.data.content | length' "${providerListFile}")
  for ((i = 0; i < ${totalProviders}; i++)); do
    provider=$(jq -r ".data.content[$i].name" "$providerListFile")
    if [ "$provider" = "$ALIST_PROVIDER" ]; then
      echo "==> Select provider: ${ALIST_PROVIDER}"
      return 0
    fi
  done

  echo "==> Provider ${ALIST_PROVIDER} not found"
  exit 1
}

# 获取 account 目录列表
sp_get_account_folders() {
  token=$(jq -r '.data.token' "${loginTokenFile}")

  echo -n "==> Getting account folder list ..."
  curl "${ALIST_SERVER}/api/fs/list" \
    -s \
    -X POST \
    -H "content-type: application/json" \
    -H "Accept: */*" \
    -H "Connection: keep-alive" \
    -H "Authorization: ${token}" \
    --data-raw '{"page":1,"per_page": 99,"refresh": false,"path": "/'"${ALIST_PROVIDER}"'"}' >"${accountListFile}"

  # 如果 curl 命令执行失败，则退出
  if [ ! $? ]; then
    echo ' failed.'
    exit 1
  else
    echo " done"
  fi

  # 输出获取结果
  print_resp "${accountListFile}" '.data.content[].name'
}

# 获取文件列表
sp_get_files() {
  token=$(jq -r '.data.token' "$loginTokenFile")

  # 文件列表文件名前缀
  listfile_prefix=${RESPONSE_FDR}/$(basename "${fileListFile}" .json)

  # 从账号列表中，逐一读取每个账号
  total_accounts=$(jq -r '.data.content | length' "${accountListFile}")
  for ((account_index = 0; account_index < total_accounts; account_index++)); do
    # 目录名即是用户名
    account_name=$(jq -r ".data.content[${account_index}].name" "${accountListFile}")
    echo "==> Account: ${account_name}... "

    # 目标文件所在的完整目录
    remote_folder="/${ALIST_PROVIDER}/${account_name}/${REMOTE_FDR}"
    # 账号文件列表临时文件
    account_file_list_file="${listfile_prefix}_${account_name}.json"

    # 读取每个账号下的文件列表
    echo -n "    -> Getting file list... "

    curl "${ALIST_SERVER}/api/fs/list" \
      -s \
      -X POST \
      -H "content-type: application/json" \
      -H "Accept: */*" \
      -H "Connection: keep-alive" \
      -H "Authorization: ${token}" \
      --data-raw '{"page":1,"per_page": 99,"refresh": false,"path": "'"${remote_folder}"'"}' >"${account_file_list_file}"

    # 如果 curl 命令执行失败，则切换到下一个账号
    if [ ! $? ]; then
      echo 'failed, server unreachable'
      continue
    else
      local json_response
      json_response=$(cat "${account_file_list_file}")
      # 如果应答报文内容不是标准 JSON 格式, 判断执行失败, 重试
      if validate_json "$json_response"; then
        local code
        code=$(jq -r .code <<<"$json_response")
        # 如果应答报文中的 code 字段不为 200, 则判断执行失败, 重试
        if [ "${code}" -eq 200 ]; then
          echo "done"
          # 输出获取结果
          #print_resp $loginTokenFile '.data.token'
        else
          echo 'failed, code '"$code"''
          rm -f "${account_file_list_file}" >/dev/null
          continue
        fi
      else
        echo 'failed, invalid response'
        rm -f "${account_file_list_file}" >/dev/null
        continue
      fi
    fi

    # 逐一获取每个文件的详细信息
    echo "    -> Getting file info... "

    # 文件信息文件名前缀
    infofile_prefix=${RESPONSE_FDR}/$(basename "${fileInfoFile}" .json)
    # 账号文件列表临时文件
    account_file_info_file="${infofile_prefix}_${account_name}.json"
    # 账号文件列表临时文件前缀
    account_file_info_prefix=${RESPONSE_FDR}/$(basename "${account_file_info_file}" .json)

    file_index=1
    jq -crM '.data.content[].name' "${account_file_list_file}" | while IFS=$'\n' read -r filename; do
      num=$file_index
      [ $num -lt 10 ] && num="0"$num

      # 目标文件所在的完整路径
      file_path="${remote_folder}/$filename"

      echo -n "    -> ${file_path}... "

      curl --location "${ALIST_SERVER}/api/fs/get" \
        -s \
        -X POST \
        -H "content-type: application/json" \
        -H "Accept: */*" \
        -H "Connection: keep-alive" \
        -H "Authorization: ${token}" \
        --data-raw '{"path": "'"${file_path}"'"}' >"${account_file_info_prefix}_${num}.json"

      # 如果 curl 命令执行失败，则切换至下一个文件
      # 如果 curl 命令执行失败，则切换到下一个账号
      if [ ! $? ]; then
        echo 'failed, server unreachable'
        continue
      else
        local json_response
        json_response=$(cat "${account_file_info_prefix}_${num}.json")
        # 如果应答报文内容不是标准 JSON 格式, 判断执行失败, 重试
        if validate_json "$json_response"; then
          local code
          code=$(jq -r .code <<<"$json_response")
          # 如果应答报文中的 code 字段不为 200, 则判断执行失败, 重试
          if [ "${code}" -eq 200 ]; then
            echo "done"
            # 输出获取结果
            #print_resp $loginTokenFile '.data.token'
          else
            echo 'failed, code '"$code"''
            rm -f "${account_file_info_prefix}_${num}.json" >/dev/null
            continue
          fi
        else
          echo 'failed, invalid response'
          rm -f "${account_file_info_prefix}_${num}.json" >/dev/null
          continue
        fi
      fi

      file_index=$((file_index + 1))
    done

    # 合并输出的结果
    jq '.data' "${account_file_info_prefix}"_*.json | jq -s '.[]' >"${account_file_info_file}"
    # 删除临时文件
    rm -f "${account_file_info_prefix}"_*.json >/dev/null 2>&1
  done

  # 合并输出的结果
  jq '.data.content.[]' "${listfile_prefix}"_*.json | jq -s '.' >"${fileListFile}"
  # 删除临时文件
  rm -f "${listfile_prefix}"_*.json >/dev/null 2>&1

  # 合并输出的结果
  jq '.' "${infofile_prefix}"_*.json | jq -s '.' >"${fileInfoFile}"
  # 删除临时文件
  rm -f "${infofile_prefix}"_*.json >/dev/null 2>&1

  # 输出获取结果
  # echo "    -> File list:"
  # print_resp "$fileListFile" '.[].name'
}

# 根据输入的 URLs 来获取 hosts, type, size 等信息
#
# 说明：
#    - 参数1: 输出文件路径, 格式为 JSON
# 调用示例：
#    sp_parse
sp_parse() {
  # Output JSON file
  output_file="${CONFIG_FDR}/sp_pan.json"

  # associative array to store domains
  declare -A domains

  echo "==> Parsing file links ..."

  # Initialize JSON array
  echo "[" >"${output_file}"

  # Read URLs from JSON
  totalNum=$(jq -r '. | length' "${fileInfoFile}")

  for ((i = 0; i < totalNum; i++)); do
    file=$(jq -r ".[$i].name" "${fileInfoFile}")
    url=$(jq -r ".[$i].raw_url" "${fileInfoFile}")

    echo "    -> ${url}"

    # Extract host using awk
    host=$(echo "$url" | awk -F[/:] '{print $4}')
    domains["$host"]="1"

    # Extract file extension using awk
    extension=$(echo "$file" | awk -F. '{print $NF}' | awk -F[/?] '{print $1}')

    # 直接从 JSON 文件中读取文件大小
    size=$(jq -r ".[$i].size" "${fileInfoFile}")

    # Write the result to the JSON file
    if [ "$size" -gt 10485760 ]; then
      if [ "$first_record" = false ]; then
        echo "," >>"${output_file}"
      fi
      first_record=false
      {
        echo "  {"
        echo "    \"category\": \"cmcdn\","
        echo "    \"host\": \"$host\","
        echo "    \"file\": \"$file\","
        echo "    \"type\": \"$extension\","
        echo "    \"size\": \"$size\","
        echo "    \"url\": \"$url\""
        echo -n "  }"
      } >>"${output_file}"
    fi
  done

  # Close JSON array
  echo "" >>"${output_file}"
  echo "]" >>"${output_file}"

  echo "==> Parsing complete. Results saved to ${output_file}."

  echo "==> The involved domain names are as follows:"
  for domain in "${!domains[@]}"; do
    echo "    $domain"
  done | rev | sort | rev
}

# 输出完成信息
sp_done() {
  echo "==> done"
}

# 提取网盘 URL, 并将其保存到本地配置
sp_pan_extract() {
  # 初始化
  sp_init

  # 登录
  sp_login

  # 获取 provider 目录列表
  sp_get_provider_folders

  # 获取 account 目录列表
  sp_get_account_folders

  # 获取目标文件信息
  sp_get_files

  # 解析文件 URL
  sp_parse

  # 完成
  sp_done
}
