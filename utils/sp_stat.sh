#!/usr/bin/env bash

LOC_WHITELIST="移动"
LOC_BLACKLIST="江苏"

# 函数用于计算两个数相除并保留一位小数
percentage() {
  # 检查输入是否为空
  if [ -z "$1" ] || [ -z "$2" ]; then
    echo "null"
    return
  fi

  # 检查除数是否为零
  if [ "$2" -eq 0 ]; then
    echo "null"
    return
  fi

  # 计算除法结果并保留一位小数
  awk 'BEGIN { rounded = sprintf("%.1f", '"$1"'*100/'"$2"'); print rounded }'
  # result=$(echo "scale=1; $1 / $2" | bc)
  # echo "$result"
}

# 计算适当的填充宽度
padding_string() {
  local str="$1"
  local target_width="$2"

  # shellcheck disable=SC2155
  local bytes=$(wc -c <<<"$str")
  # shellcheck disable=SC2155
  local chars=$(wc -m <<<"$str")

  local chn_chars=$(((bytes - chars) / 2))
  local eng_chars=$((chars - chn_chars - 1))

  # echo "eng: $eng_chars  chn: $chn_chars"
  # echo $((eng_chars+2*chn_chars+1))

  # shellcheck disable=SC2155
  local display_len=$((eng_chars + chn_chars * 2))
  local padding=$((target_width - display_len))
  printf "%*s%s" $padding " " "$str"
}

# 获取所有 providerID name
#
# 说明：
#    - none
# 调用示例：
#    sp_getproviderIDs
sp_getProviderIDs() {
  jq -r '.[].[].providerID' configs/sp_dns.json | sort | uniq | grep -v "null"
}

# 获取所有 provider name
#
# 说明：
#    - none
# 调用示例：
#    sp_getProviderNames
sp_getProviderNames() {
  jq -r '.[].[].provider' configs/sp_dns.json | sort | uniq | grep -v "null"
}

# 获取所有 province name
#
# 说明：
#    - none
# 调用示例：
#    sp_getProvinceNames
sp_getProvinceNames() {
  while IFS= read -r name; do
    name=${name#中国}   # 去掉中国国家前缀
    name=${name%省*}   # 去掉 省XXXX 后缀
    name=${name%自治区*} # 去掉 自治区XXXX 后缀
    name=${name%市*}   # 去掉 市XXXX 后缀（直辖市）
    [ "$name" != "null" ] && echo "${name}"
  done <<<"$(jq -r '.[].[].location' configs/sp_dns.json | sort | uniq)" | sort | uniq
}

# 获取所有 serviceType name
#
# 说明：
#    - none
# 调用示例：
#    sp_getServiceTypeNames
sp_getServiceTypeNames() {
  jq -r '.[].[].serviceType' configs/sp_dns.json | sort | uniq | grep -v "null"
}

# 出具类别报表
#
# 说明：
#    - none
# 调用示例：
#    sp_getProviderIDStat
sp_getProviderIDStat() {
  # 打印表头
  echo ""
  echo "                       =============================="
  echo "                                  类别统计"
  echo "                       =============================="

  printf "%36s %6s %5s\n" "Name" "Count" "%"
  printf "%36s %6s %5s\n" "--------" "------" "-----"

  # 计算汇总条目数
  sum=$(jq -crM '.[].[] | select((.type=="A" or .type=="AAAA") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

  # 逐行打印 providerID 计数
  for providerID in $(sp_getProviderIDs); do
    count=$(jq -crM '.[].[] | select(.providerID=="'"${providerID}"'") | select((.type=="A" or .type=="AAAA") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

    printf "%36s %6s %5s\n" "$providerID" "$count" "$(percentage "$count" "$sum")"
  done

  # 打印 Sum
  printf "%36s %6s %5s\n" "--------" "------" "-----"
  printf "%36s %6s %5s\n" "SUM" "$sum" "100.0"
  printf "\n\n"
}

# 出具服务端类型报表
#
# 说明：
#    - none
# 调用示例：
#    sp_getProviderStat
sp_getProviderStat() {
  # 打印表头
  echo ""
  echo "                       =============================="
  echo "                                  CDN厂商统计"
  echo "                       =============================="

  printf "%36s %6s %5s\n" "Name" "Count" "%"
  printf "%36s %6s %5s\n" "--------" "------" "-----"

  # 计算汇总条目数
  sum=$(jq -crM '.[].[] | select((.type=="A" or .type=="AAAA") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

  # 逐行打印 serviceType 计数
  for providerName in $(sp_getProviderNames); do
    count=$(jq -crM '.[].[] | select(.provider=="'"${providerName}"'") | select((.type=="A" or .type=="AAAA") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

    printf "%36s %6s %5s\n" "$(padding_string "$providerName" 36)" "$count" "$(percentage "$count" "$sum")"
  done

  # 打印 Sum
  printf "%36s %6s %5s\n" "--------" "------" "-----"
  printf "%36s %6s %5s\n" "SUM" "$sum" "100.0"
  printf "\n\n"
}

# 出具地理信息报表
#
# 说明：
#    - none
# 调用示例：
#    sp_getGeolocationStat
sp_getGeolocationStat() {
  # 打印表头
  echo ""
  echo "                       =============================="
  echo "                                  地域统计"
  echo "                       =============================="

  printf "%36s %6s %5s\n" "Name" "Count" "%"
  printf "%36s %6s %5s\n" "--------" "------" "-----"

  # 计算汇总条目数
  sum=$(jq -crM '.[].[] | select((.type=="A" or .type=="AAAA") and (.location | contains("'${LOC_BLACKLIST}'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

  # 逐行打印 地域条目 计数
  for provinceName in $(sp_getProvinceNames); do
    count=$(jq -crM '.[].[] | select((.type=="A" or .type=="AAAA") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")) and (.location | contains("'"${provinceName}"'")))' configs/sp_dns.json | jq -s '.|length')

    printf "%36s %6s %5s\n" "$(padding_string "$provinceName" 36)" "$count" "$(percentage "$count" "$sum")"
  done

  # 打印 Sum
  printf "%36s %6s %5s\n" "--------" "------" "-----"
  printf "%36s %6s %5s\n" "SUM" "$sum" "100.0"
  printf "\n\n"
}

# 出具 IP 版本报表
#
# 说明：
#    - none
# 调用示例：
#    sp_getIPVersionStat
sp_getIPVersionStat() {
  # 打印表头
  echo ""
  echo "                       =============================="
  echo "                                 IPv4/v6统计"
  echo "                       =============================="

  printf "%36s %6s %5s\n" "Name" "Count" "%"
  printf "%36s %6s %5s\n" "--------" "------" "-----"

  # 计算汇总条目数
  sum=$(jq -crM '.[].[] | select((.type=="A" or .type=="AAAA") and (.location | contains("'${LOC_BLACKLIST}'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

  declare -A ip_ver_map=(
    ["A"]="IPv4"
    ["AAAA"]="IPv6"
  )

  # 逐行打印 IP Version 计数
  for type in "${!ip_ver_map[@]}"; do
    count=$(jq -crM '.[].[] | select((.type=="'"$type"'") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

    printf "%36s %6s %5s\n" "$(padding_string "${ip_ver_map[$type]}" 36)" "$count" "$(percentage "$count" "$sum")"
  done

  # 打印 Sum
  printf "%36s %6s %5s\n" "--------" "------" "-----"
  printf "%36s %6s %5s\n" "SUM" "$sum" "100.0"
  printf "\n\n"
}

# 出具服务端类型报表
#
# 说明：
#    - none
# 调用示例：
#    sp_serviceTypeStat
sp_serviceTypeStat() {
  # 打印表头
  echo ""
  echo "                       =============================="
  echo "                                 服务端类型统计"
  echo "                       =============================="

  printf "%36s %6s %5s\n" "Name" "Count" "%"
  printf "%36s %6s %5s\n" "--------" "------" "-----"

  # 计算汇总条目数
  sum=$(jq -crM '.[].[] | select((.type=="A" or .type=="AAAA") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

  # 逐行打印 serviceType 计数
  for serviceTypeName in $(sp_getServiceTypeNames); do
    count=$(jq -crM '.[].[] | select(.serviceType=="'"${serviceTypeName}"'") | select((.type=="A" or .type=="AAAA") and (.location | contains("'"${LOC_BLACKLIST}"'") | not) and (.location | contains("'"${LOC_WHITELIST}"'")))' configs/sp_dns.json | jq -s '.|length')

    printf "%36s %6s %5s\n" "$(padding_string "$serviceTypeName" 36)" "$count" "$(percentage "$count" "$sum")"
  done

  # 打印 Sum
  printf "%36s %6s %5s\n" "--------" "------" "-----"
  printf "%36s %6s %5s\n" "SUM" "$sum" "100.0"
  printf "\n\n"
}

# sp_getProvinceNames
sp_getProviderIDStat
sp_getProviderStat
sp_getGeolocationStat
sp_getIPVersionStat
sp_serviceTypeStat