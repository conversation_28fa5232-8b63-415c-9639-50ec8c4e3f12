#!/usr/bin/env bash

# shellcheck source=/dev/null

source utils/sp_cache.sh

# GeoIP 缓存数据库
GEOIP_DB="./configs/sp_geoip.json"

# IP 归属地查询函数
#
# 说明：
#    - 返回结果为 JSON 格式，包括有 ip, location 等字段
#    - 如果查找不到结果，则返回空字符串
# 调用示例：
#    sp_geoip "2409:8087:c00:12::1:2e"
# 返回示例：
#    {"code":0,"data":{"ip":{"query":"2409:8087:c00:12::1:2e"},"location":"中国山西省 中国移动通信集团有限公司政企客户分公司政企专线城域网"}}
sp_geoip() {
  # 获取待查 IP
  ip=$1

  # 访问 API, 查询 IP 归属
  local resp
  resp=$(
    curl \
      -s \
      -H "content-type: application/json" \
      -H "Accept: */*" \
      -H "Connection: keep-alive" \
      'https://ip.zxinc.org/api.php?type=json&ip='"${ip}"
  )

  # 如果 curl 命令执行失败，则重试
  if [ ! $? ]; then
    return
  fi

  # 读取应答报文中的 JSON 状态码字段
  local code
  code=$(echo "$resp" | jq -cMr '.code')

  # 如果状态码不为 0, 说明服务端异常
  if [ ! "$code" ]; then
    return
  fi

  # 返回内容解析 .data.location 字段
  location=$(echo "${resp}" | jq -cMr '.data.location')

  # 去除非标准字符
  location=$(echo -e "$location" | sed 's/\u3000/ /g' | tr -d '\t\n\r' | awk '{gsub(/[[:cntrl:]]/, ""); print}')

  # 以 JSON 格式输出结果
  printf '{"ip": "%s", "location": "%s"}\n' "$ip" "$location"
}

# 从文件中读取 IPv6 地址并查询
#
# 说明：
#    - 参数1: 获取存放 IP 的文件名, 每行一个 IP 地址
#    - 参数2: （可选）是否离线查询, "true" 表明只查询本地缓存数据库, 默认为在线查询
#    - 返回结果为 JSON 格式，包括有 ip, location 等字段
# 调用示例：
#    lookup_geoip_v6 "~/Downloads/ip_cidr.txt" 1
# 返回示例：
#    {"ip": "2409:8087:c00:12::1:f", "location": "中国山西省 移动"}
lookup_geoip_v6() {
  # 获取存放 IP 的文件名
  file=$1
  # 是否离线查询
  is_offline=$2

  # 逐行读取文件内容, 每行一个 IPv6 地址
  while IFS= read -r ip; do
    # 规范 IPv6 地址, 如果仅以一个 : 结尾, 则在后面追加一个 :
    [[ "$ip" =~ [^:]:$ ]] && ip+=":"
    # 扩展 IPv6 为完整格式
    expanded_addr=$(sipcalc "$ip" | grep "Expanded Address" | awk '{print$4}')
    # 获取 /64 的地址前缀
    prefix64="${expanded_addr:0:20}"

    # 查询内存缓存
    resp=$(sp_cache get "$prefix64")
    # 如果内存缓存中未找到, 则调用 sp_geoip 远程查找
    if [ "$resp" == "Key not found" ]; then
      if [ "${is_offline}" != "true" ]; then
        resp=$(sp_geoip "$ip")
        # 将找到的内容存入内存缓存
        sp_cache set "$prefix64" "$resp"
      else
        resp="{}"
      fi
    fi
    # 以 JSON 格式输出结果
    # FIXME: 注意这里 $resp 特意没有添加引号, 因为添加后, jq 命令会报错：
    #  jq: parse error: Invalid string: control characters from U+0000 through U+001F must be escaped at line 1, column 142
    location=$(echo $resp | jq -cMr '.location')
    printf '{"ip": "%s", "location": "%s"}\n' "$ip" "$location"
  done <"$file"
}

# 从文件中读取 IPv4 地址并查询
#
# 说明：
#    - 参数1: 获取存放 IP 的文件名, 每行一个 IP 地址
#    - 参数2: （可选）是否离线查询, "true" 表明只查询本地缓存数据库, 默认为在线查询
#    - 返回结果为 JSON 格式，包括有 ip, location 等字段
# 调用示例：
#    lookup_geoip_v6 "~/Downloads/ip_cidr.txt" 1
# 返回示例：
#    {"ip": "2409:8087:c00:12::1:f", "location": "中国山西省 移动"}
lookup_geoip_v4() {
  # 获取存放 IP 的文件名
  file=$1
  # 是否离线查询
  is_offline=$2

  # 逐行读取文件内容, 每行一个 IPv4 地址
  while IFS= read -r ip; do
    # 获取 /24 的地址前缀
    prefix24=$(echo "${ip}" | awk -F"." '{print$1"."$2"."$3".0"}')

    # 查询内存缓存
    resp=$(sp_cache get "$prefix24")
    # 如果内存缓存中未找到, 则调用 sp_geoip 远程查找
    if [ "$resp" == "Key not found" ]; then
      if [ "${is_offline}" != "true" ]; then
        resp=$(sp_geoip "$ip")
        # 将找到的内容存入内存缓存
        sp_cache set "$prefix24" "$resp"
      else
        resp="{}"
      fi
    fi
    # 以 JSON 格式输出结果
    # FIXME: 注意这里 $resp 特意没有添加引号, 因为添加后, jq 命令会报错：
    #  jq: parse error: Invalid string: control characters from U+0000 through U+001F must be escaped at line 1, column 142
    location=$(echo $resp | jq -cMr '.location')
    printf '{"ip": "%s", "location": "%s"}\n' "$ip" "$location"
  done <"$file"
}

# 将缓存内容输出为 GeoIP 缓存数据库格式
dump_cache() {
  keys=$(sp_cache list)
  if [ "$keys" = "" ]; then
    echo "{}"
    return
  fi
  sorted_keys=$(echo "$keys" | sort -V)
  json="{"
  while IFS= read -r key; do
    # shellcheck disable=SC2001
    value=$(sp_cache get "${key}")
    json+="\"${key}\":${value},"
  done <<<"${sorted_keys}"
  # remove the trailing comma
  json=${json%,}
  json+="}"
  echo "$json" | jq -rM
}

# 加载 GeoIP 缓存数据库
load_cache() {
  while IFS= read -r line; do
    # 提取 ip 值(尽量不调用外部命令, 以提高运行效率)
    key=${line#*\"ip\":*\"} # 删除最前面的 {"ip":" 部分
    key=${key%%\"*}         # 删除后面的 ","location":"中国广东 移动"} 部分
    value="$line"
    sp_cache set "$key" "$value"
  done <<<"$(jq -cMr '.[]' $GEOIP_DB)"
}

# 清空 GeoIP 缓存数据库
empty_cache() {
  sp_cache flush
}

load_geoip_from_file() {
  dns_file=$1

  # 确保 TMPDIR 环境变量已设置，如果未设置则使用系统默认临时目录
  TMPDIR=${TMPDIR:-/tmp}
  # 创建临时工作目录
  temp_dir=$(mktemp -d -p "$TMPDIR" geoip.XXXXX)

  echo "==> Working directory: ${temp_dir}"

  ln=0
  # 读取 IPv4 记录
  while IFS= read -r line; do
    ((ln++))
    printf "%8d %s\n" "$ln" "$line"

    # 提取 ip 值, 等同于:
    # ip=$(jq -cMr '.data' <<<"${line}")
    # 尽量不调用外部命令, 以提高运行效率 (x50倍)
    ip=${line#*\"data\":*\"} # 删除最前面的 {"data":" 部分
    ip=${ip%%\"*}            # 删除后面的 ","location":"中国广东 移动"} 部分

    # 同上, 提取 location, 等同于:
    # location=$(jq -cMr '.location' <<<"${line}")
    location=${line#*\"location\":*\"}
    location=${location%%\"*}

    # 获取 /24 的地址前缀, 等同于:
    # prefix24=$(echo "${ip}" | awk -F"." '{print$1"."$2"."$3".0"}')
    prefix24="${ip%.*}.0"

    # 逐行写入临时文件
    printf '{"ip": "%s", "location": "%s"}\n' "$prefix24" "$location" >>"${temp_dir}/geoip_extracted.json"
  done <<<"$(jq -crM '.[].[] | select (.type=="A")' "${dns_file}")"

  # 读取 IPv6 记录
  while IFS= read -r line; do
    ((ln++))
    printf "%8d %s\n" "$ln" "$line"
    # ip=$(jq -cMr '.data' <<<"${line}")
    ip=${line#*\"data\":*\"} # 删除最前面的 {"data":" 部分
    ip=${ip%%\"*}            # 删除后面的 ","location":"中国广东 移动"} 部分
    # location=$(jq -cMr '.location' <<<"${line}")
    location=${line#*\"location\":*\"}
    location=${location%%\"*}

    # 扩展 IPv6 为完整格式
    expanded_addr=$(sipcalc "$ip" | grep "Expanded Address" | awk '{print$4}')
    # 获取 /64 的地址前缀
    prefix64="${expanded_addr:0:20}"

    # 逐行写入临时文件
    printf '{"ip": "%s", "location": "%s"}\n' "$prefix64" "$location" >>"${temp_dir}/geoip_extracted.json"
  done <<<"$(jq -cMr '.[].[] | select (.type=="AAAA")' "${dns_file}")"

  # 读取临时文件, 去重
  uniq "${temp_dir}/geoip_extracted.json" >"${temp_dir}/geoip_sorted.json"

  # 读取去重后的文件, 写入缓存
  while IFS= read -r line; do
    # ip=$(jq -cMr '.ip' <<<"${line}")
    ip=${line#*\"ip\":*\"} # 删除最前面的 {"ip":" 部分
    ip=${ip%%\"*}          # 删除后面的 ","location":"中国广东 移动"} 部分
    # location=$(jq -cMr '.location' <<<"${line}")
    location=${line#*\"location\":*\"}
    location=${location%%\"*}

    # printf '{"ip": "%s", "location": "%s"}\n' "$ip" "$location"

    # 查询内存缓存
    resp=$(sp_cache get "$ip")
    # 如果内存缓存中未找到, 则写入缓存
    if [ "$resp" == "Key not found" ]; then
      resp=$(printf '{"ip": "%s", "location": "%s"}' "$ip" "$location")
      sp_cache set "$ip" "$resp"
    fi
  done <"${temp_dir}/geoip_sorted.json"

  # dump 缓存信息, 保存到文件
  dump_cache >"${GEOIP_DB}"

  # 删除临时工作目录
  \rm -rf "${temp_dir}" >/dev/null
}
