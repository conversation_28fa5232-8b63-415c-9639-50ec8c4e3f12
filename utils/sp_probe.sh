#!/usr/bin/env bash

# shellcheck source=/dev/null

# httpx 探测每秒新建并发数
RATE_LIMIT=255

# 解析 CSV 文件, 提取出 IP 信息, 并合并排序成网段
#
# 说明：
#    - 返回结果为 TEXT 格式
# 调用示例：
#    sp_parse_csv AAAA ~/Downloads/dns-result.csv
# 返回示例：
# IPv4:
#    ====================
#    IPv4 CIDRs:
#    ====================
#    **********/24
#    ***********/24
#    ***********/24
# IPv6:
#    ====================
#    IPv6 Prefixes:
#    ====================
#    2409:8087:04ed:0010:5000:0000:0000:00/120
#    2409:8087:04f0:0010:5000:0000:0000:00/120
#    2409:8087:3007:0011:0000:0000:0000:11/120
sp_parse_csv() {
  # 检查解析结果 IP 类型(A: IPv4, AAAA: IPv6)
  type="$1"
  # 检查是否传入了 CSV 文件路径
  csv_file="$2"

  # 检查文件是否存在
  if [ ! -f "$csv_file" ]; then
    echo "File not found: $csv_file"
    exit 1
  fi

  # 读取第一行
  header=$(head -n 1 "$csv_file")

  # 分割第一行字段
  IFS=',' read -r -a fields <<< "$header"

  # 打印字段编号和内容
  echo "Fields in the CSV file:"
  for i in "${!fields[@]}"; do
    printf "%2d\t%s\n" "$((i+1))" "${fields[$i]}"
  done

  # 提示用户选择字段编号
  read -p "Select a field number: " field_num

  # 验证用户输入的编号
  if ! [[ "$field_num" =~ ^[0-9]+$ ]] || [ "$field_num" -lt 0 ] || [ "$field_num" -ge "${#fields[@]}" ]; then
    echo "Invalid field number: $field_num"
    exit 1
  fi

  if [ "$type" = "A" ]; then
    # 读取字段内容, 并进行去重排序
    ip_blocks=$(awk -F ',' '{print $'"${field_num}"'}' "$csv_file" | \
      awk '{gsub(/\r/,""); print}' | \
      grep -Eo "\b([0-9]{1,3}\.){3}[0-9]{1,3}\b" | \
      sort -t . -k 1,1n -k 2,2n -k 3,3n -k 4,4n | \
      uniq | \
      awk -F '.' '{print$1"."$2"."$3".0/24"}' | \
      uniq )

    echo "===================="
    echo "IPv4 CIDRs:"
    echo "===================="
    printf '%s\n' "${ip_blocks[@]}"
  elif [ "$type" = "AAAA" ]; then
    # 读取字段内容, 并进行去重排序
    ip_addrs=$( cat "$csv_file" | \
      sed 'H;${x;s/\n\r//g;p;}' | \
      awk -F',' '{print$'${field_num}'}' | \
      sed -e 's/\"//g' -e 's/ /\n/g' | \
      sort | \
      uniq | \
      sort | \
      grep --color=never '^24' )

    # 默认按照 /120 前缀汇聚
    arr=()
    for ip in $ip_addrs; do
      expanded_address=$(sipcalc "${ip}" | grep 'Expanded Address' | awk '{print$4}')
      prefix120="${expanded_address:0:37}/120"
      arr[${#arr[@]}]=$prefix120
    done

    # 对数组进行排序去重
    sorted_arr=("$(printf '%s\n' "${arr[@]}" | sort | uniq)")

    echo "===================="
    echo "IPv6 Prefixes:"
    echo "===================="
    printf '%s\n' "${sorted_arr[@]}"
  fi
}

# 探测给定的网段, 是否开放下载服务
#
# 说明：
#    - 返回结果为 TEXT 格式
# 调用示例：
#    sp_probe_ipv4 cdnIP/assets/cmgame.json
# 返回示例：
#    
sp_probe() {
  # 获取探测 IP 类型(A: IPv4, AAAA: IPv6)
  type=$1
  # 获取配置文件路径
  json_file=$2
  result_file="response/ip_probe_result.json"

  # 检查文件是否存在
  if [ ! -f "$json_file" ]; then
    echo "File not found: $json_file"
    exit 1
  fi

  # 提取出探测 url
  url=$(jq -crM '.probe' "${json_file}")

  # 提取前缀
  prefix=$( awk -F[/:] '{print $1}' <<< "$url" )
  # 提取域名
  host=$( awk -F[/:] '{print $4}' <<< "$url" )
  # 提取 URI
  uri=$( sed 's|^.*://[^/]*||g' <<< "$url" )
  # 提取端口
  port=$(echo "$url" | awk -F: '{print $3}' | awk -F/ '{print $1}')
  # 如果提取的端口为空，则使用默认端口
  if [ -z "$port" ]; then
    if [ "$prefix" = "http" ]; then
      port="80"
    else
      port="443"
    fi
  fi

  if [ "$type" = "A" ]; then
    # 提取出待测 IPv4 网段
    ipv4_blocks=$(jq -crM '.networks.ipv4.[]' "${json_file}")
    
    # 筛选可用的 IP 地址
    # 需要提前安装 ProjectDiscovery 工具包中的 httpx 工具
    httpx \
      -x "HEAD" \
      -p "${prefix}:${port}" \
      -random-agent \
      -sni "${host}" \
      -H "Host: ${host}" \
      -path "${uri}" \
      -rl $RATE_LIMIT \
      -o ${result_file} \
      -duc \
      -nfs \
      -timeout 3 \
      -rstr 1048576 \
      -json <<< $ipv4_blocks

    echo "===================="
    echo "Available IPv4s:"
    echo "===================="
    cat ${result_file} | jq -crM '.|select(.status_code==200)|.a[0]' | sort -t . -k 1,1n -k 2,2n -k 3,3n -k 4,4n
  elif [ "$type" = "AAAA" ]; then
    # 提取出待测网段
    ipv6_prefixes=$(jq -crM '.networks.ipv6.[]' ${json_file})

    # 由于 httpx 探测工具无法自动展开 IPv6 网段
    # 这里通过循环按照 /120 前缀生成段内所有明细 IP
    arr=()
    for p in $ipv6_prefixes; do
      for i in {0..255}; do
        arr[${#arr[@]}]=$(printf "[%s%02x]\n" "${p:0:37}" "$i")
      done
    done

    # 筛选可用的 IP 地址
    # 需要提前安装 ProjectDiscovery 工具包中的 httpx 工具
    httpx \
      -x "HEAD" \
      -p ${prefix}:${port} \
      -random-agent \
      -sni "${host}" \
      -H "Host: ${host}" \
      -path "${uri}" \
      -rl $RATE_LIMIT \
      -duc \
      -nfs \
      -timeout 3 \
      -rstr 1048576 \
      -o ${result_file} \
      -json <<< "$(printf "%s\n" "${arr[@]}")"

    echo "===================="
    echo "Available IPv6s:"
    echo "===================="
    cat ${result_file} | jq -crM '.|select(.status_code==200)|.aaaa[0]' | xargs sipcalc | grep 'Compressed address' | awk '{print$4}'
  fi
}

# 探测给定的 IPv6 网段, 是否开放下载服务
#
# 说明：
#    - 返回结果为 TEXT 格式
# 调用示例：
#    sp_probe_ipv6 cdnIP/assets/cmgame.json
# 返回示例：
#    
sp_probe_ipv6() {
  # 获取配置文件路径
  json_file=$1
  result_file="response/ip_probe_result.json"

  # 检查文件是否存在
  if [ ! -f "$json_file" ]; then
    echo "File not found: $json_file"
    exit 1
  fi

  # 提取出探测 url
  url=$(jq -crM '.probe' ${json_file})
  # 提取出待测网段
  ipv6_prefixes=$(jq -crM '.networks.ipv6.[]' ${json_file})

  # 由于 httpx 探测工具无法自动展开 IPv6 网段
  # 这里通过循环按照 /120 前缀生成段内所有明细 IP
  arr=()
  for prefix in $ipv6_prefixes; do
    for i in {0..255}; do
      arr[${#arr[@]}]=$(printf "[%s%02x]\n" "${prefix:0:37}" "$i")
    done
  done

  # 提取前缀
  prefix=$( awk -F[/:] '{print $1}' <<< "$url" )
  # 提取域名
  host=$( awk -F[/:] '{print $4}' <<< "$url" )
  # 提取 URI
  uri=$( sed 's|^.*://[^/]*||g' <<< "$url" )
  # 提取端口
  port=$(echo "$url" | awk -F: '{print $3}' | awk -F/ '{print $1}')
  # 如果提取的端口为空，则使用默认端口
  if [ -z "$port" ]; then
    if [ "$prefix" = "http" ]; then
      port="80"
    else
      port="443"
    fi
  fi

  # 筛选可用的 IP 地址
  # 需要提前安装 ProjectDiscovery 工具包中的 httpx 工具
  httpx \
    -x "GET" \
    -p "${prefix}:${port}" \
    -random-agent \
    -sni "${host}" \
    -H "Host: ${host}" \
    -path "${uri}" \
    -rl $RATE_LIMIT \
    -duc \
    -nfs \
    -timeout 3 \
    -rstr 1048576 \
    -o ${result_file} \
    -json <<< "$(printf "%s\n" "${arr[@]}")"

  echo "===================="
  echo "Available IPs:"
  echo "===================="
  cat ${result_file} | jq -crM '.|select(.status_code==200)|.aaaa[0]' | xargs sipcalc | grep 'Compressed address' | awk '{print$4} | sort'
} 

# sp_parse_csv AAAA /Users/<USER>/Downloads/yun.mcloud.139.com-dns-result-2.csv 
sp_probe AAAA ./assets/cmgame.json